import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1705845182427 implements MigrationInterface {
  name = 'Migrations1705845182427';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."reservation_reservationtype_enum" AS ENUM('RESERVATION', 'UNAVA<PERSON><PERSON>LE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "reservationType" "public"."reservation_reservationtype_enum" NOT NULL DEFAULT 'RESERVATION'`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "operatorName" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "dispatcherPhoneNumber" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "dispatcherEmail" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "clientDetails" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "comments" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "jobStatus" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "jobId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "invoiceNumber" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "managerId"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "managerId" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "dispatcherId"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "dispatcherId" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "operatorId"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "operatorId" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "vehicleId"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "vehicleId" integer`);
    await queryRunner.query(
      `CREATE INDEX "IDX_57201ab5433328be77b9d23ff1" ON "reservation" ("reservationType") `,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_15412881d4828fb6f32f7435f5b" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_7ac433048824708125ad22a4f0b" FOREIGN KEY ("dispatcherId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_468c922064d81dd66a100f4d9c4" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_468c922064d81dd66a100f4d9c4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_7ac433048824708125ad22a4f0b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_15412881d4828fb6f32f7435f5b"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_57201ab5433328be77b9d23ff1"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "vehicleId"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "vehicleId" character varying NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "operatorId"`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "operatorId" character varying NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "dispatcherId"`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "dispatcherId" character varying NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "managerId"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "managerId" character varying NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "invoiceNumber" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "jobId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "jobStatus" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "comments" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "clientDetails" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "dispatcherEmail" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "dispatcherPhoneNumber" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "operatorName" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "reservationType"`);
    await queryRunner.query(`DROP TYPE "public"."reservation_reservationtype_enum"`);
  }
}
