interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const jobEventRelationsConfig: Record<string, RelationConfig> = {
  job: {
    join: `LEFT JOIN "job" ON "job_event"."jobId" = "job"."id"`,
    select: `jsonb_build_object(
      'id', "job"."id",
      'created_at', "job"."created_at",
      'updated_at', "job"."updated_at",
      'deleted_at', "job"."deleted_at",
      'created_by', "job"."created_by",
      'amountOfConcrete', "job"."amountOfConcrete",
      'flowRate', "job"."flowRate",
      'balance', "job"."balance",
      'barbotine', "job"."barbotine",
      'supplyOfTheChemicalSlushie', "job"."supplyOfTheChemicalSlushie",
      'flexiblePipeLength80Mm', "job"."flexiblePipeLength80Mm",
      'flexiblePipeLength90Mm', "job"."flexiblePipeLength90Mm",
      'frontOutriggersSpan', "job"."frontOutriggersSpan",
      'rearOutriggersSpan', "job"."rearOutriggersSpan",
      'rigidPipeLength100Mm', "job"."rigidPipeLength100Mm",
      'rigidPipeLength120Mm', "job"."rigidPipeLength120Mm",
      'extraCementBag', "job"."extraCementBag",
      'units', "job"."units",
      'presenceOfPowerLines', "job"."presenceOfPowerLines",
      'voltage', "job"."voltage",
      'pipeStartingFromBAC', "job"."pipeStartingFromBAC",
      'comments', "job"."comments",
      'terrainStability', "job"."terrainStability",
      'tonnageRestriction', "job"."tonnageRestriction",
      'authorizedWeight', "job"."authorizedWeight",
      'heightRestriction', "job"."heightRestriction",
      'heightLimit', "job"."heightLimit",
      'enlistSecondTechnician', "job"."enlistSecondTechnician",
      'isElectricalRisk', "job"."isElectricalRisk",
      'electricalRiskKey', "job"."electricalRiskKey",
      'electricalRiskComment', "job"."electricalRiskComment",
      'isAccessCompliance', "job"."isAccessCompliance",
      'accessComplianceKey', "job"."accessComplianceKey",
      'accessComplianceComment', "job"."accessComplianceComment",
      'isParkingCompliance', "job"."isParkingCompliance",
      'parkingComplianceKey', "job"."parkingComplianceKey",
      'parkingComplianceComment', "job"."parkingComplianceComment",
      'isTerrainStability', "job"."isTerrainStability",
      'terrainStabilityKey', "job"."terrainStabilityKey",
      'terrainStabilityComment', "job"."terrainStabilityComment",
      'parkingOn', "job"."parkingOn",
      'status', "job"."status",
      'state', "job"."state",
      'cleaning', "job"."cleaning",
      'start', "job"."start",
      'end', "job"."end",
      'progress', "job"."progress",
      'report',
      CASE 
        WHEN "job"."report" IS NOT NULL 
        THEN ("job"."report")::jsonb 
        ELSE NULL
      END,
      'estimatedTotalCost', "job"."estimatedTotalCost"
      ) AS "job"`,
    groupBy: `"job"."id"`,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    const config = jobEventRelationsConfig[relation];
    if (config) {
      joins += config.join + ' ';
      selects += config.select + ', ';
      if (config.groupBy) {
        groupBys += config.groupBy + ', ';
      }
    }
  }

  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, '');

  return { joins, selects, groupBys };
};

const groupBy = `GROUP BY
  "job_event"."id"
`;

export const getJobEventsQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      "job_event".*
      ${selects ? `, ${selects}` : ''}
    FROM "job_event"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}
    `;
};

export const getJobEventCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "job_event"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};
