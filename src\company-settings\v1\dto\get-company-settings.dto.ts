import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional, IsArray, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { SortItem, Expression } from 'src/libs/helpers/CeQuery';

export enum CompanySettingsCategoryTypes {
  id = '"company_settings"."id"',
  companyId = '"company_settings"."companyId"',
}

export class CompanySettingsSortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: CompanySettingsCategoryTypes,
  })
  @IsEnum(CompanySettingsCategoryTypes, { message: 'invalid sort field' })
  field: CompanySettingsCategoryTypes;
}

export class CompanySettingsExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: CompanySettingsCategoryTypes,
  })
  @IsOptional()
  @IsEnum(CompanySettingsCategoryTypes, { message: 'invalid filter category' })
  category?: CompanySettingsCategoryTypes | string | null | undefined;
}
export class GetCompanySettingsDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: CompanySettingsSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CompanySettingsSortItem)
  readonly sortModel: CompanySettingsSortItem[];

  @ApiProperty({ required: true, isArray: true, type: CompanySettingsExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CompanySettingsExpression)
  readonly expressions: CompanySettingsExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
