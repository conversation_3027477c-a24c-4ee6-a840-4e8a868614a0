import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1722611499445 implements MigrationInterface {
  name = 'Migrations1722611499445';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "bulk_invoice_log" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "dateFrom" TIMESTAMP WITH TIME ZONE, "dateTo" TIMESTAMP WITH TIME ZONE, "reservationIds" integer array NOT NULL, "operatorManagerId" integer, "dispatcherManagerId" integer, "total" integer NOT NULL, CONSTRAINT "PK_0bddbc2bc6eb059c25f26fb7fa7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dad3512819f9062d1379016711" ON "bulk_invoice_log" ("operatorManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_423f522201353c94c687a4211f" ON "bulk_invoice_log" ("dispatcherManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4ac13693574655eefccfe32334" ON "bulk_invoice_log" ("operatorManagerId", "dispatcherManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c52ab39d2e388db5d03571b739" ON "bulk_invoice_log" ("dateFrom", "dateTo") `,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD CONSTRAINT "FK_dad3512819f9062d1379016711b" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD CONSTRAINT "FK_423f522201353c94c687a4211fb" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" DROP CONSTRAINT "FK_423f522201353c94c687a4211fb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" DROP CONSTRAINT "FK_dad3512819f9062d1379016711b"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_c52ab39d2e388db5d03571b739"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4ac13693574655eefccfe32334"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_423f522201353c94c687a4211f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dad3512819f9062d1379016711"`);
    await queryRunner.query(`DROP TABLE "bulk_invoice_log"`);
  }
}
