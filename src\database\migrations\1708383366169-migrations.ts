import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708383366169 implements MigrationInterface {
  name = 'Migrations1708383366169';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."job_state_enum" AS ENUM('RUNNING', 'SUCCESS', 'FAILURE', 'CANCELLED', 'WAITING')`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD "state" "public"."job_state_enum" NOT NULL DEFAULT 'WAITING'`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."job_status_enum" RENAME TO "job_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."job_status_enum" AS ENUM('NOT_STARTED', 'DRIVING_TO_SITE', 'SECURITY_VALIDATION', 'SETUP', 'PUMPING', 'ADMIN_CLEANUP', 'SIGNATURE', 'LEAVE_SITE', 'COMPLETE', 'CANCELLED')`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" TYPE "public"."job_status_enum" USING "status"::"text"::"public"."job_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "status" SET DEFAULT 'NOT_STARTED'`);
    await queryRunner.query(`DROP TYPE "public"."job_status_enum_old"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "start"`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "start" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "end"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "end" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "progress" SET DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "progress" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "end"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "end" TIMESTAMP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "start"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "start" TIMESTAMP NOT NULL`);
    await queryRunner.query(
      `CREATE TYPE "public"."job_status_enum_old" AS ENUM('RUNNING', 'SUCCESS', 'FAILURE', 'CANCELLED', 'WAITING')`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" TYPE "public"."job_status_enum_old" USING "status"::"text"::"public"."job_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "status" SET DEFAULT 'WAITING'`);
    await queryRunner.query(`DROP TYPE "public"."job_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."job_status_enum_old" RENAME TO "job_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "state"`);
    await queryRunner.query(`DROP TYPE "public"."job_state_enum"`);
  }
}
