import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1717139521306 implements MigrationInterface {
  name = 'Migrations1717139521306';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_108e9b44b89a94584c0d030bf7" ON "reservation" ("orderNumber") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_15412881d4828fb6f32f7435f5" ON "reservation" ("managerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_eee33a7d3ac2fafb669814ef88" ON "reservation" ("operatorId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7ac433048824708125ad22a4f0" ON "reservation" ("dispatcherId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_468c922064d81dd66a100f4d9c" ON "reservation" ("vehicleId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5ed0dc8f08cb6fa56feab89043" ON "reservation" ("jobId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bcd64df2c31dd5633f419c088e" ON "reservation" ("dateFrom", "dateTo") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_bcd64df2c31dd5633f419c088e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5ed0dc8f08cb6fa56feab89043"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_468c922064d81dd66a100f4d9c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7ac433048824708125ad22a4f0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_eee33a7d3ac2fafb669814ef88"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_15412881d4828fb6f32f7435f5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_108e9b44b89a94584c0d030bf7"`);
  }
}
