// common.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional, IsString, Min } from 'class-validator';

export class CommonQueryParams {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  readonly limit?: number = Number.MAX_SAFE_INTEGER;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  readonly offset?: number = 0;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  readonly sortBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  readonly sortDir?: 'ASC' | 'DESC' = 'ASC';

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  readonly searchText?: string = '';
}

export class GetUsersDto {}
