import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Type } from 'class-transformer';
import { SortItem, Expression } from 'src/libs/helpers/CeQuery';

export enum ContainerCategoryTypes {
  id = '"container_pricelists"."id"',
  companyId = '"container_pricelists"."companyId"',
  isPublic = '"container_pricelists"."isPublic"',
  isDefault = '"container_pricelists"."isDefault"',
  partnerId = '"container_pricelists"."partnerId"',
}

export class ContainerSortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: ContainerCategoryTypes,
  })
  @IsEnum(ContainerCategoryTypes, { message: 'invalid sort field' })
  field: ContainerCategoryTypes;
}

export class ContainerExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: ContainerCategoryTypes,
  })
  @IsOptional()
  @IsEnum(ContainerCategoryTypes, { message: 'invalid filter category' })
  category?: ContainerCategoryTypes | string | null | undefined;
}
export class GetContainersDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: ContainerSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContainerSortItem)
  readonly sortModel: ContainerSortItem[];

  @ApiProperty({ required: true, isArray: true, type: ContainerExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContainerExpression)
  readonly expressions: ContainerExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
