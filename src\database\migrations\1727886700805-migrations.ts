import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1727886700805 implements MigrationInterface {
  name = 'Migrations1727886700805';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "companyName"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "companyAddress"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "vatNumber"`);
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "vatNumber"`);
    await queryRunner.query(`ALTER TABLE "company" ADD "vatNumber" character varying`);
    await queryRunner.query(
      `ALTER TABLE "job_event" DROP CONSTRAINT "FK_deae36b9856509b82899b771111"`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "amountOfConcrete" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "job_event" ADD CONSTRAINT "FK_deae36b9856509b82899b771112" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "vatNumber"`);
    await queryRunner.query(`ALTER TABLE "company" ADD "vatNumber" integer`);
    await queryRunner.query(`ALTER TABLE "user" ADD "vatNumber" integer`);
    await queryRunner.query(`ALTER TABLE "user" ADD "companyAddress" character varying`);
    await queryRunner.query(`ALTER TABLE "user" ADD "companyName" character varying`);
    await queryRunner.query(
      `ALTER TABLE "job_event" DROP CONSTRAINT "FK_deae36b9856509b82899b771112"`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "amountOfConcrete" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "job_event" ADD CONSTRAINT "FK_deae36b9856509b82899b771111" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
