import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1717262815405 implements MigrationInterface {
  name = 'Migrations1717262815405';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_ef05556c304ed63583fc0881f6" ON "unavailable_period" ("from") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9c0cfc34d4336f986190f903b7" ON "unavailable_period" ("to") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5d4d3b23d15b17576e6085aa22" ON "vehicle" ("boomSize") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_5d4d3b23d15b17576e6085aa22"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9c0cfc34d4336f986190f903b7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ef05556c304ed63583fc0881f6"`);
  }
}
