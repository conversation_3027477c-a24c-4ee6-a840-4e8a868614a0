import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739543615767 implements MigrationInterface {
  name = 'Migrations1739543615767';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" ADD "city" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "city"`);
  }
}
