import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1723657100887 implements MigrationInterface {
  name = 'Migrations1723657100887';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" ADD "totalSum" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "totalSum"`);
  }
}
