import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray, ValidateNested, IsDateString } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { processNumber } from 'src/common/helpers/processNumbers';

class WorkSchedule {
  @ApiProperty()
  @IsString()
  day: string;

  @ApiProperty()
  @IsString()
  startTime: string;

  @ApiProperty()
  @IsString()
  endTime: string;
}

class Cancellation {
  @ApiProperty()
  @IsString()
  cancellationWindow: string;

  @ApiProperty()
  @IsString()
  reducedCancellationWindow: string;

  @ApiProperty()
  @IsString()
  minTimeBetweenJobs: string;
}

class Holiday {
  @ApiProperty()
  @IsDateString()
  date: string;

  @ApiProperty()
  @IsString()
  reason: string;
}

export class CreateCompanySettingsDto {
  @ApiProperty()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  companyId: number;

  @ApiProperty({ type: [WorkSchedule] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => WorkSchedule)
  workSchedules?: WorkSchedule[];

  @ApiProperty({ type: [Holiday] })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Holiday)
  holidays?: Holiday[];

  @ApiProperty({ type: Cancellation })
  @IsOptional()
  @ValidateNested()
  @Type(() => Cancellation)
  cancellation?: Cancellation;
}
