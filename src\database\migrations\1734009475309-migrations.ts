import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1734009475309 implements MigrationInterface {
  name = 'Migrations1734009475309';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" RENAME COLUMN "total" TO "total_old"`);
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD "total" double precision NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `UPDATE "bulk_invoice_log" SET "total" = "total_old"::double precision`,
    );
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "total_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" RENAME COLUMN "total" TO "total_temp"`);

    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD "total" integer NOT NULL DEFAULT 0`,
    );

    await queryRunner.query(`UPDATE "bulk_invoice_log" SET "total" = ROUND("total_temp")`);

    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "total_temp"`);
  }
}
