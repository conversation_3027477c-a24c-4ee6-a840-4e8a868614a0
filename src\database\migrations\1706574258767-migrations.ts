import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706574258767 implements MigrationInterface {
  name = 'Migrations1706574258767';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "type"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "name" character varying NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength100Mm" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength120Mm" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "frontPressureOnOutrigger" integer NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rearPressureOnOutrigger" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "bacExitReverse" boolean NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "bacExitReverse"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rearPressureOnOutrigger"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "frontPressureOnOutrigger"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength120Mm"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength100Mm"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "name"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "type" integer NOT NULL`);
  }
}
