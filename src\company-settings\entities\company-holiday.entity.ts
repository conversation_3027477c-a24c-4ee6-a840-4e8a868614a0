import { <PERSON><PERSON>ty, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, Index } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { CompanySettings } from './company-settings.entity';

@Entity()
export class CompanyHoliday extends BaseEntity {
  @Column({ type: 'date' })
  date: Date;

  @Column()
  reason: string;

  @Column({ nullable: true })
  @Index()
  companySettingsId: number;

  @ManyToOne(() => CompanySettings, entity => entity.holidays, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'companySettingsId' })
  companySettings: CompanySettings;
}
