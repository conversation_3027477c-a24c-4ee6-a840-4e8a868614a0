import {
  BadRequestException,
  ConflictException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { CreateReservationDto } from './dto/create-reservation.dto';
import { UpdateReservationDto } from './dto/update-reservation.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Reservation } from '../entities/reservation.entity';
import { DataSource, In, MoreThanOrEqual, Repository } from 'typeorm';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { JobService } from 'src/job/v1/job.service';
import { GetReservationDto } from './dto/get-reservation.dto';
import { UsersService } from 'src/users/v1/users.service';
import { VehicleService } from 'src/vehicle/v1/vehicle.service';
import { JobStatus } from 'src/common/constants/job.enum';
import { FilesService } from 'src/s3/files.service';
import { JOBS, QUEUES } from 'src/libs/bullmq/constants';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { getReservationsForInvoiceQuery } from '../queries/getReservationsForInvoice';
import { PdfService } from 'src/pdf-report/pdf.service';
import { MailerService } from 'src/mailer/mailer.service';
import { InvoiceReservationDto } from './dto/invoice-reservation.dto';
import { getSingleReservationForInvoiceQuery } from '../queries/getSingleReservationForInvoice';
import { SingleInvoiceDto } from './dto/single-invoice.dto';
import { InvoiceLogService } from 'src/invoice-log/v1/invoice-log.service';
import { CreateInvoiceLogDto } from 'src/invoice-log/v1/dto/create-invoice-logs.dto';
import {
  GenerateBulkReservationReport,
  GenerateSingleReservationReport,
} from 'src/pdf-report/interface/generate-reservation-report.interface';
import {
  HandleFileUploadsParams,
  ReservationDto,
} from './interface/handle-file-upload-params.interface';
import { CompanyService } from 'src/company/v1/company.service';
import { Company } from 'src/company/entities/company.entity';
import { WorkScheduleSettings } from 'src/company-settings/entities/work-schedule-settings.entity';
import { CompanySettingsService } from 'src/company-settings/v1/company-settings.service';
import { checkDateRangeForHolidays, formatHolidayErrorMessage } from '../helpers/holiday-validation.helper';
import { CompanySettingsCategoryTypes } from 'src/company-settings/v1/dto/get-company-settings.dto';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { InvoiceStatus } from 'src/invoice-log/enums/invoice-status.enum';
import { UpdateInvoiceLogPayload } from 'src/invoice-log/v1/interface/update-invoice-log.interface';
import {
  Expression,
  QueryOperator,
  SortDirections,
  addExpressions,
  ConditionType,
  getWhereQuery,
  getQuerySort,
  getQueryLimit,
} from 'src/libs/helpers/CeQuery';
import { getReservationCountQuery, getReservationRawQuery } from '../queries/getReservationQuery';
import { ContractedStatus } from 'src/common/constants/user.enum';
import { GetContractDto, ContractCategoryTypes } from 'src/contract/v1/dto/get-contract.dto';
import {
  calculateCancellationCost,
  calculateTotalCost,
} from 'src/common/helpers/reservation-cost-calculator';
import {
  DailyContractInvoice,
  GenerateInvoiceForManyReservation,
  ReservationInvoice,
} from './interface/reservation-invoice.interface';
import { generateInvoiceNumber } from 'src/common/helpers/generate-invoice';
import { VehicleResponse } from 'src/vehicle/v1/interfaces/get-vehicles.interface';
import { ContractService } from 'src/contract/v1/contract.service';
import { JobWithSignature } from 'src/job/v1/interfaces/jobWithSignature';
import { ReservationCost } from './interface/reservation-cost.interface';
import { mapJobToReservationCost } from '../mappers/reservation-cost.mapper';
import { Pricelist } from 'src/pricelist/entities/pricelist.entity';
import { Role } from 'src/common/constants/roles.enum';
import { InvoiceType } from 'src/invoice-log/enums/invoice-type.enum';
import { InvoiceLog } from 'src/invoice-log/entities/invoice-log.entity';
import { ReservationType } from '../entities/reservation.entity';
import { CreateJobDto } from 'src/job/v1/dto/create-job.dto';
import { DuplicateReservationDto } from './dto/duplicate-reservation.dto';

@Injectable()
export class ReservationService {
  private readonly logger = new Logger(ReservationService.name);

  constructor(
    @InjectRepository(Reservation)
    private reservationRepository: Repository<Reservation>,
    @InjectRepository(Pricelist)
    private pricelistRepository: Repository<Pricelist>,
    @Inject(forwardRef(() => JobService))
    private jobService: JobService,
    @Inject(forwardRef(() => UsersService))
    private userService: UsersService,
    @Inject(forwardRef(() => VehicleService))
    private vehicleService: VehicleService,
    private filesService: FilesService,
    @InjectQueue(QUEUES.RESERVATION_INVOICE) private invoiceQueue: Queue,
    @InjectQueue(QUEUES.RESERVATION_BULK_INVOICE) private bulkInvoiceQueue: Queue,
    private pdfService: PdfService,
    private mailerService: MailerService,
    @Inject(forwardRef(() => InvoiceLogService))
    private invoiceLogService: InvoiceLogService,
    @Inject(forwardRef(() => CompanyService))
    private companyService: CompanyService,
    @Inject(forwardRef(() => CompanySettingsService))
    private companySettingsService: CompanySettingsService,
    private contractService: ContractService,
    private dataSource: DataSource,
  ) {}

  async create(
    createReservationDto: CreateReservationDto,
    creatingUser: SerializedUser,
  ): Promise<Reservation> {
    const creatingUserId = creatingUser.sub;
    const creatingCompanyId = creatingUser.companyId;
    this.logger.log({
      method: 'createReservation',
      creatingUser: creatingUserId,
    });

    const { job, pricelistId, ...createReservation } = createReservationDto;

    const createdJob = await this.jobService.create(job, creatingUserId);
    if (!createdJob) {
      throw new HttpException('Could not create job.', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    const reservation = this.reservationRepository.create({
      ...createReservation,
      jobId: createdJob.id,
      created_by: creatingUserId,
    });

    if (createReservationDto.dateFrom && createReservationDto.dateTo) {
      await this.validateReservationDatesAgainstHolidays(
        new Date(createReservationDto.dateFrom),
        new Date(createReservationDto.dateTo),
        creatingCompanyId,
      );
    }

    if (createReservationDto.managerId) {
      reservation.manager = await this.userService.findOneById(createReservationDto.managerId);
    }
    if (createReservationDto.operatorId) {
      reservation.operator = await this.userService.findOneById(createReservationDto.operatorId);
      const existingReservations = await this.findMany(
        {
          expressions: [
            {
              category: '"reservation"."operatorId"',
              operator: '=' as QueryOperator,
              value: createReservationDto.operatorId,
              conditionType: 'AND',
            },
            {
              category: '"reservation"."dateFrom"',
              operator: '<=' as QueryOperator,
              value: new Date(createReservationDto.dateTo).toISOString(),
              conditionType: 'AND',
            },
            {
              category: '"reservation"."dateTo"',
              operator: '>=' as QueryOperator,
              value: new Date(createReservationDto.dateFrom).toISOString(),
              conditionType: 'AND',
            },
            {
              category: '"job"."status"',
              operator: 'not_in' as QueryOperator,
              value: [JobStatus.CANCELLED, JobStatus.COMPLETE],
              conditionType: 'AND',
            },
          ],
          limit: 1,
          sortModel: [],
        },
        false,
        creatingUser,
      );

      if (existingReservations.data.length > 0) {
        throw new BadRequestException(
          'Operator is already assigned to another reservation during this time period!',
        );
      }
    } else {
      throw new BadRequestException('Assigned operator is required!');
    }
    if (
      createReservationDto.dispatcherId ||
      [Role.DISPATCHER, Role.DISPATCHER_MANAGER].includes(creatingUser.roleId)
    ) {
      reservation.dispatcher = await this.userService.findOneById(
        createReservationDto.dispatcherId ?? creatingUser.sub,
      );
    }

    if (createReservationDto.vehicleId) {
      const vehicle = await this.vehicleService.findOne(
        createReservationDto.vehicleId,
        creatingUser,
      );

      reservation.vehicle = vehicle;
      reservation.vehicleUniqueId = vehicle.uniqueIdentificationNumber;
      if (vehicle.assignedPricelist) {
        reservation.pricelist = vehicle.assignedPricelist;
      }
      if (pricelistId) {
        reservation.pricelistId = pricelistId;
      }
      if (reservation.dispatcher) {
        const contractQuery: GetContractDto = {
          expressions: [
            {
              category: ContractCategoryTypes.dispatcherCompanyId,
              operator: '=' as QueryOperator,
              value: creatingCompanyId,
              conditionType: 'AND',
            },
            {
              category: ContractCategoryTypes.vehicleId,
              operator: '=' as QueryOperator,
              value: reservation.vehicle.id,
              conditionType: 'AND',
            },
            {
              category: ContractCategoryTypes.status,
              operator: '=' as QueryOperator,
              value: ContractedStatus.APPROVED,
              conditionType: 'AND',
            },
            {
              category: ContractCategoryTypes.startDate,
              operator: '<=' as QueryOperator,
              value: new Date(createReservationDto.dateFrom).toISOString(),
              conditionType: 'AND',
            },
            {
              category: ContractCategoryTypes.endDate,
              operator: '>=' as QueryOperator,
              value: new Date(createReservationDto.dateTo).toISOString(),
              conditionType: 'AND',
            },
          ],
          limit: 1,
          offset: 0,
          sortModel: [],
          relations: ['pricelist', 'suspensionPeriods'],
        };
        const {
          data: [contract],
        } = await this.contractService.findMany(contractQuery, creatingUser);

        // Check if the contract is in a suspended period.
        if (contract && contract.suspensionPeriods && contract.suspensionPeriods.length > 0) {
          // Convert the reservation dates.
          const reservationStart = new Date(createReservationDto.dateFrom);
          const reservationEnd = new Date(createReservationDto.dateTo);

          // Check for any overlap between the reservation period and any suspension period.
          const isSuspended = contract.suspensionPeriods.some(sp => {
            const spStart = new Date(sp.suspensionStart);
            const spEnd = new Date(sp.suspensionEnd);
            // Overlap exists if the reservation starts before the suspension ends
            // and ends after the suspension starts.
            return reservationStart < spEnd && reservationEnd > spStart;
          });

          if (isSuspended) {
            throw new BadRequestException('Contract is suspended during the selected period.');
          }
          reservation.pricelist = contract.pricelist;
        }
      }
      if (!reservation.pricelist) {
        if (pricelistId) {
          reservation.pricelistId = pricelistId;
        } else {
          throw new BadRequestException('Selected vehicle has no pricelist!');
        }
      }

      // Load the pricelist if it's not already loaded but pricelistId exists
      if (!reservation.pricelist && reservation.pricelistId) {
        reservation.pricelist = await this.pricelistRepository.findOne({
          where: { id: reservation.pricelistId },
        });
        if (!reservation.pricelist) {
          throw new BadRequestException('Pricelist not found!');
        }
      }

      // Check if the it's a contracted vehicle before checking the availability.
      if (!reservation.pricelist.isContract) {
        const isVehicleAvailable = await this.vehicleService.checkVehicleAvailability({
          id: vehicle.id,
          dateFrom: createReservationDto.dateFrom,
          dateTo: createReservationDto.dateTo,
          currentReservationId: null,
        });

        if (!isVehicleAvailable) {
          throw new BadRequestException('Vehicle is unavailable for reservation on this date!');
        }
      }
    }
    reservation.orderNumber = await this.validateOrderNumber(createReservationDto.orderNumber);

    await this.handleFileUploads({
      reservationDto: createReservationDto,
      userId: creatingUserId,
      existingReservation: reservation,
      jobId: createdJob.id,
    });

    await this.reservationRepository.save(reservation);
    this.logger.log({
      method: 'createReservation',
      reservationId: reservation.id,
      creatingUser: creatingUserId,
    });

    const trackLink = `${process.env.CONCRETE_EASY_APP_UI_URL}/track-and-trace?orderNumber=${
      reservation.orderNumber
    }&clientEmail=${encodeURIComponent(reservation.clientDetails.email)}`;

    const formatDate = (date: Date) => {
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        day: '2-digit',
        month: 'long',
        hour: '2-digit',
        minute: '2-digit',
      });
    };

    await this.mailerService.sendMail({
      to: reservation.clientDetails.email,
      subject: 'Concreteasy - New Reservation',
      template: 'track-and-trace',
      context: {
        clientDetails: { name: reservation.clientDetails.name },
        orderNumber: reservation.orderNumber,
        trackLink: trackLink,
        dateFrom: reservation.dateFrom ? formatDate(new Date(reservation.dateFrom)) : null,
        dateTo: reservation.dateTo ? formatDate(new Date(reservation.dateTo)) : null,
        siteAddress: reservation.siteAddress,
        amountOfConcrete: createdJob.amountOfConcrete ? `${createdJob.amountOfConcrete} m³` : null,
        pumpType: reservation.vehicleUniqueId,
      },
    });

    return reservation;
  }

  async createCancelled(
    createReservationDto: CreateReservationDto,
    pricelist: Pricelist,
    creatingUser: SerializedUser,
  ): Promise<Reservation> {
    this.logger.log({
      method: 'createCancelled',
      creatingUser: creatingUser.sub,
    });
    const createdJob = await this.jobService.create(createReservationDto.job, creatingUser.sub);
    if (!createdJob) {
      throw new HttpException('Could not create job.', HttpStatus.INTERNAL_SERVER_ERROR);
    }
    const createdJobWithSignature: JobWithSignature = createdJob;

    const newReservation = this.reservationRepository.create({
      ...createReservationDto,
      jobId: createdJob.id,
      job: createdJob,
      created_by: creatingUser.sub,
      pricelist,
    });

    const contractCancellationSettings = {
      cancellationWindow: pricelist.contractLateCancellationPeriod,
      reducedCancellationWindow: pricelist.contractCancellationPeriod,
    };

    calculateCancellationCost(
      newReservation,
      createdJobWithSignature,
      contractCancellationSettings,
    );
    newReservation.totalSum = createdJobWithSignature.totalCost;

    const savedReservation = await this.reservationRepository.save(newReservation);

    return savedReservation;
  }

  async findMany(
    body: GetReservationDto,
    shouldReturnTotalCount = true,
    fetchingUser?: SerializedUser,
  ): Promise<EntityWithCountDto<Reservation>> {
    const userId = fetchingUser?.sub;
    const companyId = fetchingUser?.companyId;

    this.logger.log({ method: 'findManyReservations', userId });

    const {
      expressions,
      sortModel = [],
      offset,
      limit,
      dateFrom,
      dateTo,
      invoiced,
      vehicleIds,
      operatorIds,
      job,
      dispatcherCompanyId,
    } = body;

    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    if (companyId) {
      const orConditions: Expression[] = [
        {
          category: '"manager"."companyId"',
          operator: '=' as QueryOperator,
          value: companyId,
        },
        {
          category: '"dispatcher"."companyId"',
          operator: '=' as QueryOperator,
          value: companyId,
          conditionType: 'OR',
        },
      ];

      expressionsToAdd.push({
        expressions: orConditions,
        conditionType: '',
      });
    }

    if (dateFrom && dateTo) {
      expressionsToAdd.push({
        category: '"reservation"."dateFrom"',
        operator: 'between' as QueryOperator,
        value: [dateFrom.toISOString(), dateTo.toISOString()],
        conditionType: 'AND',
      });
    } else if (dateFrom) {
      expressionsToAdd.push({
        category: '"reservation"."dateFrom"',
        operator: '>=' as QueryOperator,
        value: dateFrom.toISOString(),
        conditionType: 'AND',
      });
    } else if (dateTo) {
      expressionsToAdd.push({
        category: '"reservation"."dateFrom"',
        operator: '<=' as QueryOperator,
        value: dateTo.toISOString(),
        conditionType: 'AND',
      });
    }

    if (vehicleIds?.length) {
      expressionsToAdd.push({
        category: '"reservation"."vehicleId"',
        operator: 'in' as QueryOperator,
        value: vehicleIds,
        conditionType: 'AND',
      });
    }

    if (job?.state?.length) {
      expressionsToAdd.push({
        category: '"job"."state"',
        operator: 'in',
        value: job.state,
        conditionType: 'AND',
      });
    }

    // Add other filters
    if (dispatcherCompanyId) {
      expressionsToAdd.push({
        category: '"dispatcher"."companyId"',
        operator: '=' as QueryOperator,
        value: dispatcherCompanyId,
        conditionType: 'AND',
      });
    }

    if (invoiced !== undefined) {
      expressionsToAdd.push({
        category: '"reservation"."invoiced"',
        operator: '=' as QueryOperator,
        value: invoiced,
        conditionType: 'AND',
      });
    }

    if (operatorIds?.length) {
      expressionsToAdd.push({
        category: '"reservation"."operatorId"',
        operator: 'in' as QueryOperator,
        value: operatorIds,
        conditionType: 'AND',
      });
    }

    if (!sortModel.length) {
      sortModel.push({ field: '"reservation"."dateFrom"', sort: SortDirections.ASC });
    }

    const expressionsWithOtherData = addExpressions(
      expressionsToAdd,
      expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);
    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const reservationSQL = getReservationRawQuery(whereQuery.query, querySort, queryLimit);

    try {
      let totalCount = 0;

      const reservations = await queryRunner.manager.query(reservationSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getReservationCountQuery(whereQuery.query),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      return new EntityWithCountDto<Reservation>(Reservation, reservations, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser?: SerializedUser): Promise<Reservation | null> {
    const userId = fetchingUser?.sub;
    this.logger.log({ method: 'findOneById', userId });

    const { data: reservations } = await this.findMany(
      {
        expressions: [{ category: '"reservation"."id"', operator: '=', value: id }],
        limit: 1,
        sortModel: [],
      },
      false,
      fetchingUser,
    );

    if (reservations.length == 0) {
      throw new NotFoundException('Reservation was not found.');
    }

    this.logger.log({
      method: 'findOneById',
      id,
      reservationId: reservations[0].id,
    });
    return reservations[0];
  }

  async getJobProgress(
    id: number,
    fetchingUser?: SerializedUser,
  ): Promise<{ progress: number; status: JobStatus; state: string }> {
    const userId = fetchingUser?.sub;
    this.logger.log({ method: 'getJobProgress', reservationId: id, userId });

    const { data: reservations } = await this.findMany(
      {
        expressions: [{ category: '"reservation"."id"', operator: '=', value: id }],
        limit: 1,
        sortModel: [],
      },
      false,
      fetchingUser,
    );

    if (reservations.length === 0) {
      throw new NotFoundException('Reservation was not found.');
    }

    const reservation = reservations[0];
    const job = reservation.job;

    this.logger.log({
      method: 'getJobProgress',
      reservationId: id,
      progress: job.progress,
      status: job.status,
      state: job.state,
    });

    return {
      progress: job.progress || 0,
      status: job.status,
      state: job.state,
    };
  }

  async update(
    id: number,
    updateReservationDto: UpdateReservationDto,
    updatingUser: SerializedUser,
  ): Promise<Reservation> {
    const updatingUserId = updatingUser?.sub;
    const companyId = updatingUser?.companyId;
    this.logger.log({
      method: 'updateReservation',
      updatingUser: updatingUserId,
    });

    const { jobId, job, pricelistId, ...updateReservation } = updateReservationDto;
    const reservation = await this.findOneById(id, updatingUser);

    if (
      reservation.manager.companyId != companyId &&
      reservation.dispatcher.companyId != companyId
    ) {
      throw new HttpException('Reservation not found!', HttpStatus.NOT_FOUND);
    }

    if (updateReservationDto.dateFrom || updateReservationDto.dateTo) {
      const dateFrom = updateReservationDto.dateFrom
        ? new Date(updateReservationDto.dateFrom)
        : reservation.dateFrom;
      const dateTo = updateReservationDto.dateTo
        ? new Date(updateReservationDto.dateTo)
        : reservation.dateTo;

      if (dateFrom && dateTo) {
        await this.validateReservationDatesAgainstHolidays(dateFrom, dateTo, companyId);
      }
    }

    if (updateReservationDto.managerId) {
      const manager = await this.userService.findOneById(updateReservationDto.managerId);
      if (manager) {
        reservation.manager = manager;
      } else {
        reservation.manager = null;
      }
    }

    if (updateReservationDto.operatorId) {
      const operator = await this.userService.findOneById(updateReservationDto.operatorId);
      if (operator) {
        const existingReservations = await this.findMany(
          {
            expressions: [
              {
                category: '"reservation"."operatorId"',
                operator: '=' as QueryOperator,
                value: updateReservationDto.operatorId,
                conditionType: 'AND',
              },
              {
                category: '"reservation"."dateFrom"',
                operator: '<=' as QueryOperator,
                value: new Date(updateReservationDto.dateTo ?? reservation.dateTo).toISOString(),
                conditionType: 'AND',
              },
              {
                category: '"reservation"."dateTo"',
                operator: '>=' as QueryOperator,
                value: new Date(
                  updateReservationDto.dateFrom ?? reservation.dateFrom,
                ).toISOString(),
                conditionType: 'AND',
              },
              {
                category: '"reservation"."id"',
                operator: '!=' as QueryOperator,
                value: id,
                conditionType: 'AND',
              },
              {
                category: '"job"."status"',
                operator: 'not_in' as QueryOperator,
                value: [JobStatus.CANCELLED, JobStatus.COMPLETE],
                conditionType: 'AND',
              },
            ],
            limit: 1,
            sortModel: [],
          },
          false,
          updatingUser,
        );

        if (existingReservations.data.length > 0) {
          throw new BadRequestException(
            'Operator is already assigned to another reservation during this time period!',
          );
        }

        reservation.operator = operator;
      } else {
        reservation.operator = null;
      }
    } else {
      throw new BadRequestException('Assigned operator is required!');
    }

    if (updateReservationDto.dispatcherId) {
      const dispatcher = await this.userService.findOneById(updateReservationDto.dispatcherId);
      if (dispatcher) {
        reservation.dispatcher = dispatcher;
      } else {
        reservation.dispatcher = null;
      }
    }
    let vehicle: VehicleResponse | null = null;
    if (updateReservationDto.vehicleId) {
      vehicle = await this.vehicleService.findOne(updateReservationDto.vehicleId, updatingUser);
      const isVehicleAvailable = await this.vehicleService.checkVehicleAvailability({
        id: vehicle.id,
        dateFrom: updateReservationDto.dateFrom ?? reservation.dateFrom,
        dateTo: updateReservationDto.dateTo ?? reservation.dateTo,
        currentReservationId: reservation.id,
      });
      if (!isVehicleAvailable) {
        throw new BadRequestException('Vehicle is unavailable for reservation on this date!');
      }
      reservation.vehicle = vehicle;
      reservation.vehicleUniqueId = vehicle.uniqueIdentificationNumber;
    }

    if (pricelistId) {
      reservation.pricelistId = pricelistId;
    } else if (vehicle?.assignedPricelist) {
      reservation.pricelist = vehicle.assignedPricelist;
    }
    if (pricelistId && reservation.dispatcher) {
      const contractQuery: GetContractDto = {
        expressions: [
          {
            category: ContractCategoryTypes.dispatcherCompanyId,
            operator: '=' as QueryOperator,
            value: companyId,
            conditionType: 'AND',
          },
          {
            category: ContractCategoryTypes.status,
            operator: '=' as QueryOperator,
            value: ContractedStatus.APPROVED,
            conditionType: 'AND',
          },
        ],
        limit: 1,
        offset: 0,
        sortModel: [],
        relations: [],
      };
      const {
        data: [contract],
      } = await this.contractService.findMany(contractQuery, updatingUser);
      if (contract?.pricelist) {
        reservation.pricelist = contract.pricelist;
      }
    }
    if (!reservation.pricelist) {
      throw new BadRequestException('Selected vehicle has no pricelist!');
    }

    if (job && reservation.jobId) {
      const jobObject = typeof job === 'string' ? JSON.parse(job) : job;
      const updatedJob = await this.jobService.update(reservation.jobId, jobObject, updatingUserId);
      if (!updatedJob) {
        throw new HttpException('Could not update job.', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }

    await this.handleFileUploads({
      reservationDto: updateReservationDto,
      userId: updatingUserId,
      existingReservation: reservation,
      jobId: jobId,
    });

    Object.assign(reservation, updateReservation);

    const updatedReservation = await this.reservationRepository.save(reservation);

    this.logger.log({
      method: 'updateReservation',
      reservationId: reservation.id,
      updatingUser: updatingUserId,
    });

    return updatedReservation;
  }

  async remove(id: number, removingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removeReservation', id });

    const { data } = await this.findMany(
      {
        limit: 1,
        expressions: [
          {
            category: '"reservation"."id"',
            operator: '=',
            value: id,
          },
        ],
      },
      false,
      removingUser,
    );

    const reservation = data[0];

    if (!reservation) {
      throw new HttpException('Reservation not found!', HttpStatus.NOT_FOUND);
    }

    const isInProgress = reservation.job.progress > 0 && reservation.job.progress < 100;
    const isWithinReservationPeriod =
      new Date() >= new Date(reservation.dateFrom) && new Date() <= new Date(reservation.dateTo);

    if (isInProgress && isWithinReservationPeriod) {
      throw new HttpException(
        'Reservation cannot be deleted while it is active.',
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.reservationRepository.delete(id);
    await this.jobService.remove(reservation.job.id);
  }

  async findOneByJobId(jobId: number): Promise<Reservation> {
    return this.reservationRepository.findOne({
      where: { jobId },
      relations: ['manager.company', 'dispatcher.company', 'vehicle', 'pricelist'],
    });
  }

  async getUpcomingReservation(operatorId: number, fetchingUser: SerializedUser) {
    const companyId = fetchingUser?.companyId;

    const expressionsToAdd: Expression[] = [];

    expressionsToAdd.push({
      category: '"reservation"."operatorId"',
      operator: '=' as QueryOperator,
      value: operatorId,
      conditionType: 'AND',
    });

    expressionsToAdd.push({
      category: '"manager"."companyId"',
      operator: '=' as QueryOperator,
      value: companyId,
      conditionType: 'AND',
    });

    expressionsToAdd.push({
      category: '"job"."status"',
      operator: '=' as QueryOperator,
      value: JobStatus.NOT_STARTED,
      conditionType: 'AND',
    });

    expressionsToAdd.push({
      category: '"reservation"."dateFrom"',
      operator: '>=',
      value: new Date().toISOString(),
      conditionType: 'AND',
    });

    const result: EntityWithCountDto<Reservation> = await this.findMany(
      {
        limit: 1,
        offset: 0,
        sortModel: [
          {
            field: '"reservation"."dateFrom"',
            sort: SortDirections.ASC,
          },
        ],
        expressions: expressionsToAdd,
      },
      false,
    );

    return result.data?.[0] || null;
  }

  async getPendingReservation(operatorId: number, fetchingUser: SerializedUser) {
    const companyId = fetchingUser?.companyId;

    const expressionsToAdd: Expression[] = [];

    expressionsToAdd.push({
      category: '"reservation"."operatorId"',
      operator: '=',
      value: operatorId,
      conditionType: 'AND',
    });

    expressionsToAdd.push({
      category: '"manager"."companyId"',
      operator: '=',
      value: companyId,
      conditionType: 'AND',
    });

    expressionsToAdd.push({
      category: '"job"."status"',
      operator: 'not_in', // or '!='
      value: [JobStatus.NOT_STARTED, JobStatus.CANCELLED, JobStatus.COMPLETE],
      conditionType: 'AND',
    });

    expressionsToAdd.push({
      category: '"job"."start"',
      operator: '<=',
      value: new Date().toISOString(),
      conditionType: 'AND',
    });

    expressionsToAdd.push({
      category: '"reservation"."dateTo"',
      operator: '>=',
      value: new Date().toISOString(),
      conditionType: 'AND',
    });

    const result = await this.findMany(
      {
        limit: 1,
        offset: 0,
        sortModel: [
          {
            field: '"reservation"."dateFrom"',
            sort: SortDirections.DESC,
          },
        ],
        expressions: expressionsToAdd,
      },
      false,
    );

    return result.data?.[0] || null;
  }

  async findExpiredPendingReservations(): Promise<Reservation[]> {
    this.logger.log({ method: 'findExpiredPendingReservations' });

    const currentDate = new Date();
    const dateWithBuffer = new Date(currentDate.getTime() + 2 * 60 * 60 * 1000);
    const dateStr = dateWithBuffer.toISOString();

    const expressionsToAdd: Expression[] = [];

    expressionsToAdd.push({
      category: '"reservation"."dateTo"',
      operator: '<=',
      value: dateStr,
      conditionType: 'AND',
    });

    expressionsToAdd.push({
      category: '"job"."status"',
      operator: 'not_in',
      value: [JobStatus.COMPLETE, JobStatus.CANCELLED],
      conditionType: 'AND',
    });

    const result = await this.findMany(
      {
        limit: 500,
        expressions: expressionsToAdd,
      },
      false,
    );

    return result.data;
  }

  private async handleFileUploads<T extends ReservationDto>(
    params: HandleFileUploadsParams<T>,
  ): Promise<void> {
    const { reservationDto, userId, existingReservation, jobId } = params;

    const fileFields = [
      {
        field: 'trafficPlanFile',
        keyPrefix: 'trafficPlan',
        key: 'trafficPlanKey',
      },
      {
        field: 'localAdministrationAuthorizationFile',
        keyPrefix: 'localAdminAuth',
        key: 'localAdministrationAuthorizationKey',
      },
      {
        field: 'parkingPermitAcquiredFile',
        keyPrefix: 'parkingPermit',
        key: 'parkingPermitAcquiredKey',
      },
    ];

    for (const { field, keyPrefix, key } of fileFields) {
      const file = reservationDto[field];
      const existingKey = existingReservation[key];

      if (file) {
        if (existingKey) {
          try {
            await this.filesService.removeFile(existingKey, process.env.S3_BUCKET_NAME);
          } catch (error) {
            this.logger.error({
              method: 'handleFileUploads',
              message: `Failed to remove existing file with key: ${existingKey}`,
              error: error.message,
            });
          }
        }
        try {
          const uploadResult = await this.filesService.uploadPrivateFile(
            process.env.S3_BUCKET_NAME,
            file.buffer,
            `job-${jobId}/${keyPrefix}-${userId}`,
            file.mimetype,
          );
          const baseField = field.replace('File', '');
          existingReservation[`${baseField}Key`] = uploadResult.Key;
        } catch (error) {
          this.logger.error({
            method: 'handleFileUploads',
            error: error.message,
          });
          throw new HttpException('Error uploading file', HttpStatus.INTERNAL_SERVER_ERROR);
        }
        delete existingReservation[field];
      }
    }
  }

  async enqueueInvoiceForSingleReservation(
    singleInvoiceDto: SingleInvoiceDto,
    operatorManagerId: number,
  ) {
    this.logger.log(
      { method: 'enqueueInvoiceForSingleReservation' },
      { reservationIdsNum: singleInvoiceDto.reservationIds.length },
      { operatorManagerId },
    );

    const operatorCompany = await this.companyService.findOneByUserId(operatorManagerId);
    let invoiceNumber: string;
    if (singleInvoiceDto.dispatcherCompanyId) {
      const dispatcherCompany = await this.companyService.findOneById(
        singleInvoiceDto.dispatcherCompanyId,
      );
      invoiceNumber = generateInvoiceNumber(dispatcherCompany);
    } else {
      invoiceNumber = generateInvoiceNumber(operatorCompany);
    }

    const createInvoiceLog: CreateInvoiceLogDto = {
      reservationIds: singleInvoiceDto.reservationIds,
      dispatcherCompanyId: singleInvoiceDto.dispatcherCompanyId || null,
      operatorCompanyId: operatorCompany.id,
      invoiceNumber,
      type: InvoiceType.SINGLE,
      status: InvoiceStatus.PENDING,
      total: 0,
    };
    const invoiceLog = await this.invoiceLogService.create(createInvoiceLog);

    // Loop through each reservationId and enqueue a job for each
    for (const reservationId of singleInvoiceDto.reservationIds) {
      await this.invoiceQueue.add(JOBS.INVOICE, {
        reservationId,
        invoiceLog,
        operatorManagerId,
      });
    }

    return { status: 'ok', invoiceLog };
  }

  async enqueueInvoiceForManyReservations(
    reservationDto: InvoiceReservationDto,
    operatorManagerId: number,
  ) {
    this.logger.log({
      method: 'enqueueInvoiceForManyReservations',
      reservationIdsCount: reservationDto.reservationIds?.length,
      dispatcherCompanyId: reservationDto.dispatcherCompanyId,
      operatorManagerId,
    });

    if (!reservationDto.reservationIds?.length) {
      throw new BadRequestException('Reservation IDs were not provided!');
    }

    if (!reservationDto.dispatcherCompanyId) {
      throw new BadRequestException('Dispatcher Company ID was not provided!');
    }

    const dispatcherCompany = await this.companyService.findOneById(
      reservationDto.dispatcherCompanyId,
    );
    const operatorCompany = await this.companyService.findOneByUserId(operatorManagerId);

    const invoiceNumber = generateInvoiceNumber(dispatcherCompany);

    const createInvoiceLog: CreateInvoiceLogDto = {
      reservationIds: reservationDto.reservationIds,
      dispatcherCompanyId: reservationDto.dispatcherCompanyId,
      operatorCompanyId: operatorCompany.id,
      invoiceNumber,
      type: InvoiceType.BULK,
      status: InvoiceStatus.PENDING,
      total: 0,
    };
    const bulkInvoiceLog = await this.invoiceLogService.create(createInvoiceLog);

    // Enqueue a single job containing all the necessary data
    await this.bulkInvoiceQueue.add(JOBS.BULK_INVOICE, {
      reservationIds: reservationDto.reservationIds,
      dispatcherCompanyId: reservationDto.dispatcherCompanyId,
      operatorManagerId,
      invoiceLog: bulkInvoiceLog,
    });

    return { status: 'ok', bulkInvoiceLog };
  }

  async enqueueInvoiceForContract(contractId: number, generatingUser: SerializedUser) {
    this.logger.log({
      method: 'enqueueInvoiceForContract',
      contractId,
    });

    const queryRunner = this.dataSource.createQueryRunner('slave');
    try {
      const body: GetContractDto = {
        expressions: [
          {
            category: ContractCategoryTypes.id,
            operator: '=' as QueryOperator,
            value: contractId,
            conditionType: 'AND',
          },
        ],
        limit: 1,
        offset: 0,
        sortModel: [],
        relations: ['dispatcherCompany'],
      };
      const resultContracts = await this.contractService.findMany(body, generatingUser, false);

      const [contract] = resultContracts.data;

      if (!contract) {
        this.logger.error({
          method: 'enqueueInvoiceForContract',
          contractId,
          error: 'Contract was not found',
        });
        throw new HttpException('Contract was not found!', HttpStatus.NOT_FOUND);
      }

      const expressionsToAdd: Expression[] = [];
      if (contract.status != ContractedStatus.APPROVED) {
        this.logger.warn({
          method: 'enqueueInvoiceForContract',
          message: `Contract (${contractId}) cant be invoiced.`,
        });
        return { status: 'fail' };
      }
      expressionsToAdd.push({
        category: '"reservation"."pricelistId"',
        operator: '=' as QueryOperator,
        value: contract.pricelistId,
        conditionType: 'AND',
      });

      const result: EntityWithCountDto<Reservation> = await this.findMany(
        {
          limit: 1,
          offset: 0,
          sortModel: [
            {
              field: '"reservation"."dateFrom"',
              sort: SortDirections.ASC,
            },
          ],
          expressions: expressionsToAdd,
        },
        false,
      );

      if (!result.totalCount) {
        this.logger.warn({
          method: 'enqueueInvoiceForContract',
          message: `No reservations found for this (${contractId}) contract.`,
        });
        return { status: 'fail' };
      }
      const reservationIds = result.data.map(r => r.id);
      const reservationsSQL = await getReservationsForInvoiceQuery(
        reservationIds,
        contract.dispatcherCompanyId,
        contract.operatorCompanyId,
        JobStatus.COMPLETE,
      );
      const reservationsQueryResult = await queryRunner.manager.query(reservationsSQL);

      if (!reservationsQueryResult.length) {
        this.logger.log({
          method: 'enqueueInvoiceForContract',
          message: 'No matching reservations found for invoice generation',
          operatorCompanyId: contract.operatorCompanyId,
          mailSent: false,
          invoiceUpdated: false,
        });
        return { status: 'fail' };
      }

      const invoiceNumber = generateInvoiceNumber(contract.dispatcherCompany);

      const createInvoiceLog: CreateInvoiceLogDto = {
        reservationIds: reservationIds,
        dispatcherCompanyId: contract.dispatcherCompanyId,
        operatorCompanyId: contract.operatorCompanyId,
        invoiceNumber,
        type: InvoiceType.BULK,
        status: InvoiceStatus.PENDING,
        total: 0,
      };
      const bulkInvoiceLog = await this.invoiceLogService.create(createInvoiceLog);

      // Enqueue a single job containing all the necessary data
      await this.bulkInvoiceQueue.add(JOBS.BULK_INVOICE, {
        reservationIds: reservationIds,
        dispatcherCompanyId: contract.dispatcherCompanyId,
        operatorManagerId: contract.operatorCompanyId,
        bulkInvoiceLog: bulkInvoiceLog,
      });

      return { status: 'ok', bulkInvoiceLog };
    } catch (error) {
      this.logger.error('Error enqueueing invoice for contract:', error);

      return { status: 'fail', error: error.message };
    } finally {
      await queryRunner.release();
      return { status: 'ok' };
    }
  }

  async generatePdfForReservation(reservationId: number): Promise<string | null> {
    this.logger.log({ method: 'generatePdfForReservation', reservationId });

    const queryRunner = this.dataSource.createQueryRunner('slave');
    const reservationSQL = getSingleReservationForInvoiceQuery(reservationId, false); // Allow invoiced reservations for download

    try {
      const reservationData = await queryRunner.manager.query(reservationSQL);
      const reservation = reservationData[0] || null;

      if (!reservation) {
        this.logger.log({
          method: 'generatePdfForReservation',
          message: `Reservation with ID ${reservationId} not found`,
        });
        return null;
      }

      const dispatcherCompany = await this.companyService.findOneByUserId(
        reservation.dispatcherId ?? reservation.operatorId,
      );
      const operatorCompany = await this.companyService.findOneByUserId(reservation.operatorId);
      const invoiceNumber = generateInvoiceNumber(dispatcherCompany);

      if (!dispatcherCompany.billingEmail) {
        throw new BadRequestException(
          `Billing email is missing for company Id: ${dispatcherCompany.id}!`,
        );
      }

      const { totalSum, reservationDetails } = await this.processReservationsForInvoice(
        [reservation],
        operatorCompany,
      );

      const [reservationDetail] = reservationDetails;

      const singleInvoiceReport: GenerateSingleReservationReport = {
        reservation,
        dispatcherCompany,
        operatorCompany,
        invoiceNumber,
        totalExclVat: reservationDetail.totalExclVat,
        vatRate: reservationDetail.vatRate,
        vatAmount: reservationDetail.vatAmount,
        totalSum,
        job: reservationDetail.job,
      };
      const reservationBuffer =
        await this.pdfService.generateSingleReservationReport(singleInvoiceReport);

      this.logger.log({
        method: 'generatePdfForReservation',
        reservationId,
        pdfGenerated: true,
      });

      //Convert buffer to base64
      const pdfBase64 = `data:application/pdf;base64,${reservationBuffer.toString('base64')}`;

      return pdfBase64;
    } catch (e) {
      this.logger.error({
        method: 'generatePdfForReservation',
        errorMessage: e.message,
        error: e,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async generateInvoiceForSingleReservation(reservationId: number, invoiceLog: InvoiceLog) {
    this.logger.log({ method: 'generateInvoiceForSingleReservation', reservationId });

    const queryRunner = this.dataSource.createQueryRunner('slave');
    const reservationSQL = getSingleReservationForInvoiceQuery(reservationId, true); // Only non-invoiced reservations for new invoice generation

    try {
      const [reservation] = await queryRunner.manager.query(reservationSQL);
      if (!reservation) {
        this.logger.log({
          method: 'generateInvoiceForSingleReservation',
          reservationId,
          mailSent: false,
          invoiceUpdated: false,
        });
        return { status: 'fail' };
      }

      let dispatcherCompany: Company | null = null;
      if (reservation.dispatcherId !== null) {
        dispatcherCompany = await this.companyService.findOneByUserId(reservation.dispatcherId);

        if (!dispatcherCompany.billingEmail) {
          throw new BadRequestException(
            `Billing email is missing for company Id: ${dispatcherCompany.id}!`,
          );
        }
      }
      const operatorCompany = await this.companyService.findOneByUserId(reservation.operatorId);
      const invoiceNumber = invoiceLog.invoiceNumber;

      const { totalSum, reservationDetails } = await this.processReservationsForInvoice(
        [reservation],
        operatorCompany,
      );

      const [reservationDetail] = reservationDetails;

      const singleInvoiceReport: GenerateSingleReservationReport = {
        reservation,
        dispatcherCompany,
        operatorCompany,
        invoiceNumber,
        totalExclVat: reservationDetail.totalExclVat,
        vatRate: reservationDetail.vatRate,
        vatAmount: reservationDetail.vatAmount,
        totalSum,
        job: reservationDetail.job,
      };
      const reservationBuffer =
        await this.pdfService.generateSingleReservationReport(singleInvoiceReport);

      await this.mailerService.sendMail({
        to: dispatcherCompany?.billingEmail || reservation.clientDetails.email,
        subject: 'Reservation Report',
        template: 'reservation-single-report',
        context: {},
        attachments: [{ filename: 'ReservationReport.pdf', content: reservationBuffer }],
      });

      const reservationTotals = [{ id: reservation.id, totalSum }];

      await this.updateTotalSum(reservationTotals);

      const updatedReservations = await this.markReservationAsInvoicedById(
        [reservation.id],
        invoiceNumber,
      );

      const updateInvoiceLog: UpdateInvoiceLogPayload = {
        logId: invoiceLog.id,
        newStatus: InvoiceStatus.COMPLETED,
        newTotal: totalSum,
      };
      await this.invoiceLogService.updateStatusAndTotal(updateInvoiceLog);

      this.logger.log({
        method: 'generateInvoiceForSingleReservation',
        reservationId,
        mailSent: true,
        invoiceUpdated: updatedReservations.affected > 0,
      });

      return { success: true, status: 'ok' };
    } catch (e) {
      this.logger.error({
        method: 'generateInvoiceForSingleReservation',
        errorMessage: e.message,
        error: e,
      });
      const updateInvoiceLog: UpdateInvoiceLogPayload = {
        logId: invoiceLog.id,
        newStatus: InvoiceStatus.FAILED,
      };
      await this.invoiceLogService.updateStatusAndTotal(updateInvoiceLog);
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async generateInvoiceForManyReservations(
    invoiceForManyReservation: GenerateInvoiceForManyReservation,
  ) {
    const { reservationIds, dispatcherCompanyId, operatorManagerId, invoiceLog } =
      invoiceForManyReservation;
    this.logger.log({
      method: 'generateInvoiceForManyReservations',
      reservationIdsCount: reservationIds.length,
      dispatcherCompanyId,
      operatorManagerId,
    });
    const queryRunner = this.dataSource.createQueryRunner('slave');
    try {
      const reservations = await this.reservationRepository.findBy({ id: In(reservationIds) });

      if (!reservations.length) {
        this.logger.warn({
          method: 'generateInvoiceForManyReservations',
          message: 'No matching reservations found.',
        });
        return { status: 'fail' };
      }

      const reservationsSQL = await getReservationsForInvoiceQuery(
        reservationIds,
        dispatcherCompanyId,
        operatorManagerId,
        JobStatus.COMPLETE,
      );
      const reservationsQueryResult = await queryRunner.manager.query(reservationsSQL);

      if (!reservationsQueryResult.length) {
        this.logger.log({
          method: 'generateInvoiceForManyReservations',
          message: 'No matching reservations found for invoice generation',
          operatorManagerId,
          mailSent: false,
          invoiceUpdated: false,
        });
        return { status: 'fail' };
      }

      const dispatcherCompany = await this.companyService.findOneById(dispatcherCompanyId);
      const operatorCompany = await this.companyService.findOneByUserId(operatorManagerId);

      const invoiceNumber = invoiceLog.invoiceNumber;

      if (!dispatcherCompany.billingEmail) {
        throw new BadRequestException(
          `Billing email is missing for company Id: ${dispatcherCompany.id}`,
        );
      }

      // Determine if we are dealing with contract reservations.
      const isContractReservation = reservationsQueryResult[0]?.pricelist?.isContract;

      let totalSum = 0;
      let reservationDetails = [];

      if (isContractReservation) {
        // For contract reservations, process them using the daily aggregation function
        const dailyInvoices = await this.calculateDailyContractInvoices(
          reservationsQueryResult,
          operatorCompany,
        );

        totalSum = dailyInvoices.reduce((sum, dailyInvoice) => sum + dailyInvoice.totalDayCost, 0);

        reservationDetails = dailyInvoices.flatMap(dailyInvoice =>
          dailyInvoice.reservations.map(res => ({
            id: res.id,
            totalSum: res.job.totalCost,
          })),
        );
      } else {
        const result = await this.processReservationsForInvoice(
          reservationsQueryResult,
          operatorCompany,
        );
        totalSum = result.totalSum;
        reservationDetails = result.reservationDetails;
      }

      const reservationTotals = reservationDetails.map(detail => ({
        id: detail.id,
        totalSum: detail.totalSum,
      }));

      const bulkReservationReport: GenerateBulkReservationReport = {
        dispatcherCompany,
        operatorCompany,
        totalSum,
        invoiceNumber,
        totalReservations: reservationDetails.length,
      };
      const reservationsBuffer =
        await this.pdfService.generateBulkReservationReport(bulkReservationReport);

      await this.mailerService.sendMail({
        to: dispatcherCompany.billingEmail,
        subject: 'Reservation Report',
        template: 'reservation-multi-report',
        context: {},
        attachments: [{ filename: 'ReservationsReport.pdf', content: reservationsBuffer }],
      });

      await this.updateTotalSum(reservationTotals);

      const updatedReservations = await this.markReservationAsInvoicedById(
        reservationIds,
        invoiceNumber,
      );

      const updateInvoiceLog: UpdateInvoiceLogPayload = {
        logId: invoiceLog.id,
        newStatus: InvoiceStatus.COMPLETED,
        newTotal: totalSum,
      };
      await this.invoiceLogService.updateStatusAndTotal(updateInvoiceLog);

      this.logger.log({
        method: 'generateInvoiceForManyReservations',
        dispatcherCompanyId,
        operatorManagerId,
        mailSent: true,
        invoiceUpdated: updatedReservations.affected > 0,
      });
    } catch (error) {
      this.logger.error('Error generating invoice for many reservations:', error);

      const updateInvoiceLog: UpdateInvoiceLogPayload = {
        logId: invoiceLog.id,
        newStatus: InvoiceStatus.FAILED,
      };
      await this.invoiceLogService.updateStatusAndTotal(updateInvoiceLog);

      return { status: 'fail', error: error.message };
    } finally {
      await queryRunner.release();
      return { status: 'ok' };
    }
  }

  async processReservationsForInvoice(reservations: ReservationInvoice[], company: Company) {
    const workSchedule: WorkScheduleSettings[] = company?.settings?.workSchedules;

    if (!workSchedule?.length) {
      throw new Error('Invalid work schedule configuration.');
    }

    let defaultVatPercentage: number;
    const defaultTax = company?.taxes?.find(tax => tax.isDefault);
    if (defaultTax) {
      defaultVatPercentage = defaultTax.percentage;
    }

    const reservationDetails = await Promise.all(
      reservations.map(async reservation => {
        const distanceInKm = await this.vehicleService.getDistanceBetweenVehicleAndReservation(
          reservation.vehicleId,
          reservation,
        );

        calculateTotalCost(reservation, reservation.job, workSchedule, distanceInKm);

        const vatRate = defaultVatPercentage ?? 21;
        const vatAmount = (reservation.job.totalCost * vatRate) / 100;
        const totalAmount = reservation.job.totalCost + vatAmount;

        return {
          id: reservation.id,
          totalExclVat: reservation.job.totalCost,
          vatRate,
          vatAmount,
          totalSum: totalAmount,
          job: reservation.job,
        };
      }),
    );

    const totalSum = reservationDetails.reduce((sum, res) => sum + res.totalSum, 0);
    const totalExclVat = reservationDetails.reduce((sum, res) => sum + res.totalExclVat, 0);

    return { totalSum, totalExclVat, reservationDetails };
  }

  async calculateDailyContractInvoices(
    reservations: ReservationInvoice[],
    company: Company,
  ): Promise<DailyContractInvoice[]> {
    const workSchedule: WorkScheduleSettings[] = company?.settings?.workSchedules;
    if (!workSchedule?.length) {
      throw new Error('Invalid work schedule configuration.');
    }

    await Promise.all(
      reservations.map(async reservation => {
        const isCancelled = reservation.job.status === JobStatus.CANCELLED;
        if (!isCancelled) {
          const distanceInKm = await this.vehicleService.getDistanceBetweenVehicleAndReservation(
            reservation.vehicleId,
            reservation,
          );
          calculateTotalCost(reservation, reservation.job, workSchedule, distanceInKm);
        }
      }),
    );

    // Group reservations by day using the dateFrom field.
    const groupedByDate = reservations.reduce(
      (groups, reservation) => {
        const dateKey = new Date(reservation.dateFrom).toISOString().split('T')[0];
        (groups[dateKey] = groups[dateKey] || []).push(reservation);
        return groups;
      },
      {} as { [date: string]: ReservationInvoice[] },
    );

    // For each grouped day, aggregate the fees.
    const dailyInvoices: DailyContractInvoice[] = Object.entries(groupedByDate).map(
      ([dateKey, dayReservations]) => {
        const {
          dayContractFee = 0,
          dayContractDuration = 0,
          dayContractOvertimeRate = 0,
        } = dayReservations[0].pricelist || {};

        // Calculate the total working hours for all reservations of the day.
        const dayTotalHours = dayReservations.reduce(
          (sum, res) => sum + (res.job?.hourDuration || 0),
          0,
        );

        // Determine the overtime hours for the day.
        const overtimeHours = Math.max(dayTotalHours - dayContractDuration, 0);
        const aggregatedOvertimeFee = overtimeHours * dayContractOvertimeRate;

        // Sum up the total cost computed in each reservation.
        // (These represent costs like concrete, pipes, technician fees, etc.)
        const aggregatedOtherCosts = dayReservations.reduce(
          (sum, res) => sum + (res.job?.totalCost || res.totalSum || 0),
          0,
        );

        // The total day cost is the fixed contract fee plus overtime fee plus the other costs.
        const totalDayCost = dayContractFee + aggregatedOvertimeFee + aggregatedOtherCosts;

        return {
          date: dateKey,
          dayContractFee,
          contractOvertimeFee: aggregatedOvertimeFee,
          totalDayCost,
          reservations: dayReservations,
        } as DailyContractInvoice;
      },
    );

    return dailyInvoices;
  }

  async markReservationAsInvoicedById(reservationIds: number[], invoiceNumber: string) {
    const updatedReservation = await this.reservationRepository
      .createQueryBuilder('reservation')
      .update(Reservation)
      .set({
        invoiced: true,
        invoiceNumber: invoiceNumber,
      })
      .where('id IN (:...ids)', { ids: reservationIds })
      .execute();

    this.logger.log({
      method: 'markReservationAsInvoicedById',
      totalReservations: reservationIds.length,
      invoiceNumber,
    });
    return updatedReservation;
  }

  async findReservationByOrderNumber(orderNumber: string): Promise<Reservation> {
    const reservation = await this.reservationRepository.findOne({ where: { orderNumber } });

    if (!reservation) {
      throw new NotFoundException('Reservation was not found.');
    }

    return reservation;
  }

  async validateOrderNumber(orderNumber?: string): Promise<string> {
    if (orderNumber) {
      const exists = await this.checkReservationExistsByOrderNumber(orderNumber);
      if (exists) {
        throw new ConflictException('Order number already exists.');
      }
      return orderNumber;
    }

    let isUnique = false;
    let generatedOrderNumber: string;

    while (!isUnique) {
      const timestampPart = Date.now().toString().slice(-6);
      const randomPart = Math.floor(Math.random() * 1000000)
        .toString()
        .padStart(6, '0');
      generatedOrderNumber = `${timestampPart}${randomPart}`;

      const exists = await this.checkReservationExistsByOrderNumber(generatedOrderNumber);
      if (!exists) {
        isUnique = true;
      }
    }

    return generatedOrderNumber;
  }

  async checkReservationExistsByOrderNumber(orderNumber: string): Promise<boolean> {
    const reservation = await this.reservationRepository.findOne({ where: { orderNumber } });
    return !!reservation;
  }

  async validateReservationExists(orderNumber: string, clientEmail: string): Promise<boolean> {
    this.logger.log({
      method: 'validateReservationExists',
      orderNumber,
      clientEmail,
    });

    const { data: reservations } = await this.findMany(
      {
        expressions: [
          {
            category: '"reservation"."orderNumber"',
            operator: '=',
            value: orderNumber,
            conditionType: 'AND',
          },
          {
            category: '(("reservation"."clientDetails")::jsonb)->>\'email\'',
            operator: '=',
            value: clientEmail,
            conditionType: 'AND',
          },
        ],
        limit: 1,
        sortModel: [],
      },
      false,
    );

    return reservations.length > 0;
  }

  async fetchJobByOrderNumber(orderNumber: string) {
    const reservation = await this.reservationRepository.findOne({
      where: {
        orderNumber: orderNumber,
      },
      relations: [
        'job',
        'job.jobEvents',
        'job.jobLocations',
        'dispatcher',
        'dispatcher.company',
        'vehicle',
        'operator',
      ],
    });

    if (!reservation) {
      throw new BadRequestException('Invalid order number!');
    }

    return reservation;
  }

  async updateTotalSum(reservationTotals: { id: number; totalSum: number }[]): Promise<void> {
    const queryRunner = this.reservationRepository.manager.connection.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      for (const { id, totalSum } of reservationTotals) {
        const result = await queryRunner.manager
          .createQueryBuilder()
          .update(Reservation)
          .set({ totalSum })
          .where('id = :id', { id })
          .execute();

        if (result.affected === 0) {
          throw new BadRequestException(`Reservation with ID ${id} not found or update failed`);
        }
      }

      await queryRunner.commitTransaction();

      this.logger.log({
        method: 'updateTotalSum',
        reservationTotals,
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async markAsRead(id: number): Promise<Reservation> {
    const reservation = await this.findOneById(id);
    reservation.hasBeenRead = true;
    return this.reservationRepository.save(reservation);
  }

  async reservationCost(id: number): Promise<ReservationCost> {
    this.logger.log({ method: 'reservationCost', reservationId: id });
    const reservation = await this.findOneById(id);
    const job: JobWithSignature = reservation.job;

    const company = await this.companyService.findOneById(reservation.manager.companyId);
    const workSchedule: WorkScheduleSettings[] = company.settings.workSchedules;

    const vehicle = reservation.vehicle;

    const distanceInKm = await this.vehicleService.getDistanceBetweenVehicleAndReservation(
      vehicle.id,
      reservation,
    );
    if (job.status === JobStatus.COMPLETE) {
      try {
        calculateTotalCost(reservation, job, workSchedule, distanceInKm);
      } catch (error) {
        throw new InternalServerErrorException(error.message);
      }
    }

    return mapJobToReservationCost(job);
  }

  async duplicateReservation(
    id: number,
    duplicateReservationDto: DuplicateReservationDto,
    duplicatingUser?: SerializedUser,
  ): Promise<Reservation> {
    const duplicatingUserId = duplicatingUser?.sub;
    const { dateFrom: newDateFrom, dateTo: newDateTo } = duplicateReservationDto;

    this.logger.log({
      method: 'duplicateReservation',
      originalReservationId: id,
      duplicatingUser: duplicatingUserId,
      newDateFrom,
      newDateTo,
    });

    // Find the original reservation with all required relations
    const originalReservation = await this.findOneById(id, duplicatingUser);

    // Create the job data for duplication
    const originalJob = originalReservation.job;
    const jobData: CreateJobDto = {
      amountOfConcrete: originalJob.amountOfConcrete,
      flowRate: originalJob.flowRate,
      balance: originalJob.balance,
      barbotine: originalJob.barbotine,
      flexiblePipeLength80Mm: originalJob.flexiblePipeLength80Mm,
      flexiblePipeLength90Mm: originalJob.flexiblePipeLength90Mm,
      frontOutriggersSpan: originalJob.frontOutriggersSpan,
      rearOutriggersSpan: originalJob.rearOutriggersSpan,
      frontSideOpening: originalJob.frontSideOpening,
      rearSideOpening: originalJob.rearSideOpening,
      rigidPipeLength100Mm: originalJob.rigidPipeLength100Mm,
      rigidPipeLength120Mm: originalJob.rigidPipeLength120Mm,
      extraCementBag: originalJob.extraCementBag,
      units: originalJob.units,
      presenceOfPowerLines: originalJob.presenceOfPowerLines,
      voltage: originalJob.voltage,
      pipeStartingFromBAC: originalJob.pipeStartingFromBAC,
      parkingPermitAcquired: true, // Default value since it's required in DTO but not in entity
      parkingOn: originalJob.parkingOn,
      cleaning: originalJob.cleaning,
      terrainStability: originalJob.terrainStability,
      tonnageRestriction: originalJob.tonnageRestriction,
      authorizedWeight: originalJob.authorizedWeight,
      heightRestriction: originalJob.heightRestriction,
      heightLimit: originalJob.heightLimit,
      status: JobStatus.NOT_STARTED, // Always create as NOT_STARTED
      jobType: originalJob.jobType,
      supplyOfTheChemicalSlushie: originalJob.supplyOfTheChemicalSlushie,
      ciaw: originalJob.ciaw,
      reasonForDelay: originalJob.reasonForDelay,
      comments: originalJob.comments,
    };

    // Use new dates if provided, otherwise use original dates
    const dateFrom = newDateFrom ? new Date(newDateFrom) : originalReservation.dateFrom;
    const dateTo = newDateTo ? new Date(newDateTo) : originalReservation.dateTo;

    // Validate that both dates are provided if one is provided
    if ((newDateFrom && !newDateTo) || (!newDateFrom && newDateTo)) {
      throw new BadRequestException(
        'Both dateFrom and dateTo must be provided when specifying new dates',
      );
    }

    // Create the duplicate reservation data
    const duplicateData: CreateReservationDto = {
      managerId: originalReservation.managerId,
      operatorId: originalReservation.operatorId,
      dispatcherId: originalReservation.dispatcherId,
      vehicleId: originalReservation.vehicleId,
      reservationType: originalReservation.reservationType as ReservationType,
      dateFrom: dateFrom,
      dateTo: dateTo,
      siteAddress: originalReservation.siteAddress,
      city: originalReservation.city,
      plz: originalReservation.plz,
      location: originalReservation.location,
      clientDetails: originalReservation.clientDetails,
      job: jobData,
      trafficPlanKey: originalReservation.trafficPlanKey,
      parkingPermitAcquiredKey: originalReservation.parkingPermitAcquiredKey,
      localAdministrationAuthorizationKey: originalReservation.localAdministrationAuthorizationKey,
      pricelistId: originalReservation.pricelistId,
    };

    // Create the new reservation using the existing create method
    const duplicatedReservation = await this.create(duplicateData, duplicatingUser);

    this.logger.log({
      method: 'duplicateReservation',
      originalReservationId: id,
      duplicatedReservationId: duplicatedReservation.id,
      duplicatingUser: duplicatingUserId,
    });

    return duplicatedReservation;
  }

  /**
   * Validates reservation dates against company holidays
   * @param dateFrom - Start date of the reservation
   * @param dateTo - End date of the reservation
   * @param companyId - ID of the company to check holidays for
   * @throws BadRequestException if dates conflict with holidays
   */
  private async validateReservationDatesAgainstHolidays(
    dateFrom: Date,
    dateTo: Date,
    companyId: number,
  ): Promise<void> {
    try {
      this.logger.log({
        method: 'validateReservationDatesAgainstHolidays',
        companyId,
        dateFrom: dateFrom.toISOString(),
        dateTo: dateTo.toISOString(),
        message: 'Starting holiday validation',
      });

      const companySettings = await this.companySettingsService.findMany(
        {
          expressions: [
            {
              category: CompanySettingsCategoryTypes.companyId,
              operator: '=' as any,
              value: companyId,
              conditionType: 'AND',
            },
          ],
          limit: 1,
          offset: 0,
          sortModel: [],
          relations: ['holidays'],
        },
        { companyId } as any,
        false,
      );

      this.logger.log({
        method: 'validateReservationDatesAgainstHolidays',
        companyId,
        companySettingsFound: companySettings.data.length,
        holidaysCount: companySettings.data[0]?.holidays?.length || 0,
      });

      if (companySettings.data.length === 0 || !companySettings.data[0].holidays) {
        this.logger.log({
          method: 'validateReservationDatesAgainstHolidays',
          companyId,
          message: 'No holidays found, allowing reservation',
        });
        return;
      }

      const holidays = companySettings.data[0].holidays;

      this.logger.log({
        method: 'validateReservationDatesAgainstHolidays',
        companyId,
        holidays: holidays.map(h => ({ date: h.date, reason: h.reason })),
        message: 'Checking holidays against reservation dates',
      });

      const { hasHolidays, conflictingHolidays } = checkDateRangeForHolidays(
        dateFrom,
        dateTo,
        holidays,
      );

      this.logger.log({
        method: 'validateReservationDatesAgainstHolidays',
        companyId,
        hasHolidays,
        conflictingHolidays: conflictingHolidays.map(h => ({ date: h.date, reason: h.reason })),
      });

      if (hasHolidays) {
        const errorMessage = formatHolidayErrorMessage(conflictingHolidays);
        this.logger.warn({
          method: 'validateReservationDatesAgainstHolidays',
          companyId,
          errorMessage,
          message: 'Holiday conflict detected, throwing error',
        });
        throw new BadRequestException(errorMessage);
      }

      this.logger.log({
        method: 'validateReservationDatesAgainstHolidays',
        companyId,
        message: 'No holiday conflicts found, allowing reservation',
      });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.warn({
        method: 'validateReservationDatesAgainstHolidays',
        companyId,
        dateFrom,
        dateTo,
        error: error.message,
        message: 'Holiday validation failed, allowing reservation to proceed',
      });
    }
  }

  async countFutureReservationsByOperator(operatorId: number): Promise<number> {
    this.logger.log({
      method: 'countFutureReservationsByOperator',
      operatorId,
    });

    const count = await this.reservationRepository.count({
      where: {
        operatorId,
        dateFrom: MoreThanOrEqual(new Date()),
      },
    });

    this.logger.log({
      method: 'countFutureReservationsByOperator',
      operatorId,
      count,
    });

    return count;
  }
}
