import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1707088544541 implements MigrationInterface {
  name = 'Migrations1707088544541';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomSize"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomSize" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomSize"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomSize" character varying`);
  }
}
