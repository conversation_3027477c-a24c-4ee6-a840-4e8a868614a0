import { HourTypes } from 'src/job/v1/interfaces/jobWithSignature';
import { TransportRate } from 'src/pricelist/entities/pricelist.entity';

export interface ReservationCost {
  totalCost: number;
  baseFee: number;
  baseHourBreakdown: HourTypes;
  concreteCost: number;
  pipe80mmCost: number;
  pipe90mmCost: number;
  pipe100mmCost: number;
  pipe120mmCost: number;
  barbotineCost: number;
  supplyOfTheChemicalSlushieCost: number;
  cleaningFee: number;
  extraCementBagCost: number;
  secondTechnicianFee: number;
  secondTechnicianHourBreakdown: HourTypes;
  pumpTiersCost: number;
  chargedTransportRate: TransportRate;
  additionalHourFee: number;
  additionalHours: number;
  additionalHourBreakdown: HourTypes;
}
