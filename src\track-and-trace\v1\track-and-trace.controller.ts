import { Body, Controller, Get, Param, Post, UseGuards, VERSION_NEUTRAL } from '@nestjs/common';
import { TrackAndTraceService } from './track-and-trace.service';
import { AuthenticateDto } from '../dto/authenticate.dto';
import { ApiTags } from '@nestjs/swagger';
import { Public } from 'src/common/decorators/public.decorator';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { SubmitSignatureDto } from '../dto/submit-signature.dto';

@ApiTags('Track and Trace')
@Controller({ path: 'track-and-trace', version: VERSION_NEUTRAL })
export class TrackAndTraceController {
  constructor(private readonly trackService: TrackAndTraceService) {}

  @Post('authenticate')
  @Public()
  async authenticateOrder(@Body() body: AuthenticateDto) {
    return await this.trackService.authenticateOrder(body);
  }

  @Get('/:orderNumber')
  @UseGuards(AuthGuard)
  async getJobInfo(@Param('orderNumber') orderNumber: string) {
    return this.trackService.getJobInfo(orderNumber);
  }

  @UseGuards(AuthGuard)
  @Post('add-job-signature')
  async submitSignature(@Body() body: SubmitSignatureDto) {
    return await this.trackService.submitSignature(body);
  }
}
