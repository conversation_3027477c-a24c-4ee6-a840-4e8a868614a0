{"info": {"_postman_id": "eb0a77c5-75e9-4f3e-90cc-55d7b1534297", "name": "ConcreteEasy", "description": "This is the API for ConcreteEasy application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "auth", "item": [{"name": "Auth Controller sign In", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"email\": \"non Duis\",\n  \"password\": \"consequat nisi velit occaecat\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Auth Controller logout", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "users", "item": [{"name": "{id}", "item": [{"name": "Users Controller find One", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/:id", "host": ["{{baseUrl}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/:id", "host": ["{{baseUrl}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Users Controller update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/:id", "host": ["{{baseUrl}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/:id", "host": ["{{baseUrl}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Users Controller remove", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/users/:id", "host": ["{{baseUrl}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/users/:id", "host": ["{{baseUrl}}"], "path": ["users", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "Users Controller create", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON> \",\n  \"surname\": \"<PERSON><PERSON><PERSON>\",\n  \"companyName\": \"TEST COMPANY\",\n  \"companyAddress\": \"TEST ADDRESS\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password\",\n  \"phoneNumber\": \"+38970618968\",\n  \"vatNumber\": \"123\",\n  \"status\": 1,\n  \"role\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"irure ipsum \",\n  \"surname\": \"occaecat veniam ea ad fugiat\",\n  \"companyName\": \"ut\",\n  \"companyAddress\": \"enim\",\n  \"email\": \"nulla culpa\",\n  \"password\": \"deserunt elit\",\n  \"phoneNumber\": \"ut tempor nisi\",\n  \"vatNumber\": \"quis qui ut commodo do\",\n  \"status\": 1,\n  \"role\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Users Controller find All", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "vehicle", "item": [{"name": "{id}", "item": [{"name": "Vehicle Controller find One", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/vehicle/:id", "host": ["{{baseUrl}}"], "path": ["vehicle", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/vehicle/:id", "host": ["{{baseUrl}}"], "path": ["vehicle", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Vehicle Controller update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"managerId\": -87913206.26306608,\n  \"operatorId\": -96582238.8032885,\n  \"companyName\": \"sint non enim\",\n  \"type\": 96926227.3190158,\n  \"typeOfMotorization\": \"dolor consequat\",\n  \"licensePlateNumber\": \"nisi mollit ex\",\n  \"weight\": 27991401.*********,\n  \"height\": 81021044.0960975,\n  \"length\": 76253732.94662845,\n  \"width\": -14185673.*********,\n  \"verticalReach\": 45776838.*********,\n  \"horizontalReach\": -12338728.*********,\n  \"endHoseLength\": -87332158.38738419,\n  \"maxFlowRate\": -22653248.64741452,\n  \"maxConcretePressure\": -9345739.*********,\n  \"availableFlexiblePipeLength80Mm\": -76073081.99985488,\n  \"availableFlexiblePipeLength90Mm\": 32166455.10932623,\n  \"availableRigidPipeLength\": 27547090.*********,\n  \"maxDownwardReach\": 87145598.9840399,\n  \"numberOfBoomSections\": -987066.**********,\n  \"minUnfoldingHeight\": 67243526.67495662,\n  \"boomRotation\": 9908190.*********,\n  \"rubberEndHoseLength\": -47849322.51004008,\n  \"frontOutriggerSpan\": -19539654.76833588,\n  \"rearOutriggerSpan\": 53605601.66375175,\n  \"boomUnfoldingSystem\": \"aute consequat dolo\",\n  \"bacExit\": false,\n  \"weekdaysHourlyPrice\": -20854899.422064975,\n  \"weekendHourlyPrice\": 56470947.7739273,\n  \"contractWeekdaysPrice\": -43390614.867977105,\n  \"contractWeekendPrice\": 53152929.1243372,\n  \"flatWeekdaysFee\": -3555001.2696118504,\n  \"flatWeekendFee\": 75842140.7149294,\n  \"pricePerMeterPumped\": 6260082.4916756,\n  \"cementBagPrice\": -40545123.37711711,\n  \"pricePerMeterOfRigidPipePlaced\": -43379007.856242605,\n  \"pricePerMeterOfFlexiblePipePlaced\": -20849604.861907795,\n  \"cleaningPrice\": -53087845.64834244,\n  \"siteAddress\": \"sed Duis veniam e\",\n  \"country\": \"aute id mollit\",\n  \"location\": {\n    \"type\": \"la\",\n    \"coordinates\": [\n      -47602430.39664247,\n      -175312.72028921545\n    ]\n  },\n  \"coverage\": 96801922.0540663,\n  \"outCoverage50KmAdditionalFee\": -85227347.74000655,\n  \"outCoverage100KmAdditionalFee\": 13986766.906920463,\n  \"uniqueIdentificationNumber\": \"cupidatat nostrud ut Ut sit\",\n  \"completedJobs\": -405375.5784408748\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/vehicle/:id", "host": ["{{baseUrl}}"], "path": ["vehicle", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"managerId\": -87913206.26306608,\n  \"operatorId\": -96582238.8032885,\n  \"companyName\": \"sint non enim\",\n  \"type\": 96926227.3190158,\n  \"typeOfMotorization\": \"dolor consequat\",\n  \"licensePlateNumber\": \"nisi mollit ex\",\n  \"weight\": 27991401.*********,\n  \"height\": 81021044.0960975,\n  \"length\": 76253732.94662845,\n  \"width\": -14185673.*********,\n  \"verticalReach\": 45776838.*********,\n  \"horizontalReach\": -12338728.*********,\n  \"endHoseLength\": -87332158.38738419,\n  \"maxFlowRate\": -22653248.64741452,\n  \"maxConcretePressure\": -9345739.*********,\n  \"availableFlexiblePipeLength80Mm\": -76073081.99985488,\n  \"availableFlexiblePipeLength90Mm\": 32166455.10932623,\n  \"availableRigidPipeLength\": 27547090.*********,\n  \"maxDownwardReach\": 87145598.9840399,\n  \"numberOfBoomSections\": -987066.**********,\n  \"minUnfoldingHeight\": 67243526.67495662,\n  \"boomRotation\": 9908190.*********,\n  \"rubberEndHoseLength\": -47849322.51004008,\n  \"frontOutriggerSpan\": -19539654.76833588,\n  \"rearOutriggerSpan\": 53605601.66375175,\n  \"boomUnfoldingSystem\": \"aute consequat dolo\",\n  \"bacExit\": false,\n  \"weekdaysHourlyPrice\": -20854899.422064975,\n  \"weekendHourlyPrice\": 56470947.7739273,\n  \"contractWeekdaysPrice\": -43390614.867977105,\n  \"contractWeekendPrice\": 53152929.1243372,\n  \"flatWeekdaysFee\": -3555001.2696118504,\n  \"flatWeekendFee\": 75842140.7149294,\n  \"pricePerMeterPumped\": 6260082.4916756,\n  \"cementBagPrice\": -40545123.37711711,\n  \"pricePerMeterOfRigidPipePlaced\": -43379007.856242605,\n  \"pricePerMeterOfFlexiblePipePlaced\": -20849604.861907795,\n  \"cleaningPrice\": -53087845.64834244,\n  \"siteAddress\": \"sed Duis veniam e\",\n  \"country\": \"aute id mollit\",\n  \"location\": {\n    \"type\": \"la\",\n    \"coordinates\": [\n      -47602430.39664247,\n      -175312.72028921545\n    ]\n  },\n  \"coverage\": 96801922.0540663,\n  \"outCoverage50KmAdditionalFee\": -85227347.74000655,\n  \"outCoverage100KmAdditionalFee\": 13986766.906920463,\n  \"uniqueIdentificationNumber\": \"cupidatat nostrud ut Ut sit\",\n  \"completedJobs\": -405375.5784408748\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/vehicle/:id", "host": ["{{baseUrl}}"], "path": ["vehicle", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Vehicle Controller remove", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/vehicle/:id", "host": ["{{baseUrl}}"], "path": ["vehicle", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/vehicle/:id", "host": ["{{baseUrl}}"], "path": ["vehicle", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "Vehicle Controller create", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"managerId\": -6137944.58082144,\n  \"operatorId\": 74307409.79413667,\n  \"companyName\": \"ullamco et voluptate est aliquip\",\n  \"type\": -20618195.3556944,\n  \"typeOfMotorization\": \"sunt Excepteur \",\n  \"licensePlateNumber\": \"aliqua\",\n  \"weight\": 39961048.06335205,\n  \"height\": 24922297.01831858,\n  \"length\": 4234646.46516259,\n  \"width\": -92462758.86425814,\n  \"verticalReach\": 24299080.*********,\n  \"horizontalReach\": -15290649.*********,\n  \"endHoseLength\": 10816383.42328471,\n  \"maxFlowRate\": 94215749.18224809,\n  \"maxConcretePressure\": 61880464.*********,\n  \"availableFlexiblePipeLength80Mm\": -20781198.*********,\n  \"availableFlexiblePipeLength90Mm\": 53217441.*********,\n  \"availableRigidPipeLength\": 88974812.18104097,\n  \"maxDownwardReach\": 33389657.*********,\n  \"numberOfBoomSections\": -17903276.20407614,\n  \"minUnfoldingHeight\": 76632909.98900667,\n  \"boomRotation\": -49773049.33397662,\n  \"rubberEndHoseLength\": 59060471.33763087,\n  \"frontOutriggerSpan\": -19220777.*********,\n  \"rearOutriggerSpan\": -80516894.5670844,\n  \"boomUnfoldingSystem\": \"voluptate\",\n  \"bacExit\": true,\n  \"weekdaysHourlyPrice\": 52133797.78269771,\n  \"weekendHourlyPrice\": -40387769.28604775,\n  \"contractWeekdaysPrice\": 66750497.10289693,\n  \"contractWeekendPrice\": 75440865.25191167,\n  \"flatWeekdaysFee\": 14588554.177191466,\n  \"flatWeekendFee\": -2117800.762464121,\n  \"pricePerMeterPumped\": -68042765.47881383,\n  \"cementBagPrice\": -5419248.50885804,\n  \"pricePerMeterOfRigidPipePlaced\": -32779686.26202257,\n  \"pricePerMeterOfFlexiblePipePlaced\": -51838341.93221899,\n  \"cleaningPrice\": -14707224.403001845,\n  \"siteAddress\": \"do dolor\",\n  \"country\": \"ex\",\n  \"location\": {\n    \"type\": \"veniam in minim adipisicing\",\n    \"coordinates\": [\n      99529436.55837241,\n      90660545.31124923\n    ]\n  },\n  \"coverage\": -67296591.79080248,\n  \"outCoverage50KmAdditionalFee\": 27878969.*********,\n  \"outCoverage100KmAdditionalFee\": -62464415.58818794,\n  \"uniqueIdentificationNumber\": \"velit cupidatat\",\n  \"completedJobs\": -21252030.55859424\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/vehicle", "host": ["{{baseUrl}}"], "path": ["vehicle"]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"managerId\": 21282306.01418495,\n  \"operatorId\": -94575927.29686266,\n  \"companyName\": \"sint ex pariatur fugiat dolore\",\n  \"type\": -47419943.*********,\n  \"typeOfMotorization\": \"quis ea aliquip\",\n  \"licensePlateNumber\": \"velit labore eu nulla tempor\",\n  \"weight\": 80332891.72828674,\n  \"height\": -57166040.98845974,\n  \"length\": -93112371.25089553,\n  \"width\": -15142292.67397447,\n  \"verticalReach\": 85457896.47744405,\n  \"horizontalReach\": -97906265.15318328,\n  \"endHoseLength\": -39988941.10565616,\n  \"maxFlowRate\": -69206307.85287383,\n  \"maxConcretePressure\": 64484518.35089469,\n  \"availableFlexiblePipeLength80Mm\": 35082641.10977808,\n  \"availableFlexiblePipeLength90Mm\": -80179435.45114891,\n  \"availableRigidPipeLength\": 82502500.4363864,\n  \"maxDownwardReach\": -95064775.70743944,\n  \"numberOfBoomSections\": 36468527.32567075,\n  \"minUnfoldingHeight\": -91147705.43818302,\n  \"boomRotation\": 81680232.52592564,\n  \"rubberEndHoseLength\": 4655787.*********,\n  \"frontOutriggerSpan\": -47245421.33026821,\n  \"rearOutriggerSpan\": 73680155.54724261,\n  \"boomUnfoldingSystem\": \"laboris quis Lorem occaecat ea\",\n  \"bacExit\": false,\n  \"weekdaysHourlyPrice\": -21964985.637094796,\n  \"weekendHourlyPrice\": 85750254.72269946,\n  \"contractWeekdaysPrice\": 51428036.44004089,\n  \"contractWeekendPrice\": 88155190.843382,\n  \"flatWeekdaysFee\": -84043273.41026212,\n  \"flatWeekendFee\": 47537930.7102468,\n  \"pricePerMeterPumped\": 84141726.20205656,\n  \"cementBagPrice\": 94192783.07047239,\n  \"pricePerMeterOfRigidPipePlaced\": 27922769.113529935,\n  \"pricePerMeterOfFlexiblePipePlaced\": -61190740.46684765,\n  \"cleaningPrice\": 28069812.21150045,\n  \"siteAddress\": \"exercitation dolor consequat\",\n  \"country\": \"dolore mollit\",\n  \"location\": {\n    \"type\": \"minim consectetur\",\n    \"coordinates\": [\n      -74587198.55457172,\n      -60035208.43041934\n    ]\n  },\n  \"coverage\": -33298956.836164393,\n  \"outCoverage50KmAdditionalFee\": -3099751.439341873,\n  \"outCoverage100KmAdditionalFee\": -6112863.351631463,\n  \"uniqueIdentificationNumber\": \"mollit laborum veniam esse\",\n  \"completedJobs\": -23578854.057454765\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/vehicle", "host": ["{{baseUrl}}"], "path": ["vehicle"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Vehicle Controller find All", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/vehicle", "host": ["{{baseUrl}}"], "path": ["vehicle"]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/vehicle", "host": ["{{baseUrl}}"], "path": ["vehicle"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "reservation", "item": [{"name": "{id}", "item": [{"name": "Reservation Controller find One", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/reservation/:id", "host": ["{{baseUrl}}"], "path": ["reservation", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/reservation/:id", "host": ["{{baseUrl}}"], "path": ["reservation", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Reservation Controller update", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"managerId\": \"nulla deserunt elit fugiat\",\n  \"dispatcherId\": \"Ut in pa\",\n  \"vehicleId\": \"laboris\",\n  \"reservationType\": \"RESERVATION\",\n  \"operatorId\": \"esse ut\",\n  \"operatorName\": \"ea Lorem laboris\",\n  \"vehicleType\": 14278524.*********,\n  \"managerCompanyName\": \"ex ad dolore Duis\",\n  \"managerPhoneNumber\": \"\",\n  \"dispatcherPhoneNumber\": \"aliquip Du<PERSON> sit\",\n  \"dispatcherEmail\": \"ullamco nulla esse\",\n  \"dateFrom\": \"officia magna ad\",\n  \"dateTo\": \"amet\",\n  \"siteAddress\": \"amet mollit dolor Lorem\",\n  \"location\": {\n    \"type\": \"adipisicing ut est\",\n    \"coordinates\": [\n      71641470.89114964,\n      -26915485.*********\n    ]\n  },\n  \"clientDetails\": {\n    \"name\": \"cillum consectetur su\",\n    \"lastName\": \"adipisicing ullamco ea\",\n    \"email\": \"irure deserunt Lorem velit\",\n    \"phoneNumber\": \"non anim sit nostrud\",\n    \"companyName\": \"sit quis dolore\",\n    \"companyVatNumber\": \"mollit non sit eni\"\n  },\n  \"amountOfConcrete\": -44555732.32773715,\n  \"balance\": true,\n  \"extraCementBags\": 58103116.*********,\n  \"rigidPipeLength\": 71038.3403223604,\n  \"flexiblePipeLength80Mm\": 30144204.*********,\n  \"flexiblePipeLength90Mm\": -30924788.*********,\n  \"distance\": 24432539.*********,\n  \"comments\": \"aliquip dolor\",\n  \"jobStatus\": \"adipisicing\",\n  \"jobId\": \"irure et adipisicing cupidatat\",\n  \"invoiceNumber\": \"quis aliqua elit\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/reservation/:id", "host": ["{{baseUrl}}"], "path": ["reservation", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "{\n  \"managerId\": \"nulla deserunt elit fugiat\",\n  \"dispatcherId\": \"Ut in pa\",\n  \"vehicleId\": \"laboris\",\n  \"reservationType\": \"RESERVATION\",\n  \"operatorId\": \"esse ut\",\n  \"operatorName\": \"ea Lorem laboris\",\n  \"vehicleType\": 14278524.*********,\n  \"managerCompanyName\": \"ex ad dolore Duis\",\n  \"managerPhoneNumber\": \"\",\n  \"dispatcherPhoneNumber\": \"aliquip Du<PERSON> sit\",\n  \"dispatcherEmail\": \"ullamco nulla esse\",\n  \"dateFrom\": \"officia magna ad\",\n  \"dateTo\": \"amet\",\n  \"siteAddress\": \"amet mollit dolor Lorem\",\n  \"location\": {\n    \"type\": \"adipisicing ut est\",\n    \"coordinates\": [\n      71641470.89114964,\n      -26915485.*********\n    ]\n  },\n  \"clientDetails\": {\n    \"name\": \"cillum consectetur su\",\n    \"lastName\": \"adipisicing ullamco ea\",\n    \"email\": \"irure deserunt Lorem velit\",\n    \"phoneNumber\": \"non anim sit nostrud\",\n    \"companyName\": \"sit quis dolore\",\n    \"companyVatNumber\": \"mollit non sit eni\"\n  },\n  \"amountOfConcrete\": -44555732.32773715,\n  \"balance\": true,\n  \"extraCementBags\": 58103116.*********,\n  \"rigidPipeLength\": 71038.3403223604,\n  \"flexiblePipeLength80Mm\": 30144204.*********,\n  \"flexiblePipeLength90Mm\": -30924788.*********,\n  \"distance\": 24432539.*********,\n  \"comments\": \"aliquip dolor\",\n  \"jobStatus\": \"adipisicing\",\n  \"jobId\": \"irure et adipisicing cupidatat\",\n  \"invoiceNumber\": \"quis aliqua elit\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/reservation/:id", "host": ["{{baseUrl}}"], "path": ["reservation", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Reservation Controller remove", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/reservation/:id", "host": ["{{baseUrl}}"], "path": ["reservation", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/reservation/:id", "host": ["{{baseUrl}}"], "path": ["reservation", ":id"], "variable": [{"key": "id", "value": "ea dolore sunt ut", "description": "(Required) "}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "Reservation Controller create", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"managerId\": \"esse fugiat nulla\",\n  \"dispatcherId\": \"U\",\n  \"vehicleId\": \"nulla reprehenderit\",\n  \"reservationType\": \"RESERVATION\",\n  \"operatorId\": \"pariatur adipisicing\",\n  \"operatorName\": \"est nisi elit\",\n  \"vehicleType\": -91249384.40482007,\n  \"managerCompanyName\": \"adipisicing do anim\",\n  \"managerPhoneNumber\": \"proident consequat culpa commodo dolor\",\n  \"dispatcherPhoneNumber\": \"non sunt in magna aliqua\",\n  \"dispatcherEmail\": \"velit adipisicing in\",\n  \"dateFrom\": \"aliqua ullamco\",\n  \"dateTo\": \"dolore in\",\n  \"siteAddress\": \"ut in ex dolore laboris\",\n  \"location\": {\n    \"type\": \"do in quis\",\n    \"coordinates\": [\n      -22947473.36233525,\n      -85574705.84502482\n    ]\n  },\n  \"clientDetails\": {\n    \"name\": \"ex eu deserunt laboris fugiat\",\n    \"lastName\": \"in anim aliquip laborum\",\n    \"email\": \"elit tempor ipsum ut\",\n    \"phoneNumber\": \"sed aute consequat\",\n    \"companyName\": \"dolore eiusmod elit commodo\",\n    \"companyVatNumber\": \"sit elit adipisicing in\"\n  },\n  \"amountOfConcrete\": -50602764.67912064,\n  \"balance\": true,\n  \"extraCementBags\": -9128126.*********,\n  \"rigidPipeLength\": -47228364.10471874,\n  \"flexiblePipeLength80Mm\": -66841845.*********,\n  \"flexiblePipeLength90Mm\": -31937935.*********,\n  \"distance\": -4869053.*********,\n  \"comments\": \"sit labore sunt ullamco\",\n  \"jobStatus\": \"nostrud dolor exerci\",\n  \"jobId\": \"quis minim ex\",\n  \"invoiceNumber\": \"nostrud\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/reservation", "host": ["{{baseUrl}}"], "path": ["reservation"]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"managerId\": \"esse fugiat nulla\",\n  \"dispatcherId\": \"U\",\n  \"vehicleId\": \"nulla reprehenderit\",\n  \"reservationType\": \"RESERVATION\",\n  \"operatorId\": \"pariatur adipisicing\",\n  \"operatorName\": \"est nisi elit\",\n  \"vehicleType\": -91249384.40482007,\n  \"managerCompanyName\": \"adipisicing do anim\",\n  \"managerPhoneNumber\": \"proident consequat culpa commodo dolor\",\n  \"dispatcherPhoneNumber\": \"non sunt in magna aliqua\",\n  \"dispatcherEmail\": \"velit adipisicing in\",\n  \"dateFrom\": \"aliqua ullamco\",\n  \"dateTo\": \"dolore in\",\n  \"siteAddress\": \"ut in ex dolore laboris\",\n  \"location\": {\n    \"type\": \"do in quis\",\n    \"coordinates\": [\n      -22947473.36233525,\n      -85574705.84502482\n    ]\n  },\n  \"clientDetails\": {\n    \"name\": \"ex eu deserunt laboris fugiat\",\n    \"lastName\": \"in anim aliquip laborum\",\n    \"email\": \"elit tempor ipsum ut\",\n    \"phoneNumber\": \"sed aute consequat\",\n    \"companyName\": \"dolore eiusmod elit commodo\",\n    \"companyVatNumber\": \"sit elit adipisicing in\"\n  },\n  \"amountOfConcrete\": -50602764.67912064,\n  \"balance\": true,\n  \"extraCementBags\": -9128126.*********,\n  \"rigidPipeLength\": -47228364.10471874,\n  \"flexiblePipeLength80Mm\": -66841845.*********,\n  \"flexiblePipeLength90Mm\": -31937935.*********,\n  \"distance\": -4869053.*********,\n  \"comments\": \"sit labore sunt ullamco\",\n  \"jobStatus\": \"nostrud dolor exerci\",\n  \"jobId\": \"quis minim ex\",\n  \"invoiceNumber\": \"nostrud\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/reservation", "host": ["{{baseUrl}}"], "path": ["reservation"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Reservation Controller find All", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/reservation", "host": ["{{baseUrl}}"], "path": ["reservation"]}}, "response": [{"name": "Untitled Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/reservation", "host": ["{{baseUrl}}"], "path": ["reservation"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}