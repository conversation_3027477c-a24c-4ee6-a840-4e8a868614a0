import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1729105956864 implements MigrationInterface {
  name = 'Migrations1729105956864';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."company_category_enum" AS ENUM('1', '2')`);
    await queryRunner.query(
      `ALTER TABLE "company" ADD "category" "public"."company_category_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "category"`);
    await queryRunner.query(`DROP TYPE "public"."company_category_enum"`);
  }
}
