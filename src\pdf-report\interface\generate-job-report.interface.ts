import { Company } from 'src/company/entities/company.entity';
import { JobEventEnum } from 'src/job-events/enums/job-event.enum';
import { JobWithSignature } from 'src/job/v1/interfaces/jobWithSignature';
import { Reservation } from 'src/reservation/entities/reservation.entity';
import { Vehicle } from 'src/vehicle/entities/vehicle.entity';

export interface GenerateJobReport {
  job: JobWithSignature;
  vehicle: Vehicle;
  reservation: Reservation;
  company: Company;
  jobEventTimes: Record<JobEventEnum, string | undefined>;
}
