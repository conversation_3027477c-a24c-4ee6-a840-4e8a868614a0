import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';

export class CreateOperatorClientDto {
  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'name'))
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'lastName'))
  @IsOptional()
  lastName: string;

  @ApiProperty()
  @IsEmail()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'email'))
  @IsOptional()
  email: string;

  @ApiProperty()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'phoneNumber'))
  @IsOptional()
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'companyName'))
  @IsOptional()
  companyName: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'companyVatNumber'))
  @IsOptional()
  companyVatNumber: string;
}
