import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1763753189153 implements MigrationInterface {
    name = 'Migrations1763753189153'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."work_session_status_enum" AS ENUM('ACTIVE', 'ON_BREAK', 'ENDED')`);
        await queryRunner.query(`CREATE TABLE "work_session" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "operatorId" integer NOT NULL, "sessionStart" TIMESTAMP WITH TIME ZONE NOT NULL, "sessionEnd" TIMESTAMP WITH TIME ZONE, "status" "public"."work_session_status_enum" NOT NULL DEFAULT 'ACTIVE', "breaks" text, "jobIds" text, "totalWorkTime" integer, "totalBreakTime" integer, "notes" text, CONSTRAINT "PK_77af700aa2da211636470a6352b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_3e03df367f4c65eda08979e89f" ON "work_session" ("operatorId") `);
        await queryRunner.query(`CREATE INDEX "IDX_b99f94fd02bb886c0d95f94de8" ON "work_session" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_77f329f1d8877288f2340ed6ee" ON "work_session" ("operatorId", "sessionStart") `);
        await queryRunner.query(`ALTER TABLE "work_session" ADD CONSTRAINT "FK_3e03df367f4c65eda08979e89f7" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "work_session" DROP CONSTRAINT "FK_3e03df367f4c65eda08979e89f7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_77f329f1d8877288f2340ed6ee"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b99f94fd02bb886c0d95f94de8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3e03df367f4c65eda08979e89f"`);
        await queryRunner.query(`DROP TABLE "work_session"`);
        await queryRunner.query(`DROP TYPE "public"."work_session_status_enum"`);
    }

}
