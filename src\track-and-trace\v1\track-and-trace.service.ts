import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { JobService } from 'src/job/v1/job.service';
import { ReservationService } from 'src/reservation/v1/reservation.service';
import { AuthenticateDto } from '../dto/authenticate.dto';
import { ConfigService } from '@nestjs/config';
import { Job } from 'src/job/entities/job.entity';
import { SubmitSignatureDto } from '../dto/submit-signature.dto';

@Injectable()
export class TrackAndTraceService {
  private readonly logger = new Logger(TrackAndTraceService.name);
  constructor(
    private jobService: JobService,
    private reservationService: ReservationService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async authenticateOrder(body: AuthenticateDto): Promise<{ token: string }> {
    const { orderNumber, clientEmail } = body;
    this.logger.log({ method: 'authenticateOrder', orderNumber, clientEmail });

    const reservation = await this.reservationService.validateReservationExists(
      orderNumber,
      clientEmail,
    );

    if (!reservation) {
      throw new UnauthorizedException('Invalid Order Number or email!');
    }

    const payload = {
      orderNumber,
      clientEmail,
    };
    const token = await this.jwtService.signAsync(payload, {
      secret: this.configService.get('JWT_SECRET'),
      expiresIn: '12h',
    });
    return { token };
  }

  async getJobInfo(orderNumber: string) {
    this.logger.log({ method: 'getJobInfo', orderNumber });
    const reservation = await this.reservationService.fetchJobByOrderNumber(orderNumber);

    const result = {
      reservation: {
        dateFrom: reservation?.dateFrom || null,
        dateTo: reservation?.dateTo || null,
        siteAddress: reservation?.siteAddress || null,
        city: reservation?.city || null,
        plz: reservation?.plz || null,
        clientDetails: reservation?.clientDetails || null,
        vehicleUniqueId: reservation?.vehicleUniqueId || null,
        vehicle: reservation?.vehicle || null,
        location: reservation?.location || null,
        dispatcher: {
          id: reservation?.dispatcher?.id,
          phoneNumber: reservation?.dispatcher?.phoneNumber,
        },
        operator: {
          id: reservation?.operator?.id,
          phoneNumber: reservation?.operator?.phoneNumber,
        },
      },
      company: {
        name: reservation?.dispatcher?.company?.name || null,
      },
      job: reservation?.job || null,
    };

    return result;
  }

  async submitSignature(body: SubmitSignatureDto): Promise<Job> {
    const { signature, jobId } = body;
    this.logger.log({ method: 'submitSignature', jobId });

    return await this.jobService.updateSignature(jobId, signature);
  }
}
