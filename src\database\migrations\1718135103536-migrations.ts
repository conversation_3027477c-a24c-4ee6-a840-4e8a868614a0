import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1718135103536 implements MigrationInterface {
  name = 'Migrations1718135103536';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingPermitAcquired"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "localAdministrationAuthorization"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "trafficPlan"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingPermitAcquiredPath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingPermitAcquiredKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "localAdministrationAuthorizationPath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "localAdministrationAuthorizationKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "trafficPlanPath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "trafficPlanKey"`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "localAdministrationAuthorizationPath" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "localAdministrationAuthorizationKey" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ADD "trafficPlanPath" character varying`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "trafficPlanKey" character varying`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "parkingPermitAcquiredPath" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "parkingPermitAcquiredKey" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "parkingPermitAcquiredKey"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "parkingPermitAcquiredPath"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "trafficPlanKey"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "trafficPlanPath"`);
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP COLUMN "localAdministrationAuthorizationKey"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP COLUMN "localAdministrationAuthorizationPath"`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "trafficPlanKey" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "trafficPlanPath" character varying`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "localAdministrationAuthorizationKey" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD "localAdministrationAuthorizationPath" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingPermitAcquiredKey" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingPermitAcquiredPath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "trafficPlan" boolean`);
    await queryRunner.query(`ALTER TABLE "job" ADD "localAdministrationAuthorization" boolean`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "parkingPermitAcquired" boolean NOT NULL DEFAULT false`,
    );
  }
}
