import { ContractedStatus } from 'src/common/constants/user.enum';
import { SearchVehicleType } from 'src/common/types/search-vehicle.type';
import { SelectQueryBuilder } from 'typeorm';
import { Vehicle } from '../entities/vehicle.entity';

export const getVehicleQuery = async (
  queryBuilder: SelectQueryBuilder<Vehicle>,
  offset: number,
  limit: number,
  pricelistIds: number[] = [],
  includePricelist: boolean = false,
  searchType: SearchVehicleType = 'All',
  isOperatorManager: boolean = false,
  companyId?: number,
  filters?: {
    type?: string;
    boomSize?: number;
    siteAddress?: string;
    dateFrom?: Date;
    dateTo?: Date;
    operatorCompanyIds?: number[];
  },
) => {
  const { dateFrom = null, dateTo = null, operatorCompanyIds = null } = filters || {};

  queryBuilder.addSelect([
    'vehicle',
    'vehicle.imageUrl',
    'operator.id',
    'operator.firstName',
    'operator.lastName',
    'operator.country',
    'operator.phoneNumber',
    'manager.id',
    'manager.firstName',
    'manager.lastName',
    'manager.country',
    'company.id',
    'company.name',
    'company.address',
    'company.contactEmail',
    'company.phoneNumber',
  ]);

  queryBuilder
    .leftJoin('vehicle.operator', 'operator')
    .leftJoin('vehicle.manager', 'manager')
    .leftJoin('manager.company', 'company')
    .addOrderBy('vehicle.boomSize', 'ASC');

  if (includePricelist) {
    if (searchType !== 'Contracted' && !isOperatorManager) {
      if (pricelistIds.length > 0) {
        queryBuilder
          .leftJoinAndSelect(
            'vehicle.pricelists',
            'pricelists',
            'pricelists.id IN (:...pricelistIds)',
            {
              pricelistIds,
            },
          )
          .addOrderBy('pricelists.id', 'ASC', 'NULLS LAST');
      }
    } else if (isOperatorManager) {
      queryBuilder.leftJoinAndSelect('vehicle.pricelists', 'allPricelists');
      queryBuilder.leftJoinAndSelect('allPricelists.container', 'allContainer');
      queryBuilder.leftJoinAndSelect(
        'vehicle.pricelists',
        'pricelists',
        'pricelists.containerId = allContainer.id AND allContainer.companyId = :companyId',
        { companyId },
      );
      queryBuilder.leftJoinAndSelect('pricelists.container', 'container');
    } else {
      // For contracted vehicles, use an inner join to enforce that there is an approved contract.
      queryBuilder.innerJoinAndSelect(
        'vehicle.contracts',
        'contract',
        'contract.status = :approvedStatus',
        { approvedStatus: ContractedStatus.APPROVED },
      );

      if (operatorCompanyIds?.length) {
        queryBuilder.andWhere('contract.operatorCompanyId IN (:...operatorCompanyIds)', {
          operatorCompanyIds,
        });
      }
      if (companyId) {
        queryBuilder.andWhere('contract.dispatcherCompanyId = :companyId', {
          companyId,
        });
      }
      if (dateFrom && dateTo) {
        queryBuilder.andWhere(
          '(contract.startDate BETWEEN :dateFromContract AND :dateToContract OR ' +
            'contract.endDate BETWEEN :dateFromContract AND :dateToContract OR ' +
            '(contract.startDate <= :dateFromContract AND contract.endDate >= :dateToContract))',
          {
            dateFromContract: new Date(dateFrom).toISOString(),
            dateToContract: new Date(dateTo).toISOString(),
          },
        );
      } else if (dateFrom) {
        queryBuilder.andWhere(
          'contract.startDate <= :dateFromContract AND contract.endDate >= :dateFromContract',
          { dateFromContract: new Date(dateFrom).toISOString() },
        );
      } else if (dateTo) {
        queryBuilder.andWhere(
          'contract.startDate <= :dateToContract AND contract.endDate >= :dateToContract',
          { dateToContract: new Date(dateTo).toISOString() },
        );
      }

      queryBuilder.leftJoinAndSelect('contract.pricelist', 'pricelist');
    }
  }

  if (searchType == 'Partnered') {
    if (pricelistIds.length) {
      queryBuilder.andWhere('pricelists.id IN (:...pricelistIds)', {
        pricelistIds,
      });
    } else {
      const response: [Vehicle[], number] = [[], 0];
      return response;
    }
  }
  queryBuilder.skip(offset).take(limit);
  return await queryBuilder.getManyAndCount();
};
