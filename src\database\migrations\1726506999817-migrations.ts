import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1726506999817 implements MigrationInterface {
  name = 'Migrations1726506999817';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "isDefault"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_a3a88ad51cf1cc45a92bec25d26"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_a3a88ad51cf1cc45a92bec25d26" FOREIGN KEY ("parentId") REFERENCES "pricelist"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_a3a88ad51cf1cc45a92bec25d26"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_a3a88ad51cf1cc45a92bec25d26" FOREIGN KEY ("parentId") REFERENCES "pricelist"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "isDefault" boolean NOT NULL DEFAULT false`,
    );
  }
}
