import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739739110681 implements MigrationInterface {
  name = 'Migrations1739739110681';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "city" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "plz" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "plz"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "city"`);
  }
}
