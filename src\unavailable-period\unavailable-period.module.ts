import { UnavailablePeriodService } from './v1/unavailable-period.service';
import { UnavailablePeriodController } from './v1/unavailable-period.controller';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UnavailablePeriod } from './entities/unavailable-period.entity';
import { UsersModule } from 'src/users/users.module';
import { UnavailablePeriodCronService } from './unavailable-period-cron.service';
import { ReservationModule } from 'src/reservation/reservation.module';
import { VehicleModule } from 'src/vehicle/vehicle.module';
import { BullmqModule } from 'src/libs/bullmq/bullmq.module';
import { UnavailablePeriodStatusUpdateConsumer } from './workers/unavailable-period-status-update.worker';

@Module({
  imports: [
    TypeOrmModule.forFeature([UnavailablePeriod]),
    forwardRef(() => UsersModule),
    forwardRef(() => VehicleModule),
    ReservationModule,
    BullmqModule,
  ],
  controllers: [UnavailablePeriodController],
  providers: [
    UnavailablePeriodService,
    UnavailablePeriodCronService,
    UnavailablePeriodStatusUpdateConsumer,
  ],
  exports: [UnavailablePeriodService],
})
export class UnavailablePeriodModule {}
