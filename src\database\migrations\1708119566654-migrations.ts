import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708119566654 implements MigrationInterface {
  name = 'Migrations1708119566654';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."job_status_enum" AS ENUM('RUNNING', 'SUCCESS', 'FAILURE', 'CANCELLED', 'WAITING')`,
    );
    await queryRunner.query(
      `CREATE TABLE "job" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "amountOfConcrete" integer NOT NULL, "flexiblePipeLength80Mm" integer, "flexiblePipeLength90Mm" integer, "frontOutriggersSpan" integer, "rearOutriggersSpan" integer, "rigidPipeLength" integer, "extraCementBag" boolean NOT NULL DEFAULT false, "presenceOfPowerLines" boolean NOT NULL DEFAULT false, "parkingPermitAcquired" boolean NOT NULL DEFAULT false, "pipeStartingFromBAC" boolean NOT NULL DEFAULT false, "stabilization" text, "cleaningThePump" text, "comments" text, "status" "public"."job_status_enum" NOT NULL DEFAULT 'WAITING', "start" TIMESTAMP NOT NULL, "end" TIMESTAMP NOT NULL, "progress" integer NOT NULL, CONSTRAINT "PK_98ab1c14ff8d1cf80d18703b92f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "operatorName"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "vehicleType"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "managerCompanyName"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "managerPhoneNumber"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "dispatcherPhoneNumber"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "dispatcherEmail"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "amountOfConcrete"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "balance"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "extraCementBags"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "rigidPipeLength"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "flexiblePipeLength80Mm"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "flexiblePipeLength90Mm"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "distance"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "comments"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "jobStatus"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "invoiceNumber"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "operatorId"`);
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_15412881d4828fb6f32f7435f5b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_468c922064d81dd66a100f4d9c4"`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "managerId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "vehicleId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "jobId"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "jobId" integer NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "UQ_5ed0dc8f08cb6fa56feab890430" UNIQUE ("jobId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_15412881d4828fb6f32f7435f5b" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_468c922064d81dd66a100f4d9c4" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_5ed0dc8f08cb6fa56feab890430" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_5ed0dc8f08cb6fa56feab890430"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_468c922064d81dd66a100f4d9c4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_15412881d4828fb6f32f7435f5b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "UQ_5ed0dc8f08cb6fa56feab890430"`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "jobId"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "jobId" character varying`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "vehicleId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "managerId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_468c922064d81dd66a100f4d9c4" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_15412881d4828fb6f32f7435f5b" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ADD "operatorId" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "invoiceNumber" character varying`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "jobStatus" character varying`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "comments" character varying`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "distance" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "flexiblePipeLength90Mm" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "flexiblePipeLength80Mm" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "rigidPipeLength" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "extraCementBags" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "balance" boolean`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "amountOfConcrete" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "dispatcherEmail" character varying`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "dispatcherPhoneNumber" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ADD "managerPhoneNumber" character varying`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "managerCompanyName" character varying`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "vehicleType" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "operatorName" character varying`);
    await queryRunner.query(`DROP TABLE "job"`);
    await queryRunner.query(`DROP TYPE "public"."job_status_enum"`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
