import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739021266183 implements MigrationInterface {
  name = ' Migrations1739021266183';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "vehicleBrand" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "brandModel" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "brandModel"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "vehicleBrand"`);
  }
}
