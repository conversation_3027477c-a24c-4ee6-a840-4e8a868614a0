import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Req,
  VERSION_NEUTRAL,
  Delete,
  Patch,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Role } from 'src/common/constants/roles.enum';
import { Roles } from 'src/common/decorators/roles.decorator';
import { ContractService } from './contract.service';
import { CreateContractDto } from './dto/create-contract.dto';
import { GetContractDto } from './dto/get-contract.dto';
import { UpdateContractDto } from './dto/update-contract.dto';

@ApiTags('Contracts')
@Controller({ path: 'contracts', version: VERSION_NEUTRAL })
export class ContractController {
  constructor(private readonly contractService: ContractService) {}

  @Post('get')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER },
  ])
  findMany(@Body() body: GetContractDto, @Req() req) {
    return this.contractService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER },
  ])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.contractService.findOneById(+id, req?.user);
  }

  @Post()
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  requestContract(@Body() createContractDto: CreateContractDto, @Req() req) {
    return this.contractService.requestContract(createContractDto, req?.user);
  }

  @Patch(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  async update(@Param('id') id: string, @Body() updateContractDto: UpdateContractDto, @Req() req) {
    return this.contractService.update(+id, updateContractDto, req?.user?.sub);
  }

  @Post(':id/accept')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  acceptContract(
    @Param('id') id: number,
    @Body() updateContractDto: UpdateContractDto,
    @Req() req,
  ) {
    return this.contractService.acceptContract(id, updateContractDto.comment, req?.user);
  }

  @Post(':id/reject')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  rejectContract(
    @Param('id') id: number,
    @Body() updateContractDto: UpdateContractDto,
    @Req() req,
  ) {
    return this.contractService.rejectContract(id, updateContractDto.comment, req?.user);
  }

  @Post(':id/comment')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  addComment(@Param('id') id: number, @Body() updateContractDto: UpdateContractDto, @Req() req) {
    return this.contractService.addComment(id, updateContractDto.comment, req?.user);
  }

  @Post(':id/suspension-period')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  addSuspensionPeriod(
    @Param('id') id: number,
    @Body() updateContractDto: UpdateContractDto,
    @Req() req,
  ) {
    return this.contractService.addSuspensionPeriod(
      id,
      updateContractDto.suspensionStart,
      updateContractDto.suspensionEnd,
      req?.user,
    );
  }

  @Delete('suspension-period/:id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  removeSuspensionPeriodById(@Param('id') id: number, @Req() req) {
    return this.contractService.removeSuspensionPeriodById(id, req?.user);
  }

  @Delete(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  removeOneById(@Param('id') id: number, @Req() req) {
    return this.contractService.removeOneById(id, req?.user);
  }
}
