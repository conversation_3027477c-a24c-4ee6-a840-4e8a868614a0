import { InvoiceLog } from 'src/invoice-log/entities/invoice-log.entity';
import { JobWithSignature } from 'src/job/v1/interfaces/jobWithSignature';
import { Reservation } from 'src/reservation/entities/reservation.entity';

export interface JobReport {
  amountOfConcrete: number;
  cleaningTime: number;
  flexiblePipeLength80Mm: number;
  flexiblePipeLength90Mm: number;
  rigidPipeLength100Mm: number;
  rigidPipeLength120Mm: number;
  secondTechnicianMinutes: number;
  extraCementBags: boolean;
  cementBags: number;
  flow: string;
}

export interface GenerateInvoiceForManyReservation {
  reservationIds: number[];
  dispatcherCompanyId: number;
  operatorManagerId: number;
  invoiceLog: InvoiceLog;
}

export interface ReservationInvoice extends Reservation {
  job: JobWithSignature;
}

export interface DailyContractInvoice {
  date: string;
  dayContractFee: number;
  contractOvertimeFee: number;
  totalDayCost: number;
  reservations: ReservationInvoice[];
}
