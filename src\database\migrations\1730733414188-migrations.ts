import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1730733414188 implements MigrationInterface {
  name = 'Migrations1730733414188';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "hasBeenRead" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "hasBeenRead"`);
  }
}
