import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706531807962 implements MigrationInterface {
  name = 'Migrations1706531807962';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "weekdaysHourlyPrice"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "weekendHourlyPrice"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "contractWeekdaysPrice"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "contractWeekendPrice"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "flatWeekdaysFee"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "flatWeekendFee"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "pricePerMeterPumped"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "cementBagPrice"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "pricePerMeterOfRigidPipePlaced"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP COLUMN "pricePerMeterOfFlexiblePipePlaced"`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "cleaningPrice"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "coverage"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "outCoverage50KmAdditionalFee"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "outCoverage100KmAdditionalFee"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "outCoverage100KmAdditionalFee" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "outCoverage50KmAdditionalFee" integer NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "coverage" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "cleaningPrice" integer NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "pricePerMeterOfFlexiblePipePlaced" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "pricePerMeterOfRigidPipePlaced" integer NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "cementBagPrice" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "pricePerMeterPumped" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "flatWeekendFee" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "flatWeekdaysFee" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "contractWeekendPrice" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "contractWeekdaysPrice" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "weekendHourlyPrice" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "weekdaysHourlyPrice" integer NOT NULL`);
  }
}
