import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1725632164411 implements MigrationInterface {
  name = 'Migrations1725632164411';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "barbotine" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "barbotine"`);
  }
}
