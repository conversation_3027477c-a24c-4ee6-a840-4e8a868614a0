import { Injectable } from '@nestjs/common';
import { MailerService as NestMailerService } from '@nestjs-modules/mailer';
import { SendMailParams } from './interface/send-mail-params.interface';

@Injectable()
export class MailerService {
  constructor(private readonly nestMailerService: NestMailerService) {}

  async sendMail<TContext>({
    to,
    subject,
    template,
    context,
    attachments,
  }: SendMailParams<TContext>): Promise<void> {
    await this.nestMailerService.sendMail({
      to,
      subject,
      template: `./${template}`,
      context,
      attachments,
    });
  }
}
