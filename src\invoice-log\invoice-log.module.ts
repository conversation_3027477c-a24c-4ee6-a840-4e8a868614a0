import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InvoiceLog } from './entities/invoice-log.entity';
import { InvoiceLogController } from './v1/invoice-log.controller';
import { InvoiceLogService } from './v1/invoice-log.service';
import { PdfService } from 'src/pdf-report/pdf.service';
import { FilesService } from 'src/s3/files.service';
import { ReservationModule } from 'src/reservation/reservation.module';

@Module({
  imports: [TypeOrmModule.forFeature([InvoiceLog]), forwardRef(() => ReservationModule)],
  controllers: [InvoiceLogController],
  providers: [InvoiceLogService, PdfService, FilesService],
  exports: [InvoiceLogService],
})
export class InvoiceLogModule {}
