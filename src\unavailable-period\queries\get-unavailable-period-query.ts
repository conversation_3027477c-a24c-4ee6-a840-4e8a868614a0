const groupBy = `
GROUP BY
  "unavailable_period"."id"
`;

export const getUnavailablePeriodsQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations: string[] = [],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
      SELECT
        "unavailable_period".* 
        ${selects ? `, ${selects}` : ''}
      FROM "unavailable_period"
      ${joins}
      ${whereQuery}
        ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
      ${querySort}
      ${queryLimit}
    `;
};

export const getUnavailablePeriodsCountQuery = (whereQuery: string, relations: string[] = []) => {
  const { joins } = getDynamicJoins(relations);

  return `
      SELECT
        COUNT(DISTINCT "unavailable_period"."id") AS "totalCount"
      FROM "unavailable_period"
      ${joins}
      ${whereQuery}
      LIMIT 1;
    `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  operator: {
    join: `
        LEFT JOIN "user" AS "operator"
          ON "unavailable_period"."operatorId" = "operator"."id"
      `,
    select: `
        CASE WHEN "operator"."id" IS NULL THEN NULL
        ELSE jsonb_build_object(
          'id', "operator"."id",
          'firstName', "operator"."firstName",
          'lastName', "operator"."lastName",
          'email', "operator"."email",
          'status', "operator"."status"
        ) END AS "operator"
      `,
    groupBy: `"operator"."id"`,
  },

  operatorManager: {
    join: `
        LEFT JOIN "user" AS "operator_manager"
          ON "unavailable_period"."operatorManagerId" = "operator_manager"."id"
      `,
    select: `
        CASE WHEN "operator_manager"."id" IS NULL THEN NULL
        ELSE jsonb_build_object(
          'id', "operator_manager"."id",
          'firstName', "operator_manager"."firstName",
          'lastName', "operator_manager"."lastName",
          'email', "operator_manager"."email"
        ) END AS "operatorManager"
      `,
    groupBy: `"operator_manager"."id"`,
  },

  vehicles: {
    join: `
        LEFT JOIN "unavailable_period_vehicles"
          ON "unavailable_period_vehicles"."unavailablePeriodId" = "unavailable_period"."id"
        LEFT JOIN "vehicle" as "vehicles"
          ON "vehicles"."id" = "unavailable_period_vehicles"."vehicleId"
      `,

    select: `
        COALESCE(
          json_agg(
            DISTINCT jsonb_build_object(
              'id', "vehicles"."id",
              'uniqueIdentificationNumber', "vehicles"."uniqueIdentificationNumber",
              'managerId', "vehicles"."managerId",
              'operatorId', "vehicles"."operatorId"
            )
          ) FILTER (WHERE "vehicles"."id" IS NOT NULL),
          '[]'
        ) AS "vehicles"
      `,
  },

  contract: {
    join: `
        LEFT JOIN "contract" AS "contract"
          ON "unavailable_period"."contractId" = "contract"."id"
      `,
    select: `
        CASE WHEN "contract"."id" IS NULL THEN NULL
        ELSE to_jsonb(contract) END AS "contract"
      `,
    groupBy: `"contract"."id"`,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
