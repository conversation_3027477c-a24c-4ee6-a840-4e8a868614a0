import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706448750819 implements MigrationInterface {
  name = 'Migrations1706448750819';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "country" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "country"`);
  }
}
