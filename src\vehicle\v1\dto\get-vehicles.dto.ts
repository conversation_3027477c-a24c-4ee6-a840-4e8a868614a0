import { ApiProperty } from '@nestjs/swagger';
import { IsISO8601, IsN<PERSON>ber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Transform, Type } from 'class-transformer';
import { processNumber } from 'src/common/helpers/processNumbers';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';
import { SearchVehicleType } from 'src/common/types/search-vehicle.type';
import { Status } from 'src/common/constants/status.enum';

class OperatorFilter {
  @ApiProperty({ required: false })
  @IsOptional()
  readonly id?: number | null;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly firstName?: string | null;

  @ApiProperty({ required: false })
  @IsOptional()
  readonly lastName?: string | null;
}

export class GetVehiclesDto extends CommonQueryParams {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'siteAddress'))
  readonly siteAddress?: string;

  @IsOptional()
  readonly coordinates?: number[] | null;

  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  readonly radius?: number = 20000;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'type'))
  readonly type?: string;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly boomSize?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly availableFlexiblePipeLength80Mm?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly availableFlexiblePipeLength90Mm?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly availableFlexiblePipeLength100Mm?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly availableFlexiblePipeLength120Mm?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly frontOutriggerSpan?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly rearOutriggerSpan?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsISO8601()
  readonly dateFrom?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsISO8601()
  readonly dateTo?: Date;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly operatorCompanyId?: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsOptional()
  readonly operatorCompanyIds?: number[] | null;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly operatorManagerId?: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsOptional()
  readonly operatorManagerIds?: number[] | null;

  @ApiProperty({
    type: () => OperatorFilter,
    required: false,
    description: 'Object for operator filtering',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OperatorFilter)
  readonly operator?: OperatorFilter;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly operatorId?: number;

  @ApiProperty({ required: false })
  readonly searchType?: SearchVehicleType = 'All';

  @ApiProperty({ type: Status, required: false })
  @IsOptional()
  status?: Status;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsOptional()
  readonly vehicleIds?: number[] | null;
}
