import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1716289671347 implements MigrationInterface {
  name = 'Migrations1716289671347';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "country"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableRigidPipeLength"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "availableRigidPipeLength" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "country" character varying`);
  }
}
