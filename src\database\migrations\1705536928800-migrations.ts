import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1705536928800 implements MigrationInterface {
  name = 'Migrations1705536928800';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."user_status_enum" AS ENUM('1', '2', '3')`);
    await queryRunner.query(`CREATE TYPE "public"."user_role_enum" AS ENUM('1', '2', '3')`);
    await queryRunner.query(
      `CREATE TABLE "user" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "firstName" character varying NOT NULL, "lastName" character varying NOT NULL, "companyName" character varying NOT NULL, "companyAddress" character varying NOT NULL, "email" character varying NOT NULL, "password" character varying NOT NULL, "phoneNumber" character varying NOT NULL, "vatNumber" character varying NOT NULL, "status" "public"."user_status_enum" NOT NULL DEFAULT '1', "role" "public"."user_role_enum" NOT NULL DEFAULT '1', CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e12875dfb3b1d92d7d7c5377e2" ON "user" ("email") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_3d44ccf43b8a0d6b9978affb88" ON "user" ("status") `);
    await queryRunner.query(`CREATE INDEX "IDX_6620cd026ee2b231beac7cfe57" ON "user" ("role") `);
    await queryRunner.query(
      `CREATE TABLE "vehicle" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "companyName" character varying NOT NULL, "type" integer NOT NULL, "typeOfMotorization" character varying NOT NULL, "licensePlateNumber" character varying NOT NULL, "weight" integer NOT NULL, "height" integer NOT NULL, "length" integer NOT NULL, "width" integer NOT NULL, "verticalReach" integer NOT NULL, "horizontalReach" integer NOT NULL, "endHoseLength" integer NOT NULL, "maxFlowRate" integer NOT NULL, "maxConcretePressure" integer NOT NULL, "availableFlexiblePipeLength80Mm" integer NOT NULL, "availableFlexiblePipeLength90Mm" integer NOT NULL, "availableRigidPipeLength" integer NOT NULL, "maxDownwardReach" integer NOT NULL, "numberOfBoomSections" integer NOT NULL, "minUnfoldingHeight" integer NOT NULL, "boomRotation" integer NOT NULL, "rubberEndHoseLength" integer NOT NULL, "frontOutriggerSpan" integer NOT NULL, "rearOutriggerSpan" integer NOT NULL, "boomUnfoldingSystem" character varying NOT NULL, "bacExit" boolean NOT NULL, "weekdaysHourlyPrice" integer NOT NULL, "weekendHourlyPrice" integer NOT NULL, "contractWeekdaysPrice" integer NOT NULL, "contractWeekendPrice" integer NOT NULL, "flatWeekdaysFee" integer NOT NULL, "flatWeekendFee" integer NOT NULL, "pricePerMeterPumped" integer NOT NULL, "cementBagPrice" integer NOT NULL, "pricePerMeterOfRigidPipePlaced" integer NOT NULL, "pricePerMeterOfFlexiblePipePlaced" integer NOT NULL, "cleaningPrice" integer NOT NULL, "siteAddress" character varying NOT NULL, "country" character varying NOT NULL, "location" text NOT NULL, "coverage" integer NOT NULL, "outCoverage50KmAdditionalFee" integer NOT NULL, "outCoverage100KmAdditionalFee" integer NOT NULL, "uniqueIdentificationNumber" character varying NOT NULL, "completedJobs" integer NOT NULL, "managerId" integer, "operatorId" integer, CONSTRAINT "PK_187fa17ba39d367e5604b3d1ec9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_849e0cc4d98a094f3b21a01578" ON "vehicle" ("licensePlateNumber") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f9119b941e097329b73c3ce10f" ON "vehicle" ("uniqueIdentificationNumber") `,
    );
    await queryRunner.query(
      `CREATE TABLE "reservation" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" integer, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "managerId" character varying NOT NULL, "dispatcherId" character varying NOT NULL, "vehicleId" character varying NOT NULL, "operatorId" character varying NOT NULL, "operatorName" character varying NOT NULL, "vehicleType" integer NOT NULL, "managerCompanyName" character varying NOT NULL, "managerPhoneNumber" character varying NOT NULL, "dispatcherPhoneNumber" character varying NOT NULL, "dispatcherEmail" character varying NOT NULL, "dateFrom" character varying NOT NULL, "dateTo" character varying NOT NULL, "siteAddress" character varying NOT NULL, "location" text NOT NULL, "clientDetails" text NOT NULL, "amountOfConcrete" integer NOT NULL, "balance" boolean NOT NULL, "extraCementBags" integer NOT NULL, "rigidPipeLength" integer NOT NULL, "flexiblePipeLength80Mm" integer NOT NULL, "flexiblePipeLength90Mm" integer NOT NULL, "distance" integer NOT NULL, "comments" character varying NOT NULL, "jobStatus" character varying NOT NULL, "jobId" character varying NOT NULL, "invoiceNumber" character varying NOT NULL, CONSTRAINT "PK_48b1f9922368359ab88e8bfa525" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_55b219065150443755f93d2671f" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_55b219065150443755f93d2671f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188"`,
    );
    await queryRunner.query(`DROP TABLE "reservation"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f9119b941e097329b73c3ce10f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_849e0cc4d98a094f3b21a01578"`);
    await queryRunner.query(`DROP TABLE "vehicle"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6620cd026ee2b231beac7cfe57"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3d44ccf43b8a0d6b9978affb88"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e12875dfb3b1d92d7d7c5377e2"`);
    await queryRunner.query(`DROP TABLE "user"`);
    await queryRunner.query(`DROP TYPE "public"."user_role_enum"`);
    await queryRunner.query(`DROP TYPE "public"."user_status_enum"`);
  }
}
