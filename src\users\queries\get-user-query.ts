const joins = `
LEFT JOIN "company" ON "user"."companyId" = "company"."id"
LEFT JOIN "unavailable_period" ON "user"."id" = "unavailable_period"."operatorId"
`;

const groupBy = `
GROUP BY "user"."id", "company"."id"
`;

// Raw query to fetch users based on dynamic conditions
export const getUsersQuery = (whereQuery: string, querySort: string, queryLimit: string) => {
  return `
  SELECT 
    "user".*,
    CAST ("user"."role" AS text)::integer,
    CAST ("user"."status" AS text)::integer,
    CASE WHEN "company"."id" IS NULL THEN NULL ELSE jsonb_build_object('id', "company"."id", 'name', "company"."name") END AS "company",
    COALESCE(
      json_agg(
        DISTINCT jsonb_build_object(
          'id', "unavailable_period"."id",
          'from', "unavailable_period"."from",
          'to', "unavailable_period"."to"
        )
      ) FILTER (WHERE "unavailable_period"."id" IS NOT NULL),
      '[]'
    ) AS "unavailablePeriods"
  FROM "user"
  ${joins}
  ${whereQuery}
  ${groupBy}
  ${querySort}
  ${queryLimit}
  `;
};

// Query to count the total number of users (for pagination)
export const getUserCountQuery = (whereQuery: string) => {
  return `
  SELECT
    COUNT(*) OVER() AS "totalCount",
    CAST ("user"."role" AS text)::integer,
    CAST ("user"."status" AS text)::integer,
    CASE WHEN "company"."id" IS NULL THEN NULL ELSE jsonb_build_object('id', "company"."id", 'name', "company"."name") END AS "company",
    COALESCE(
      json_agg(
        DISTINCT jsonb_build_object(
          'id', "unavailable_period"."id",
          'from', "unavailable_period"."from",
          'to', "unavailable_period"."to"
        )
      ) FILTER (WHERE "unavailable_period"."id" IS NOT NULL),
      '[]'
    ) AS "unavailablePeriods"
  FROM "user"
  ${joins}
  ${whereQuery}
  ${groupBy}
  LIMIT 1;
  `;
};
