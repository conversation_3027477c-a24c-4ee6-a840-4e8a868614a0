import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { CreateOperatorClientDto } from './dto/create-operator-client.dto';
import { UpdateOperatorClientDto } from './dto/update-operator-client.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { OperatorClient } from '../entities/operator-client.entity';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import {
  getOperatorClientsQuery,
  getOperatorClientCountQuery,
} from '../queries/operator-client-raw-query';
import {
  addExpressions,
  ConditionType,
  Expression,
  getQueryLimit,
  getQuerySort,
  getWhereQuery,
  QueryOperator,
  SortDirections,
} from 'src/libs/helpers/CeQuery';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { GetOperatorClientDto, OperatorClientCategoryTypes } from './dto/get-operator-client.dto';

@Injectable()
export class OperatorClientService {
  private readonly logger = new Logger(OperatorClientService.name);

  constructor(
    @InjectRepository(OperatorClient)
    private operatorClientRepository: Repository<OperatorClient>,
    private dataSource: DataSource,
  ) {}

  async create(
    createOperatorClientDto: CreateOperatorClientDto,
    creatingUserId: number,
  ): Promise<OperatorClient> {
    this.logger.log({
      method: 'createOperatorClient',
      creatingUserId: creatingUserId,
    });

    const operatorClient = this.operatorClientRepository.create({
      ...createOperatorClientDto,
      created_by: creatingUserId,
    });
    const savedOperatorClient = await this.operatorClientRepository.save(operatorClient);

    this.logger.log({
      method: 'createOperatorClient',
      operatorClientId: savedOperatorClient.id,
    });

    return savedOperatorClient;
  }

  async findMany(
    body: GetOperatorClientDto,
    fetchingUser: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<OperatorClient>> {
    this.logger.log({ method: 'findMany' });

    const { limit, offset, sortModel = [], relations = [] } = body;

    const userId = fetchingUser?.sub;

    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [
      {
        category: '"operator_client"."created_by"',
        operator: '=' as QueryOperator,
        value: userId,
        conditionType: 'AND',
      },
    ];

    // Add default sorting if no sort model is provided
    if (!sortModel.length) {
      sortModel.push({ field: OperatorClientCategoryTypes.name, sort: SortDirections.ASC });
    }

    // Combine expressions from the request with role-specific expressions
    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    // Build the WHERE query, sort, and limit clauses
    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;

    // Generate the raw SQL query
    const operatorClientsSQL = getOperatorClientsQuery(
      whereQuery.query,
      querySort,
      queryLimit,
      relations,
    );

    try {
      let totalCount = 0;

      // Execute the raw SQL query
      const operatorClients = await queryRunner.manager.query(
        operatorClientsSQL,
        whereQuery.params,
      );

      // Fetch the total count if required
      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getOperatorClientCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({
        method: 'findMany',
        query: body,
        operatorClientsLength: operatorClients.length,
      });

      // Return the result wrapped in EntityWithCountDto
      return new EntityWithCountDto<OperatorClient>(OperatorClient, operatorClients, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser?: SerializedUser): Promise<OperatorClient | null> {
    this.logger.log({ method: 'findOneById', id });

    const body: GetOperatorClientDto = {
      expressions: [
        {
          category: '"operator_client"."id"',
          operator: '=',
          value: id,
          conditionType: 'AND',
        },
      ],
      limit: 1,
      offset: 0,
      sortModel: [],
    };

    const result = await this.findMany(body, fetchingUser, false);

    const operatorClient = result.data[0];

    if (!operatorClient) {
      this.logger.error({ method: 'findOneById', id, error: 'OperatorClient not found' });
      throw new NotFoundException(`OperatorClient with ID ${id} not found`);
    }

    this.logger.log({ method: 'findOneById', id, operatorClientId: operatorClient.id });
    return operatorClient;
  }

  async updateOneById(
    id: number,
    updateOperatorClientDto: UpdateOperatorClientDto,
    updatingUserId: SerializedUser,
  ): Promise<OperatorClient> {
    this.logger.log({
      method: 'updateOneById',
      operatorClientId: id,
      updatingUserId: updatingUserId,
    });
    const operatorClient = await this.findOneById(id, updatingUserId);

    Object.assign(operatorClient, updateOperatorClientDto);
    const savedOperatorClient = await this.operatorClientRepository.save(operatorClient);

    this.logger.log({
      method: 'updateOneById',
      operatorClientId: savedOperatorClient.id,
    });

    return savedOperatorClient;
  }

  async removeOneById(id: number): Promise<void> {
    this.logger.log({ method: 'removeOneById', id });

    const result = await this.operatorClientRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`OperatorClient with ID ${id} not found`);
    }

    this.logger.log({ method: 'removeOneById', id });
  }
}
