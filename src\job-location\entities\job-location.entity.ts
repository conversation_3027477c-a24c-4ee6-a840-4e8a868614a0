import { BaseEntity } from '../../common/entities/base.entity';
import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { JobStatus, JobState } from 'src/common/constants/job.enum';
import { Job } from 'src/job/entities/job.entity';

@Entity()
export class JobLocation extends BaseEntity {
  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.NOT_STARTED,
  })
  status: JobStatus;

  @Column({
    type: 'enum',
    enum: JobState,
    default: JobState.WAITING,
  })
  state: JobState;

  @Column({ default: 0 })
  progress: number;

  @Column({ type: 'json' })
  coords: { latitude: number; longitude: number };

  @Column()
  @Index()
  jobId: number;

  @ManyToOne(() => Job, job => job.jobLocations, { onDelete: 'CASCADE' })
  job: Job;
}
