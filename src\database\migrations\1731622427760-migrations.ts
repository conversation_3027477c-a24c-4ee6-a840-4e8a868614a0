import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1731622427760 implements MigrationInterface {
  name = 'Migrations1731622427760';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company_settings" DROP CONSTRAINT "FK_474a36aafd4ff4a422eab6a3a9d"`,
    );
    await queryRunner.query(`ALTER TABLE "company_settings" ALTER COLUMN "companyId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "company_settings" ADD CONSTRAINT "FK_474a36aafd4ff4a422eab6a3a9d" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company_settings" DROP CONSTRAINT "FK_474a36aafd4ff4a422eab6a3a9d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "company_settings" ALTER COLUMN "companyId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "company_settings" ADD CONSTRAINT "FK_474a36aafd4ff4a422eab6a3a9d" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
