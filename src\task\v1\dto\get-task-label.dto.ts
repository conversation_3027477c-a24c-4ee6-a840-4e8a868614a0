import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';

export enum TaskLabelCategoryTypes {
  id = '"task_label"."id"',
  name = '"task_label"."name"',
  color = '"task_label"."color"',
  companyId = '"task_label"."companyId"',
}

export class TaskLabelSortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: TaskLabelCategoryTypes,
  })
  @IsEnum(TaskLabelCategoryTypes, { message: 'invalid sort field' })
  field: TaskLabelCategoryTypes;
}

export class TaskLabelExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: TaskLabelCategoryTypes,
  })
  @IsOptional()
  @IsEnum(TaskLabelCategoryTypes, { message: 'invalid filter category' })
  category?: TaskLabelCategoryTypes | string | null | undefined;
}

export class GetTaskLabelDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: TaskLabelSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskLabelSortItem)
  readonly sortModel: TaskLabelSortItem[];

  @ApiProperty({ required: true, isArray: true, type: TaskLabelExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskLabelExpression)
  readonly expressions: TaskLabelExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
