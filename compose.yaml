services:
  server:
    build:
      context: ./
      dockerfile: ./Dockerfile.local
    environment:
      NODE_ENV: ${NODE_ENV}
      DB_PROVIDER: ${DB_PROVIDER}
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT}
      DB_USERNAME: ${DB_USERNAME}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_NAME: ${DB_NAME}
      DB_CERT: ${DB_CERT}
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRES_SECONDS: ${JWT_EXPIRES_SECONDS}
      USER_EMAIL: ${USER_EMAIL}
      CLIENT_ID: ${CLIENT_ID}
      CLIENT_SECRET: ${CLIENT_SECRET}
      REFRESH_TOKEN: ${REFRESH_TOKEN}
      GOOGLE_OAUTHPLAYGROUNDURL: ${GOOGLE_OAUTHPLAYGROUNDURL}
      AWS_REGION: ${AWS_REGION}
      S3_ACCESS_KEY_ID: ${S3_ACCESS_KEY_ID}
      S3_SECRET_ACCESS_KEY: ${S3_SECRET_ACCESS_KEY}
    ports:
      - 3001:3001
