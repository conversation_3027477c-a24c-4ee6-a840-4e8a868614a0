import { forwardRef, Module } from '@nestjs/common';
import { ContractService } from './v1/contract.service';
import { ContractController } from './v1/contract.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Contract } from './entities/contract.entity';
import { UsersModule } from 'src/users/users.module';
import { ContractComment } from './entities/contract-comment.entity';
import { SuspensionPeriod } from './entities/suspension-period.entity';
import { UnavailablePeriodModule } from 'src/unavailable-period/unavailable-period.module';
import { VehicleModule } from 'src/vehicle/vehicle.module';
import { PricelistModule } from 'src/pricelist/pricelist.module';
import { ReservationModule } from 'src/reservation/reservation.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Contract, ContractComment, SuspensionPeriod]),
    forwardRef(() => UsersModule),
    forwardRef(() => UnavailablePeriodModule),
    forwardRef(() => VehicleModule),
    forwardRef(() => PricelistModule),
    forwardRef(() => ReservationModule),
  ],
  controllers: [ContractController],
  providers: [ContractService],
  exports: [ContractService],
})
export class ContractModule {}
