import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNumber, IsOptional, IsBoolean, IsString } from 'class-validator';
import { processBoolean } from 'src/common/helpers/processBooleans';
import { processNumber } from 'src/common/helpers/processNumbers';

export class ReportDto {
  @ApiProperty()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  jobId: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  cleaningTime?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  amountOfConcrete?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  flexiblePipeLength80Mm?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  flexiblePipeLength90Mm?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  rigidPipeLength100Mm?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  rigidPipeLength120Mm?: number;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  extraCementBags?: boolean;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  cementBags?: number;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  secondTechnician?: boolean;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  backupPackage?: boolean;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  signature?: string;
}
