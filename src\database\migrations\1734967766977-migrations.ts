import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1734967766977 implements MigrationInterface {
  name = 'Migrations1734967766977';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
              ALTER TABLE "reservation"
              ALTER COLUMN "totalSum"
              TYPE numeric(15,2)
              USING "totalSum"::numeric(15,2);
            `);

    await queryRunner.query(`
              ALTER TABLE "bulk_invoice_log"
              ALTER COLUMN "total"
              TYPE numeric(15,2)
              USING "total"::numeric(15,2);
            `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
              ALTER TABLE "bulk_invoice_log"
              ALTER COLUMN "total"
              TYPE double precision
              USING "total"::double precision;
            `);

    await queryRunner.query(`
              ALTER TABLE "reservation"
              ALTER COLUMN "totalSum"
              TYPE double precision
              USING "totalSum"::double precision;
            `);
  }
}
