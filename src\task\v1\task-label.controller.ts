import {
  <PERSON>,
  Post,
  Body,
  Req,
  Get,
  Param,
  Patch,
  Delete,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Role } from 'src/common/constants/roles.enum';
import { Roles } from 'src/common/decorators/roles.decorator';
import { CreateTaskLabelDto } from './dto/create-task-label.dto';
import { GetTaskLabelDto } from './dto/get-task-label.dto';
import { UpdateTaskLabelDto } from './dto/update-task-label.dto';
import { TaskLabelService } from './task-label.service';

@ApiTags('TaskLabels')
@Controller({ path: 'task-labels', version: VERSION_NEUTRAL })
export class TaskLabelController {
  constructor(private readonly tasklabelService: TaskLabelService) {}

  @Post()
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async create(@Body() createTaskLabelDto: CreateTaskLabelDto, @Req() req) {
    return this.tasklabelService.create(createTaskLabelDto, req?.user);
  }

  @Post('get')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  findMany(@Body() body: GetTaskLabelDto, @Req() req) {
    return this.tasklabelService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.tasklabelService.findOneById(+id, req?.user);
  }

  @Patch(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async updateOneById(
    @Param('id') id: string,
    @Body() updateTaskLabelDto: UpdateTaskLabelDto,
    @Req() req,
  ) {
    return this.tasklabelService.updateOneById(+id, updateTaskLabelDto, req?.user);
  }

  @Delete(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  removeOneById(@Param('id') id: string, @Req() req) {
    return this.tasklabelService.removeOneById(+id, req?.user);
  }
}
