import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1724604832529 implements MigrationInterface {
  name = 'Migrations1724604832529';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "orderNumber" TYPE character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "orderNumber" TYPE character varying(12)`,
    );
  }
}
