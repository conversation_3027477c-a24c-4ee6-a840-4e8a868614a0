import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1741567901008 implements MigrationInterface {
  name = 'Migrations1741567901008';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "location_temp" geometry(Point,4326)`);
    await queryRunner.query(`
            UPDATE "vehicle"
            SET "location_temp" = ST_SetSRID(
                ST_MakePoint(
                    (location::jsonb->'coordinates'->>1)::FLOAT, -- Longitude
                    (location::jsonb->'coordinates'->>0)::FLOAT  -- Latitude
                ),
                4326
            )
            WHERE location IS NOT NULL;
        `);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "location"`);
    await queryRunner.query(`ALTER TABLE "vehicle" RENAME COLUMN "location_temp" TO "location"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "location_temp" text`);
    await queryRunner.query(`
            UPDATE "vehicle"
            SET "location_temp" = CONCAT(
                '{"type":"Point","coordinates":[',
                ST_Y(location::geometry), ',', ST_X(location::geometry), 
                ']}'
            )
            WHERE "location" IS NOT NULL;
        `);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "location"`);
    await queryRunner.query(`ALTER TABLE "vehicle" RENAME COLUMN "location_temp" TO "location"`);
  }
}
