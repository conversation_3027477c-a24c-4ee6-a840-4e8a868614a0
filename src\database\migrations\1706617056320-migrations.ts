import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706617056320 implements MigrationInterface {
  name = 'Migrations1706617056320';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "uniqueIdentificationNumber" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "completedJobs" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "completedJobs" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "uniqueIdentificationNumber" SET NOT NULL`,
    );
  }
}
