import { Company } from 'src/company/entities/company.entity';
import { Entity, Column, ManyToOne, JoinColumn, Index, OneToMany, Unique } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Task } from './task.entity';

@Entity()
@Unique(['companyId', 'color'])
export class TaskLabel extends BaseEntity {
  @Column()
  name: string;

  @Column()
  color: string;

  @Column()
  @Index()
  companyId: number;

  @ManyToOne(() => Company, company => company.tasks, { nullable: false })
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @OneToMany(() => Task, task => task.label, { cascade: true })
  tasks: Task[];
}
