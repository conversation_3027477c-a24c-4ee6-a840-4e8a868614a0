import { SelectQueryBuilder } from 'typeorm';
import { Favorite } from '../entities/favorite.entity';
import { GetFavoriteDto } from '../v1/dto/get-favorite.to';

export const favoriteQuery = async (
  queryBuilder: SelectQueryBuilder<Favorite>,
  query: GetFavoriteDto,
) => {
  const {
    limit,
    offset,
    sortBy = 'favorite.id',
    sortDir,
    dispatcherCompanyId,
    operatorCompanyIds,
  } = query;

  if (dispatcherCompanyId) {
    queryBuilder.andWhere('favorite.dispatcherCompanyId = :dispatcherCompanyId', {
      dispatcherCompanyId,
    });
  }
  if (operatorCompanyIds && operatorCompanyIds.length > 0) {
    queryBuilder.andWhere('favorite.operatorCompanyId IN (:...operatorCompanyIds)', {
      operatorCompanyIds,
    });
  }

  // Apply sorting and pagination
  return queryBuilder.orderBy(sortBy, sortDir).skip(offset).take(limit).getManyAndCount();
};
