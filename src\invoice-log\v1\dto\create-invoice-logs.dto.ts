import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsEnum, IsInt, IsNumber, IsString } from 'class-validator';
import { InvoiceStatus } from 'src/invoice-log/enums/invoice-status.enum';
import { InvoiceType } from 'src/invoice-log/enums/invoice-type.enum';

export class CreateInvoiceLogDto {
  @ApiProperty({ type: [Number], description: 'Array of reservation IDs' })
  @IsArray()
  @IsInt({ each: true })
  reservationIds: number[];

  @ApiProperty({ description: 'ID of the dispatcher company' })
  @IsNumber()
  dispatcherCompanyId: number;

  @ApiProperty({ description: 'ID of the operator company' })
  @IsNumber()
  operatorCompanyId: number;

  @ApiProperty({ description: 'Invoice Number' })
  @IsString()
  invoiceNumber: string;

  @ApiProperty({ enum: InvoiceStatus, description: 'Status' })
  @IsEnum(InvoiceStatus)
  status: InvoiceStatus;

  @ApiProperty({ enum: InvoiceType, description: 'Type' })
  @IsEnum(InvoiceType)
  type: InvoiceType;

  @ApiProperty({ description: 'Total amount for the invoice' })
  @IsNumber()
  total: number;
}
