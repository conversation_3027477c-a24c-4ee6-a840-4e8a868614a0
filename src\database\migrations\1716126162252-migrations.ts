import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1716126162252 implements MigrationInterface {
  name = 'Migrations1716126162252';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job_event" DROP CONSTRAINT "FK_deae36b9856509b82899b771112"`,
    );
    await queryRunner.query(`
        ALTER TABLE "job_event"
        ADD CONSTRAINT "FK_deae36b9856509b82899b771111" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE CASCADE;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job_event" DROP CONSTRAINT "FK_deae36b9856509b82899b771111"`,
    );
    await queryRunner.query(`
        ALTER TABLE "job_event"
        ADD CONSTRAINT "FK_deae36b9856509b82899b771112" FOREIGN KEY ("jobId") REFERENCES "job"("id");
        `);
  }
}
