import { JobState, JobStatus } from 'src/common/constants/job.enum';

export class JobUtils {
  static statusProgressMap = {
    [JobStatus.NOT_STARTED]: {
      progress: 0,
      state: JobState.WAITING,
      status: JobStatus.NOT_STARTED,
    },
    [JobStatus.DRIVING_TO_SITE]: {
      progress: 10,
      state: JobState.RUNNING,
      status: JobStatus.DRIVING_TO_SITE,
    },
    [JobStatus.SITE_ARRIVAL]: {
      progress: 20,
      state: JobState.RUNNING,
      status: JobStatus.SITE_ARRIVAL,
    },
    [JobStatus.SECURITY_VALIDATION]: {
      progress: 30,
      state: JobState.RUNNING,
      status: JobStatus.SECURITY_VALIDATION,
    },
    [JobStatus.START_SETUP]: {
      progress: 40,
      state: JobState.RUNNING,
      status: JobStatus.START_SETUP,
    },
    [JobStatus.END_SETUP]: { progress: 50, state: JobState.RUNNING, status: JobStatus.END_SETUP },
    [JobStatus.START_PUMPING]: {
      progress: 60,
      state: JobState.RUNNING,
      status: JobStatus.START_PUMPING,
    },
    [JobStatus.END_PUMPING]: {
      progress: 70,
      state: JobState.RUNNING,
      status: JobStatus.END_PUMPING,
    },
    [JobStatus.REPORT]: { progress: 80, state: JobState.RUNNING, status: JobStatus.REPORT },
    [JobStatus.SIGNATURE]: { progress: 90, state: JobState.RUNNING, status: JobStatus.SIGNATURE },
    [JobStatus.START_CLEANUP]: {
      progress: 92,
      state: JobState.RUNNING,
      status: JobStatus.START_CLEANUP,
    },
    [JobStatus.END_CLEANUP]: {
      progress: 94,
      state: JobState.RUNNING,
      status: JobStatus.END_CLEANUP,
    },
    [JobStatus.LEAVE_SITE]: { progress: 96, state: JobState.RUNNING, status: JobStatus.LEAVE_SITE },
    [JobStatus.COMPLETE]: { progress: 100, state: JobState.SUCCESS, status: JobStatus.COMPLETE },
    [JobStatus.CANCELLED]: { progress: 0, state: JobState.CANCELLED, status: JobStatus.CANCELLED },
  };

  static getStateAndProgressForStatus(status: JobStatus): {
    progress: number | null;
    state: JobState;
  } {
    return status ? this.statusProgressMap[status] : this.statusProgressMap[JobStatus.NOT_STARTED];
  }

  // Helper to format dates
  static formatDate = date => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Function to calculate duration in minutes
  static calculateDurationInMinutes = (start, end) => {
    if (!start || !end) {
      console.error(
        `Missing start or end time for duration calculation: start=${start}, end=${end}`,
      );
      return null; // Return null if either time is missing
    }
    const startDate = new Date(start);
    const endDate = new Date(end);
    return ((endDate.getTime() - startDate.getTime()) / 60000).toFixed(2);
  };
}
