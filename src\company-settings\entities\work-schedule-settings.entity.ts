import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, Index } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { CompanySettings } from './company-settings.entity';

@Entity()
export class WorkScheduleSettings extends BaseEntity {
  @Column()
  day: string;

  @Column({ type: 'time' })
  startTime: string;

  @Column({ type: 'time' })
  endTime: string;

  @Column({ nullable: true })
  @Index()
  companySettingsId: number;

  @ManyToOne(() => CompanySettings, entity => entity.workSchedules, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'companySettingsId' })
  companySettings: CompanySettings;
}
