import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1710450514983 implements MigrationInterface {
  name = 'Migrations1710450514983';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "terrainStability"`);
    await queryRunner.query(
      `CREATE TYPE "public"."job_terrainstability_enum" AS ENUM('Natural soil', 'Backfill', 'Buried network')`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD "terrainStability" "public"."job_terrainstability_enum"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "terrainStability"`);
    await queryRunner.query(`DROP TYPE "public"."job_terrainstability_enum"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "terrainStability" text`);
  }
}
