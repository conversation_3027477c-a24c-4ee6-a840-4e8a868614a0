import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { SortItem, Expression } from 'src/libs/helpers/CeQuery';

export enum OperatorClientCategoryTypes {
  id = '"operator_client"."id"',
  name = '"operator_client"."name"',
  lastName = '"operator_client"."lastName"',
  email = '"operator_client"."email"',
  phoneNumber = '"operator_client"."phoneNumber"',
  companyName = '"operator_client"."companyName"',
  companyVatNumber = '"operator_client"."companyVatNumber"',
}

export class OperatorClientSortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: OperatorClientCategoryTypes,
  })
  @IsEnum(OperatorClientCategoryTypes, { message: 'invalid sort field' })
  field: OperatorClientCategoryTypes;
}

export class OperatorClientExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: OperatorClientCategoryTypes,
  })
  @IsOptional()
  @IsEnum(OperatorClientCategoryTypes, { message: 'invalid filter category' })
  category?: OperatorClientCategoryTypes | string | null | undefined;
}
export class GetOperatorClientDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: OperatorClientSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OperatorClientSortItem)
  readonly sortModel: OperatorClientSortItem[];

  @ApiProperty({ required: true, isArray: true, type: OperatorClientExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OperatorClientExpression)
  readonly expressions: OperatorClientExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
