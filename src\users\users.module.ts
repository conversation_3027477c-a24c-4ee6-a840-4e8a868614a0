import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MailerService } from 'src/mailer/mailer.service';
import { User } from './entities/user.entity';
import { UsersService } from './v1/users.service';
import { UsersController } from './v1/users.controller';
import { CompanyModule } from 'src/company/company.module';
import { UnavailablePeriodModule } from 'src/unavailable-period/unavailable-period.module';
import { ReservationModule } from 'src/reservation/reservation.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    forwardRef(() => CompanyModule),
    forwardRef(() => UnavailablePeriodModule),
    forwardRef(() => ReservationModule),
  ],
  controllers: [UsersController],
  providers: [UsersService, MailerService],
  exports: [UsersService],
})
export class UsersModule {}
