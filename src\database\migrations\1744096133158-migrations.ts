import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1744096133158 implements MigrationInterface {
  name = 'Migrations1744096133158';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "suspension_period" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "suspensionStart" TIMESTAMP WITH TIME ZONE NOT NULL, "suspensionEnd" TIMESTAMP WITH TIME ZONE NOT NULL, "contractId" integer NOT NULL, CONSTRAINT "PK_b1bd0a4ea937fb4f89303a7cdea" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6ef7e282dc5bacd9c0fc0a979f" ON "suspension_period" ("contractId") `,
    );
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "partnerId"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "isContract" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."vehicle_status_enum" AS ENUM('1', '2', '3', '4')`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "status" "public"."vehicle_status_enum" NOT NULL DEFAULT '2'`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_62dd5af91a0d20d09486118cca" ON "contract" ("dispatcherCompanyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2d6f82992f9ea8327b9e5b8bef" ON "contract" ("operatorCompanyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b2de2e34be105312e6c1696b6e" ON "vehicle" ("status") `,
    );
    await queryRunner.query(
      `ALTER TABLE "suspension_period" ADD CONSTRAINT "FK_6ef7e282dc5bacd9c0fc0a979fb" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "suspension_period" DROP CONSTRAINT "FK_6ef7e282dc5bacd9c0fc0a979fb"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_b2de2e34be105312e6c1696b6e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2d6f82992f9ea8327b9e5b8bef"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_62dd5af91a0d20d09486118cca"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."vehicle_status_enum"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "isContract"`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "partnerId" integer`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6ef7e282dc5bacd9c0fc0a979f"`);
    await queryRunner.query(`DROP TABLE "suspension_period"`);
  }
}
