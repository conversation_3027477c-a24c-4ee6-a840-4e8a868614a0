import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';

@ValidatorConstraint({ name: 'IsStartDateBeforeOrEqualEndDate', async: false })
export class IsStartDateBeforeOrEqualEndDate implements ValidatorConstraintInterface {
  validate(startDate: string, args: ValidationArguments): boolean {
    const object = args.object as any;
    const endDate = object.endDate;
    if (!startDate || !endDate) {
      return true;
    }
    return new Date(startDate) <= new Date(endDate);
  }

  defaultMessage(): string {
    return 'startDate must be before or equal to endDate';
  }
}
