import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsNumber, ValidateNested } from 'class-validator';
import { JobState, JobStatus } from 'src/common/constants/job.enum';
import { processNumber } from 'src/common/helpers/processNumbers';

class Coordinates {
  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @Transform(({ value }) => processNumber(value))
  latitude: number;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @Transform(({ value }) => processNumber(value))
  longitude: number;
}

export class CreateJobLocationDto {
  @ApiProperty({ enum: JobStatus, enumName: 'JobStatus' })
  @IsNotEmpty()
  @IsEnum(JobStatus)
  status: JobStatus;

  @ApiProperty({ enum: JobState, enumName: 'JobState' })
  @IsNotEmpty()
  @IsEnum(JobState)
  state: JobState;

  @ApiProperty({ type: Number })
  @IsNotEmpty()
  @Transform(({ value }) => processNumber(value))
  progress: number;

  @ApiProperty({ type: Coordinates })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => Coordinates)
  coords: Coordinates;

  @ApiProperty({ type: Number })
  @IsNumber()
  jobId: number;
}
