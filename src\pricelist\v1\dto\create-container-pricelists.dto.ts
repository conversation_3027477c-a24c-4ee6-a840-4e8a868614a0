import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { processBoolean } from 'src/common/helpers/processBooleans';
import { processNumber } from 'src/common/helpers/processNumbers';

export class CreateContainerPricelistsDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  readonly title: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  readonly isPublic?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  readonly isDefault?: boolean;

  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  readonly partnerIds?: number[] | null;

  @ApiProperty()
  @Type(() => Number)
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  readonly copyFromContainerId?: number;
}
