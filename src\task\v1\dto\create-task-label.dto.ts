import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, Matches } from 'class-validator';

export class CreateTaskLabelDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    required: true,
    description: 'Label color must be a valid HEX color code (e.g., #7E57C2).',
  })
  @Matches(/^#[0-9A-Fa-f]{6}$/, {
    message: 'color must be a valid HEX color code (e.g., #7E57C2).',
  })
  color: string;
}
