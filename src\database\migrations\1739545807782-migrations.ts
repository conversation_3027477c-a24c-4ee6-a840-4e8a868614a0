import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739545807782 implements MigrationInterface {
  name = 'Migrations1739545807782';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" ADD "plz" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "plz"`);
  }
}
