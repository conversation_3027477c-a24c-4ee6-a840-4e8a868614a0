import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708857974494 implements MigrationInterface {
  name = 'Migrations1708857974494';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "adminCleanup" text`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "adminCleanup"`);
  }
}
