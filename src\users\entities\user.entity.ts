import { Exclude } from 'class-transformer';
import { Role } from '../../common/constants/roles.enum';
import { Status } from '../../common/constants/status.enum';
import { BaseEntity } from '../../common/entities/base.entity';
import { Entity, Column, Index, OneToMany, JoinColumn, ManyToOne } from 'typeorm';
import { Vehicle } from 'src/vehicle/entities/vehicle.entity';
import { UnavailablePeriod } from 'src/unavailable-period/entities/unavailable-period.entity';
import { Company } from 'src/company/entities/company.entity';
import { Task } from 'src/task/entities/task.entity';

@Entity()
export class User extends BaseEntity {
  @Column({ name: 'firstName' })
  firstName: string;

  @Column({ name: 'lastName' })
  lastName: string;

  @Index({ unique: true })
  @Column()
  email: string;

  @Column({ type: 'timestamptz', nullable: true })
  birthdate?: Date;

  @Column({ nullable: true })
  address?: string;

  @Column({ nullable: true })
  secondaryAddress?: string;

  @Column({ nullable: true })
  @Exclude()
  password: string;

  @Column()
  phoneNumber: string;

  @Column({ nullable: true })
  phoneNumberPersonal?: string;

  @Column({ nullable: true })
  country?: string;

  @Column({ nullable: true })
  city?: string;

  @Column({ nullable: true })
  zipCode?: number;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE,
  })
  @Index()
  status: number;

  @Column({
    type: 'enum',
    enum: Role,
    default: Role.OPERATOR,
  })
  @Index()
  role: number;

  @Column({ nullable: true })
  resetPasswordToken?: string | null;

  @Column({ type: 'timestamp', nullable: true })
  resetPasswordTokenExpiresAt?: Date | null;

  @Column({ default: 5 })
  remainingAttempts: number;

  @Column({ type: 'timestamp', nullable: true })
  lockoutEndTime?: Date | null;

  @OneToMany(() => Vehicle, vehicle => vehicle.operator)
  operatorVehicles?: Vehicle[] | null;

  @OneToMany(() => Vehicle, vehicle => vehicle.manager)
  managerVehicles?: Vehicle[] | null;

  @OneToMany(() => UnavailablePeriod, unavailablePeriod => unavailablePeriod.operatorManager)
  operatorManagerUnavailablePeriods: UnavailablePeriod[];

  @OneToMany(() => UnavailablePeriod, unavailablePeriod => unavailablePeriod.operator)
  operatorUnavailablePeriods: UnavailablePeriod[];

  @Column({ nullable: true })
  @Index()
  companyId: number;

  @ManyToOne(() => Company, entity => entity.users, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'companyId' })
  company?: Company;

  @OneToMany(() => Task, task => task.assignee)
  tasks: Task[];

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  totalWorkingHours?: number;
}
