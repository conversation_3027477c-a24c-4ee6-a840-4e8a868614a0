import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '../../common/entities/base.entity';
import { User } from '../../users/entities/user.entity';
import { Location, Vehicle } from '../../vehicle/entities/vehicle.entity';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToOne } from 'typeorm';
import { Job } from 'src/job/entities/job.entity';
import { Pricelist } from 'src/pricelist/entities/pricelist.entity';

export class ClientDetails {
  @ApiProperty()
  name: string;

  @ApiProperty()
  lastName: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  phoneNumber: string;

  @ApiProperty()
  companyName: string;

  @ApiProperty()
  companyVatNumber: string;
}

export enum ReservationType {
  RESERVATION = 'RESERVATION',
  UNAVAILABLE = 'UNAVAILABLE',
}

@Entity()
@Index(['dateFrom', 'dateTo'])
export class Reservation extends BaseEntity {
  @Column({ type: 'timestamptz', nullable: true })
  dateFrom: Date;

  @Column({ type: 'timestamptz', nullable: true })
  dateTo: Date;

  @Column({ nullable: true })
  siteAddress: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  plz: number;

  @Column('simple-json')
  location: Location;

  @Column('simple-json', { nullable: true })
  clientDetails: ClientDetails;

  @Column({
    type: 'enum',
    enum: ReservationType,
    default: ReservationType.RESERVATION,
  })
  @Index()
  reservationType: string;

  @Column({
    unique: true,
  })
  @Index()
  orderNumber: string;

  @Column({ nullable: true })
  localAdministrationAuthorizationKey?: string;

  @Column({ nullable: true })
  trafficPlanKey: string;

  @Column({ nullable: true })
  parkingPermitAcquiredKey: string;

  @Column({ default: false })
  invoiced: boolean;

  @Column({ nullable: true })
  invoiceNumber?: string;

  @Column({ nullable: true, type: 'numeric', precision: 15, scale: 2 })
  totalSum?: number;

  @Column({ default: false })
  hasBeenRead: boolean;

  @Column({ nullable: true })
  @Index()
  managerId?: number;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'managerId' })
  manager?: User;

  @Column({ nullable: true })
  @Index()
  operatorId?: number;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'operatorId' })
  operator?: User;

  @Column({ nullable: true })
  @Index()
  dispatcherId?: number;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'dispatcherId' })
  dispatcher?: User;

  @Column({ nullable: true })
  vehicleUniqueId?: string;

  @Column({ nullable: true })
  @Index()
  vehicleId?: number;

  @ManyToOne(() => Vehicle, vehicle => vehicle.reservations, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'vehicleId' })
  vehicle?: Vehicle;

  @Column({ nullable: true })
  @Index()
  jobId?: number;

  @OneToOne(() => Job, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'jobId' })
  job?: Job;

  @Column({ nullable: true })
  pricelistId?: number;

  @ManyToOne(() => Pricelist, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'pricelistId' })
  pricelist?: Pricelist;
}
