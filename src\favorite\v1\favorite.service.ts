import { forwardRef, HttpException, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { Favorite } from '../entities/favorite.entity';
import { GetFavoriteDto } from './dto/get-favorite.to';
import { favoriteQuery } from '../queries/favoriteQuery';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { CompanyService } from 'src/company/v1/company.service';
import { CreateFavoriteDto } from './dto/create-favorite.dto';

@Injectable()
export class FavoriteService {
  private readonly logger = new Logger(FavoriteService.name);
  constructor(
    @InjectRepository(Favorite)
    private readonly favoriteRepository: Repository<Favorite>,
    @Inject(forwardRef(() => CompanyService))
    private readonly companyService: CompanyService,
  ) {}

  async findMany(query: GetFavoriteDto): Promise<EntityWithCountDto<Favorite>> {
    this.logger.log({ method: 'findMany', query });

    const queryBuilder: SelectQueryBuilder<Favorite> =
      this.favoriteRepository.createQueryBuilder('favorite');
    queryBuilder.leftJoinAndSelect('favorite.operatorCompany', 'operatorCompany');

    const [favorites, totalCount] = await favoriteQuery(queryBuilder, query);

    this.logger.log({ method: 'findMany', favorites, totalCount });
    return new EntityWithCountDto<Favorite>(Favorite, favorites, totalCount);
  }
  async favoriteCompany(createFavoriteDto: CreateFavoriteDto): Promise<Favorite> {
    this.logger.log({ method: 'favoriteCompany', createFavoriteDto });
    const { dispatcherCompanyId, operatorCompanyId } = createFavoriteDto;
    const dispatcherCompany = await this.companyService.findOneById(dispatcherCompanyId);
    const operatorCompany = await this.companyService.findOneById(operatorCompanyId);

    const existingFavorite = await this.favoriteRepository.findOne({
      where: { dispatcherCompanyId, operatorCompanyId },
    });

    if (existingFavorite) {
      throw new HttpException('This company is already favorited!', HttpStatus.BAD_REQUEST);
    }

    const favorite = this.favoriteRepository.create({ dispatcherCompany, operatorCompany });
    this.logger.log({ method: 'favoriteCompany', favorite });
    return this.favoriteRepository.save(favorite);
  }

  async unFavoriteCompany(createFavoriteDto: CreateFavoriteDto): Promise<void> {
    this.logger.log({ method: 'unFavoriteCompany', createFavoriteDto });
    const { dispatcherCompanyId, operatorCompanyId } = createFavoriteDto;
    const favorite = await this.favoriteRepository.findOne({
      where: {
        dispatcherCompanyId,
        operatorCompanyId,
      },
    });

    if (!favorite) {
      throw new Error('Favorite entry not found');
    }
    await this.favoriteRepository.delete(favorite.id);
  }
}
