import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708381469997 implements MigrationInterface {
  name = 'Migrations1708381469997';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "from"`);
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD "from" TIMESTAMP WITH TIME ZONE NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "to"`);
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD "to" TIMESTAMP WITH TIME ZONE NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "dateFrom"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "dateFrom" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "dateTo"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "dateTo" TIMESTAMP WITH TIME ZONE`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "dateTo"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "dateTo" character varying`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "dateFrom"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "dateFrom" character varying`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "to"`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "to" TIMESTAMP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "from"`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "from" TIMESTAMP NOT NULL`);
  }
}
