import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1721976554316 implements MigrationInterface {
  name = 'Migrations1721976554316';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "invoiced" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "invoiced"`);
  }
}
