import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';

export enum ContractCategoryTypes {
  id = '"contract"."id"',
  startDate = '"contract"."startDate"',
  endDate = '"contract"."endDate"',
  dispatcherCompanyId = '"contract"."dispatcherCompanyId"',
  operatorCompanyId = '"contract"."operatorCompanyId"',
  status = '"contract"."status"',
  pricelistId = '"contract"."pricelistId"',
  vehicleId = '"contract"."vehicleId"',
  suspensionStart = '"suspensionPeriods"."suspensionStart"',
  suspensionEnd = '"suspensionPeriods"."suspensionEnd"',
}

export class ContractSortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: ContractCategoryTypes,
  })
  @IsEnum(ContractCategoryTypes, { message: 'invalid sort field' })
  field: ContractCategoryTypes;
}

export class ContractExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: ContractCategoryTypes,
  })
  @IsOptional()
  @IsEnum(ContractCategoryTypes, { message: 'invalid filter category' })
  category?: ContractCategoryTypes | string | null | undefined;
}
export class GetContractDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: ContractSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContractSortItem)
  readonly sortModel: ContractSortItem[];

  @ApiProperty({ required: true, isArray: true, type: ContractExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContractExpression)
  readonly expressions: ContractExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
