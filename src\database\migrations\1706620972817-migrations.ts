import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706620972817 implements MigrationInterface {
  name = 'Migrations1706620972817';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188"`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "managerId"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "managerId" integer`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
