import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompanySettingsModule } from 'src/company-settings/company-settings.module';
import { Company } from './entities/company.entity';
import { CompanyController } from './v1/company.controller';
import { CompanyService } from './v1/company.service';
import { VehicleModule } from 'src/vehicle/vehicle.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Company]),
    forwardRef(() => CompanySettingsModule),
    forwardRef(() => VehicleModule),
  ],
  controllers: [CompanyController],
  providers: [CompanyService],
  exports: [CompanyService],
})
export class CompanyModule {}
