import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1719264703505 implements MigrationInterface {
  name = 'Migrations1719264703505';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" ADD "vehicleUniqueId" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "vehicleUniqueId"`);
  }
}
