import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { GetVehiclesDto } from './get-vehicles.dto';
import { processNumber } from 'src/common/helpers/processNumbers';
import { Transform } from 'class-transformer';

export class GetContractedVehiclesDto extends GetVehiclesDto {
  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  readonly dispatcherCompanyId?: number | null;
}
