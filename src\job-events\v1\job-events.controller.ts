import { Controller, Get, Post, Body, Param, Delete, Put, Req } from '@nestjs/common';
import { JobEventsService } from './job-events.service';
import { Role } from 'src/common/constants/roles.enum';
import { Roles } from 'src/common/decorators/roles.decorator';
import { CreateJobEventsDto } from './dto/create-job-events.dto';
import { UpdateJobEventsDto } from './dto/update-job-events.dto';
import { ApiTags } from '@nestjs/swagger';
import { GetJobEventsDto } from './dto/get-job-events.dto';

@ApiTags('JobEvents')
@Controller('job-events')
export class JobEventsController {
  constructor(private readonly jobEventsService: JobEventsService) {}

  @Post()
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  create(@Body() createJobEventsDto: CreateJobEventsDto, @Req() req) {
    return this.jobEventsService.create(createJobEventsDto, req?.user?.sub);
  }

  @Post('get')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  findMany(@Body() body: GetJobEventsDto) {
    return this.jobEventsService.findMany(body);
  }

  @Get(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  findOne(@Param('id') id: string) {
    return this.jobEventsService.findOneById(+id);
  }

  @Put(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  update(@Param('id') id: string, @Body() updatejobEventsDto: UpdateJobEventsDto, @Req() req) {
    return this.jobEventsService.update(+id, updatejobEventsDto, req?.user?.sub);
  }

  @Delete(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  remove(@Param('id') id: string) {
    return this.jobEventsService.remove(+id);
  }
}
