const groupBy = `
GROUP BY
  "task"."id"
`;

export const getTasksQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT 
      "task".*
      ${selects ? `, ${selects}` : ''}
    FROM "task"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getTasksCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "task"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  assignee: {
    join: `LEFT JOIN "user" AS "user" ON "task"."assigneeId" = "user"."id"`,
    select: `jsonb_build_object(
      'id', "user"."id",
      'firstName', "user"."firstName",
      'lastName', "user"."lastName",
      'email', "user"."email"
    ) AS "assignee"`,
    groupBy: `"user"."id"`,
  },
  company: {
    join: `LEFT JOIN "company" AS "company" ON "task"."companyId" = "company"."id"`,
    select: `jsonb_build_object(
      'id', "company"."id",
      'name', "company"."name"
    ) AS "company"`,
    groupBy: `"company"."id"`,
  },
  activities: {
    join: `LEFT JOIN "task_activity" as "activities" ON "task"."id" = "activities"."taskId"`,
    select: `COALESCE(json_agg(DISTINCT jsonb_build_object(
      'id', "activities"."id",
      'actionType', "activities"."actionType",
      'taskId', "activities"."taskId",
      'oldValue', "activities"."oldValue"
    )) FILTER (WHERE "activities"."id" IS NOT NULL), '[]') AS "activities"`,
  },
  label: {
    join: `LEFT JOIN "task_label" AS "label" ON "task"."labelId" = "label"."id"`,
    select: `jsonb_build_object(
      'id', "label"."id",
      'name', "label"."name",
      'companyId', "label"."companyId",
      'color', "label"."color"
    ) AS "label"`,
    groupBy: `"label"."id"`,
  },
  comments: {
    join: `LEFT JOIN "task_comment" AS "comments" ON "task"."id" = "comments"."taskId"`,
    select: `COALESCE(json_agg(DISTINCT jsonb_build_object(
      'id', "comments"."id",
      'taskId', "comments"."taskId",
      'content', "comments"."content"
    )) FILTER (WHERE "comments"."id" IS NOT NULL), '[]') AS "comments"`,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
