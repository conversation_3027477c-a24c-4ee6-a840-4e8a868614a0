import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { config as dotenvConfig } from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';

dotenvConfig({ path: '.env' });

const config: TypeOrmModuleOptions = {
  type: 'postgres',
  host: `${process.env.DB_HOST}`,
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  username: `${process.env.DB_USERNAME}`,
  password: process.env.DB_PASSWORD || 'password',
  database: `${process.env.DB_NAME}`,
  entities: ['dist/**/*.entity{.ts,.js}'],
  migrations: ['dist/database/migrations/*{.ts,.js}'],
  migrationsTableName: 'migration_table',
  autoLoadEntities: true,
  synchronize: false,
  migrationsRun: true,
  poolSize: 10,
  ssl: process.env.DB_CERT
    ? {
        rejectUnauthorized: true,
        ca: process.env.DB_CERT,
      }
    : false,
};

export default registerAs('typeorm', () => config);
export const connectionSource = new DataSource(config as DataSourceOptions);
