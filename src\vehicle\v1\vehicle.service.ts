import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Vehicle } from '../entities/vehicle.entity';
import { DataSource, Geometry, In, Repository, SelectQueryBuilder } from 'typeorm';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { UsersService } from 'src/users/v1/users.service';
import { GetVehiclesDto } from 'src/vehicle/v1/dto/get-vehicles.dto';
import { ReservationService } from 'src/reservation/v1/reservation.service';
import { JobState } from 'src/common/constants/job.enum';
import { getVehicleFiltersQuery } from '../queries/vehicleFiltersQuery';
import { getOperatorManagerQuery } from '../queries/operatorManagerQuery';
import { getVehicleAvailabilityQuery } from '../queries/vehicleAvailabilityQuery';
import { getVehicleQuery } from '../queries/vehicleQuery';
import { CheckVehicleSlotAvailabilityDto } from './dto/check-vehicle-slot-availability.dto';
import { FavoriteService } from 'src/favorite/v1/favorite.service';
import { GetContractedVehiclesDto } from './dto/get-contracted-vehicles.dto';
import { GetFavoriteVehiclesDto } from './dto/get-favorite-vehicles.dto';
import { getOperatorManagersQuery } from '../queries/operatorManagersQuery';
import { GetFavoriteDto } from 'src/favorite/v1/dto/get-favorite.to';
import { VehicleUtils } from '../helpers/vehicle.utils';
import { GetVehiclesSearchDto } from './dto/get-vehicles-search.dto';
import { applySearch } from 'src/common/helpers/termSearch';
import { CompanyService } from 'src/company/v1/company.service';
import { VehicleResponse } from './interfaces/get-vehicles.interface';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { getOperatorCompaniesQuery } from '../queries/operatorCompaniesQuery';
import { getVehiclesReservationsQuery } from '../queries/vehiclesReservationsQuery';
import { VehicleReservationsResponse } from './interfaces/get-vehicles-reservations.interface';
import { getHiddenVehicleQuery } from '../queries/hiddenVehicleQuery';
import { PartnerService } from 'src/partner/v1/partner.service';
import { PartnerCategoryTypes } from 'src/partner/v1/dto/get-partner.dto';
import { Reservation } from 'src/reservation/entities/reservation.entity';
import { UnavailablePeriodService } from 'src/unavailable-period/v1/unavailable-period.service';
import { Status } from 'src/common/constants/status.enum';
import { ContractService } from 'src/contract/v1/contract.service';
import { ContractCategoryTypes, ContractExpression } from 'src/contract/v1/dto/get-contract.dto';
import { ContractedStatus } from 'src/common/constants/user.enum';
import { FilesService } from 'src/s3/files.service';

@Injectable()
export class VehicleService {
  private readonly logger = new Logger(VehicleService.name);

  constructor(
    @InjectRepository(Vehicle)
    private vehicleRepository: Repository<Vehicle>,
    @Inject(forwardRef(() => UsersService))
    private usersService: UsersService,
    @Inject(forwardRef(() => ReservationService))
    private reservationService: ReservationService,
    private dataSource: DataSource,
    private partnerService: PartnerService,
    private favoriteService: FavoriteService,
    @Inject(forwardRef(() => CompanyService))
    private companyService: CompanyService,
    @Inject(forwardRef(() => UnavailablePeriodService))
    private unavailablePeriodService: UnavailablePeriodService,
    private contractService: ContractService,
    private filesService: FilesService,
  ) {}

  async create(createVehicleDto: CreateVehicleDto, creatingUserId: number) {
    this.logger.log({
      method: 'createVehicle',
      creatingUser: creatingUserId,
    });

    if (createVehicleDto.status === undefined || createVehicleDto.status === null) {
      createVehicleDto.status = Status.ACTIVE;
    }

    const manager = await this.usersService.findOneById(creatingUserId);

    const operator = createVehicleDto.operatorId
      ? await this.usersService.findOneById(createVehicleDto.operatorId)
      : null;

    const vehicle = this.vehicleRepository.create({
      ...createVehicleDto,
      created_by: creatingUserId,
      location: createVehicleDto.location as Geometry,
    });

    vehicle.manager = manager;
    vehicle.operator = operator;

    // Generate unique ID
    vehicle.uniqueIdentificationNumber = VehicleUtils.getVehicleUniqueId(vehicle);

    const savedVehicle = this.vehicleRepository.save(vehicle);

    this.logger.log({ method: 'createVehicle', vehicleId: vehicle.id });
    return savedVehicle;
  }

  async findManySearch(
    query: GetVehiclesSearchDto,
    searchingUser?: SerializedUser,
  ): Promise<EntityWithCountDto<Vehicle>> {
    this.logger.log({ method: 'findManyVehicles' });
    const { searchType, ...attrs } = query;

    let result: EntityWithCountDto<Vehicle>;
    switch (searchType) {
      case 'All': {
        const {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          dispatcherCompanyId,
          ...vehicleAttrs
        } = attrs;

        const vehicleQuery: GetVehiclesDto = {
          ...vehicleAttrs,
          status: Status.ACTIVE,
        };
        result = await this.findMany(vehicleQuery, searchingUser);
        this.logger.log({ method: 'findManyVehicles', vehiclesLength: result.totalCount });
        break;
      }
      case 'Partnered': {
        result = await this.getPartneredVehicles(query, searchingUser);
        this.logger.log({
          method: 'findManyVehicles',
          partneredVehiclesLength: result.totalCount,
        });
        break;
      }
      case 'Contracted': {
        result = await this.getContractedVehicles(query, searchingUser);
        this.logger.log({
          method: 'findManyVehicles',
          contractedVehiclesLength: result.totalCount,
        });
        break;
      }
      case 'Favorited': {
        result = await this.getFavoritedVehicles(query, searchingUser);
        this.logger.log({
          method: 'findManyVehicles',
          favoritedVehiclesLength: result.totalCount,
        });
        break;
      }

      default:
        return new EntityWithCountDto<Vehicle>(Vehicle, [], 0);
    }

    await this.addPresignedUrlsToVehicles(result.data);
    return result;
  }

  async findMany(
    query: GetVehiclesDto,
    searchingUser?: SerializedUser,
  ): Promise<EntityWithCountDto<VehicleResponse>> {
    this.logger.log({ method: 'findManyVehicles' });
    const roleId = searchingUser?.roleId;
    const companyId = searchingUser?.companyId;
    const searchingUserId = searchingUser?.sub;
    const isOperatorManager = roleId === Role.OPERATOR_MANAGER;
    const {
      limit,
      offset,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      sortBy = 'vehicle.boomSize',
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      sortDir,
      operatorManagerId,
      operatorManagerIds,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      operatorCompanyIds,
      dateFrom,
      dateTo,
      searchText,
      searchType,
      coordinates,
      radius,
      status,
      vehicleIds,
      ...filters
    } = query;

    const queryBuilder = this.dataSource.getRepository(Vehicle).createQueryBuilder('vehicle');

    getHiddenVehicleQuery(queryBuilder, roleId, dateFrom, dateTo);

    // Add WHERE conditions from filters
    getVehicleFiltersQuery(queryBuilder, filters);

    if (operatorCompanyIds && operatorCompanyIds.length > 0) {
      getOperatorCompaniesQuery(queryBuilder, operatorCompanyIds);
    }
    if (operatorManagerIds && operatorManagerIds.length > 0) {
      getOperatorManagersQuery(queryBuilder, operatorManagerIds);
    }
    if (operatorManagerId !== undefined && operatorManagerId !== null) {
      getOperatorManagerQuery(queryBuilder, operatorManagerId);
    }
    if (searchType != 'Contracted' && dateFrom && dateTo) {
      getVehicleAvailabilityQuery(queryBuilder, dateFrom, dateTo);
    }
    if (status) {
      queryBuilder.andWhere('vehicle.status = :status', { status: Status.ACTIVE });
    }
    if (searchText) {
      applySearch(queryBuilder, searchText, [
        'vehicle.uniqueIdentificationNumber',
        'operator.firstName',
        'operator.lastName',
      ]);
    }
    if (vehicleIds) {
      queryBuilder.andWhere('vehicle.id IN (:...vehicleIds)', {
        vehicleIds,
      });
    }

    this.applyLocationFilter(queryBuilder, coordinates, radius);

    let vehiclesResponse: VehicleResponse[];
    let totalCountResponse: number;
    if (roleId === Role.DISPATCHER || roleId === Role.DISPATCHER_MANAGER) {
      let variantPricelistIds: number[] = [];

      const dispatcher = await this.usersService.findOneById(searchingUserId);
      if (dispatcher && dispatcher.company && searchType != 'Contracted') {
        const company = await this.companyService.findOneDispatcherWithPartners(
          dispatcher.company.id,
        );
        variantPricelistIds = company.dispatcherPartners.flatMap(
          partner =>
            partner.containerPricelists?.flatMap(
              container => container.pricelists?.map(pricelist => pricelist.id) || [],
            ) || [],
        );
      }

      const [vehicles, totalCount] = await getVehicleQuery(
        queryBuilder,
        offset,
        limit,
        variantPricelistIds,
        true,
        searchType,
        null,
        null,
        { dateFrom, dateTo, operatorCompanyIds }, // filters
      );

      totalCountResponse = totalCount;
      vehiclesResponse = this.filterVehiclePricelists(vehicles as VehicleResponse[]);
    } else if (isOperatorManager) {
      const [vehicles, totalCount] = await getVehicleQuery(
        queryBuilder,
        offset,
        limit,
        [],
        true,
        searchType,
        isOperatorManager,
        companyId,
      );
      totalCountResponse = totalCount;
      vehiclesResponse = this.filterVehiclePricelists(
        vehicles as VehicleResponse[],
        [],
        isOperatorManager,
      );
    } else {
      const [vehicles, totalCount] = await getVehicleQuery(queryBuilder, offset, limit);
      totalCountResponse = totalCount;
      vehiclesResponse = vehicles as VehicleResponse[];
    }

    this.logger.log({ method: 'findManyVehicles', query, vehiclesLength: vehiclesResponse.length });
    return new EntityWithCountDto<VehicleResponse>(Vehicle, vehiclesResponse, totalCountResponse);
  }

  async findManyWithReservations(
    query: GetVehiclesSearchDto,
    searchingUser?: SerializedUser,
  ): Promise<EntityWithCountDto<VehicleReservationsResponse>> {
    this.logger.log({ method: 'findManyVehiclesWithReservation', query });
    const {
      searchType,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      dispatcherCompanyId,
      coordinates,
      radius,
      operatorName,
      clientName,
      ...queryAttributes
    } = query;
    const userCompanyId = searchingUser?.companyId;
    const isDispatcher = [Role.DISPATCHER, Role.DISPATCHER_MANAGER].includes(searchingUser.roleId)
      ? true
      : false;
    // Create the base query
    const queryBuilder = this.dataSource.getRepository(Vehicle).createQueryBuilder('vehicle');

    if (searchType !== 'All' && !queryAttributes.operatorCompanyIds?.length) {
      return new EntityWithCountDto<VehicleReservationsResponse>(Vehicle, [], 0);
    }

    this.applyLocationFilter(queryBuilder, coordinates, radius);

    let pricelistIds: number[] = [];
    if (isDispatcher) {
      if (searchType != 'Contracted') {
        const company = await this.companyService.findOneWithPartners(userCompanyId);
        pricelistIds = company.dispatcherPartners.flatMap(
          partner =>
            partner.containerPricelists?.flatMap(
              container => container.pricelists?.map(pricelist => pricelist.id) || [],
            ) || [],
        );
      }
    }

    // Apply filters and sorting in the query
    const [vehicles, totalCount] = await getVehiclesReservationsQuery(
      queryBuilder,
      searchingUser.sub,
      { ...queryAttributes, operatorName, clientName },
      pricelistIds,
      userCompanyId,
      searchType,
      isDispatcher,
      searchingUser.roleId,
    );

    this.logger.log({
      method: 'findManyVehiclesWithReservation',
      vehiclesLength: totalCount,
      query,
    });

    return new EntityWithCountDto<VehicleReservationsResponse>(Vehicle, vehicles, totalCount);
  }

  async findOne(id: number, searchingUser?: SerializedUser): Promise<VehicleResponse> {
    this.logger.log({ method: 'findOneVehicle', id });
    const roleId = searchingUser?.roleId;
    const userCompanyId = searchingUser?.companyId;
    const isDispatcher = [Role.DISPATCHER, Role.DISPATCHER_MANAGER].includes(roleId);
    const isOperatorManager = roleId === Role.OPERATOR_MANAGER;

    let pricelistIds: number[] = [];
    if (isDispatcher) {
      const company = await this.companyService.findOneWithPartners(userCompanyId);
      pricelistIds = company.dispatcherPartners.flatMap(
        partner =>
          partner.containerPricelists?.flatMap(
            container => container.pricelists?.map(pricelist => pricelist.id) || [],
          ) || [],
      );
    }

    const queryBuilder = this.vehicleRepository.createQueryBuilder('vehicle');
    queryBuilder
      .where('vehicle.id = :id', { id })
      .leftJoinAndSelect('vehicle.operator', 'operator')
      .leftJoinAndSelect('vehicle.manager', 'manager')
      .leftJoinAndSelect('manager.company', 'company');

    if (pricelistIds.length > 0) {
      queryBuilder.leftJoinAndSelect('vehicle.pricelists', 'pricelists');
    } else if (isOperatorManager) {
      queryBuilder.leftJoinAndSelect('vehicle.pricelists', 'allPricelists');
      queryBuilder.leftJoinAndSelect('allPricelists.container', 'allContainer');
      queryBuilder.leftJoinAndSelect(
        'vehicle.pricelists',
        'pricelists',
        'pricelists.containerId = allContainer.id AND allContainer.companyId = :userCompanyId',
        { userCompanyId },
      );
      queryBuilder.leftJoinAndSelect('pricelists.container', 'container');
    }

    const now = new Date().toISOString();

    queryBuilder.leftJoinAndSelect(
      'vehicle.contracts',
      'contract',
      `(contract.status = :approvedStatus AND 
        contract.startDate <= :now AND 
        contract.endDate >= :now AND 
        contract.dispatcherCompanyId = :companyId)`,
      { approvedStatus: ContractedStatus.APPROVED, now, companyId: userCompanyId },
    );

    queryBuilder.leftJoinAndSelect('contract.pricelist', 'pricelist');

    const vehicle = await queryBuilder.getOne();

    if (!vehicle) {
      throw new NotFoundException('Vehicle was not found.');
    }

    this.filterVehiclePricelists([vehicle] as VehicleResponse[], pricelistIds, isOperatorManager);
    this.logger.log({ method: 'findOneVehicle', id, vehicleId: vehicle.id });
    return vehicle;
  }

  async checkVehicleAvailability(checkVehicleSlotAvailabilityDto: CheckVehicleSlotAvailabilityDto) {
    this.logger.log({
      method: 'checkVehicleAvailability',
      checkVehicleSlotAvailabilityDto,
    });

    const { id, dateFrom, dateTo, currentReservationId } = checkVehicleSlotAvailabilityDto;

    const queryBuilder = this.vehicleRepository
      .createQueryBuilder('vehicle')
      .where('vehicle.id = :id', { id: id });

    getVehicleAvailabilityQuery(queryBuilder, dateFrom, dateTo, currentReservationId);

    const availableVehicle = await queryBuilder.getOne();

    return !!availableVehicle;
  }

  async findManyByIds(ids: number[]): Promise<Vehicle[]> {
    this.logger.log({ method: 'findManyByIds', ids });

    const vehicles = await this.vehicleRepository.findBy({ id: In(ids) });

    this.logger.log({
      method: 'findManyByIds',
      ids,
      vehiclesFound: vehicles.length,
    });
    return vehicles;
  }

  async update(
    id: number,
    updateVehicleDto: UpdateVehicleDto,
    updatingUserId: number,
  ): Promise<Vehicle> {
    this.logger.log({
      method: 'updateVehicle',
      vehicleId: id,
      updatingUser: updatingUserId,
    });

    const vehicleReservation = await this.reservationService.findMany({
      vehicleIds: [id],
      job: { state: [JobState.RUNNING, JobState.WAITING] },
      expressions: [],
    });

    if (vehicleReservation.totalCount > 0) {
      throw new BadRequestException('Vehicle has active or incomplete reservations.');
    }

    const { operatorId } = updateVehicleDto;

    const vehicle = await this.findOne(id);
    const existingImageUrl = vehicle.imageUrl;

    Object.assign(vehicle, updateVehicleDto);

    vehicle.imageUrl = existingImageUrl;

    if (operatorId && operatorId !== vehicle.operator?.id) {
      const operator = await this.usersService.findOneById(operatorId);

      await this.unavailablePeriodService.removeVehiclesFromUnavailablePeriods(
        [vehicle.id],
        vehicle.operator?.id,
      );
      vehicle.operator = operator;
    } else if (!operatorId) {
      vehicle.operator = null;
    }

    if (updatingUserId && updatingUserId !== vehicle.manager?.id) {
      const manager = await this.usersService.findOneById(updatingUserId);
      vehicle.manager = manager;
    }

    // Generate unique ID
    vehicle.uniqueIdentificationNumber = VehicleUtils.getVehicleUniqueId(vehicle);

    const savedVehicle = await this.vehicleRepository.save(vehicle);

    this.logger.log({
      method: 'updateVehicle',
      vehicleId: vehicle.id,
      updatingUser: updatingUserId,
    });
    return savedVehicle;
  }

  async remove(id: number): Promise<void> {
    this.logger.log({ method: 'removeVehicle', id });

    const vehicleReservation = await this.reservationService.findMany({
      vehicleIds: [id],
      job: { state: [JobState.RUNNING, JobState.WAITING] },
      expressions: [],
    });

    if (vehicleReservation.totalCount > 0) {
      throw new BadRequestException('Vehicle has active or incomplete reservations.');
    }

    await this.vehicleRepository.delete(id);
  }

  async incrementCompletedJobs(vehicleId: number): Promise<void> {
    const vehicle = await this.findOne(vehicleId);
    if (vehicle) {
      vehicle.completedJobs = vehicle.completedJobs ? vehicle.completedJobs + 1 : 1;
      await this.vehicleRepository.save(vehicle);
    }
  }

  async getPartneredVehicles(
    query: GetContractedVehiclesDto,
    fetchingUser?: SerializedUser,
  ): Promise<EntityWithCountDto<Vehicle>> {
    this.logger.log({ method: 'getPartneredVehicles', query });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { dispatcherCompanyId, operatorCompanyIds, status, ...filters } = query;

    const partners = await this.partnerService.findMany(
      {
        expressions: operatorCompanyIds?.length
          ? [
              {
                category: PartnerCategoryTypes.partnerOperatorCompanyId,
                operator: 'in',
                value: operatorCompanyIds,
                conditionType: 'AND',
              },
            ]
          : [],
        sortModel: [],
      },
      fetchingUser,
    );

    if (!partners.totalCount) {
      return new EntityWithCountDto<Vehicle>(Vehicle, [], 0);
    }

    // Extract operator company IDs from the partners
    const partnerOperatorCompanyIds = [
      ...new Set(partners.data.map(partner => partner.operatorCompanyId)),
    ];

    const vehicleQuery: GetVehiclesDto = {
      ...filters,
      operatorCompanyIds: partnerOperatorCompanyIds,
    };

    // Fetch vehicles associated with the extracted operator manager IDs
    const vehicles = await this.findMany(vehicleQuery, fetchingUser);

    return vehicles;
  }

  async getFavoritedVehicles(
    query: GetFavoriteVehiclesDto,
    searchingUser?: SerializedUser,
  ): Promise<EntityWithCountDto<Vehicle>> {
    this.logger.log({ method: 'getFavoritedVehicles', query });
    const { dispatcherCompanyId, operatorCompanyIds, ...filters } = query;

    const favoriteQuery: GetFavoriteDto = {
      dispatcherCompanyId: dispatcherCompanyId || null,
      operatorCompanyIds: operatorCompanyIds || [],
    };

    // Fetch favorites for the dispatcher company
    const favorites = await this.favoriteService.findMany(favoriteQuery);

    if (!favorites.totalCount) {
      return new EntityWithCountDto<Vehicle>(Vehicle, [], 0);
    }

    // Extract operator company IDs
    const favoriteOperatorCompanyIds = [
      ...new Set(favorites.data.map(favorite => favorite.operatorCompanyId)),
    ];

    const vehicleQuery: GetVehiclesDto = {
      ...filters,
      operatorCompanyIds: favoriteOperatorCompanyIds,
    };

    // Fetch vehicles associated with the extracted operator manager IDs
    const vehicles = await this.findMany(vehicleQuery, searchingUser);

    this.logger.log({ method: 'getFavoritedVehicles', vehiclesLength: vehicles.totalCount });
    return vehicles;
  }

  async getContractedVehicles(
    query: GetContractedVehiclesDto,
    fetchingUser?: SerializedUser,
  ): Promise<EntityWithCountDto<Vehicle>> {
    this.logger.log({ method: 'getContractedVehicles', query });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { dispatcherCompanyId, operatorCompanyIds, status, ...filters } = query;

    const expressionsToAdd: ContractExpression[] = [
      {
        category: ContractCategoryTypes.status,
        operator: '=',
        value: ContractedStatus.APPROVED,
        conditionType: 'AND',
      },
    ];
    if (operatorCompanyIds?.length > 0) {
      expressionsToAdd.push({
        category: ContractCategoryTypes.operatorCompanyId,
        operator: 'in',
        value: operatorCompanyIds,
        conditionType: 'AND',
      });
    }
    if (dispatcherCompanyId) {
      expressionsToAdd.push({
        category: ContractCategoryTypes.dispatcherCompanyId,
        operator: '=',
        value: dispatcherCompanyId,
        conditionType: 'AND',
      });
    }
    const isoDateFrom = filters.dateFrom ? new Date(filters.dateFrom).toISOString() : null;
    const isoDateTo = filters.dateTo ? new Date(filters.dateTo).toISOString() : null;

    if (isoDateFrom && isoDateTo) {
      expressionsToAdd.push(
        {
          conditionType: 'AND',
          expressions: [
            // Contract date conditions
            {
              category: ContractCategoryTypes.endDate,
              operator: '>=',
              value: isoDateFrom,
              conditionType: 'AND',
            },
            {
              category: ContractCategoryTypes.startDate,
              operator: '<=',
              value: isoDateTo,
              conditionType: 'AND',
            },
          ],
        },
        {
          conditionType: 'AND',
          expressions: [
            // Suspension date conditions
            {
              // Accept contracts with no suspension period.
              category: '"suspensionPeriods"."id"',
              operator: 'isEmpty',
              conditionType: 'OR',
            },
            {
              conditionType: 'OR',
              expressions: [
                {
                  // This group should be interpreted as “NOT (there is an overlapping suspension)”
                  conditionType: 'NOT',
                  expressions: [
                    {
                      category: ContractCategoryTypes.suspensionEnd,
                      operator: '>=',
                      value: isoDateFrom,
                      conditionType: 'AND',
                    },
                    {
                      category: ContractCategoryTypes.suspensionStart,
                      operator: '<=',
                      value: isoDateTo,
                      conditionType: 'AND',
                    },
                  ],
                },
              ],
            },
          ],
        },
      );
    } else if (isoDateFrom) {
      // Only a start date is provided.
      expressionsToAdd.push(
        {
          // Contract must be active on isoDateFrom.
          conditionType: 'AND',
          expressions: [
            {
              category: ContractCategoryTypes.startDate,
              operator: '<=',
              value: isoDateFrom,
              conditionType: 'AND',
            },
            {
              category: ContractCategoryTypes.endDate,
              operator: '>=',
              value: isoDateFrom,
              conditionType: 'AND',
            },
          ],
        },
        {
          // Suspension condition for a single date:
          // Either there is no suspension, or the suspension does NOT cover isoDateFrom.
          conditionType: 'AND',
          expressions: [
            {
              conditionType: 'OR',
              expressions: [
                {
                  category: '"suspensionPeriods"."id"',
                  operator: 'isEmpty',
                  conditionType: 'AND',
                },
                {
                  conditionType: 'NOT',
                  expressions: [
                    {
                      category: ContractCategoryTypes.suspensionEnd,
                      operator: '>=',
                      value: isoDateFrom,
                      conditionType: 'AND',
                    },
                    {
                      category: ContractCategoryTypes.suspensionStart,
                      operator: '<=',
                      value: isoDateFrom,
                      conditionType: 'AND',
                    },
                  ],
                },
              ],
            },
          ],
        },
      );
    } else if (isoDateTo) {
      // Only an end date is provided.
      expressionsToAdd.push(
        {
          // Contract must be active on isoDateTo.
          conditionType: 'AND',
          expressions: [
            {
              category: ContractCategoryTypes.startDate,
              operator: '<=',
              value: isoDateTo,
              conditionType: 'AND',
            },
            {
              category: ContractCategoryTypes.endDate,
              operator: '>=',
              value: isoDateTo,
              conditionType: 'AND',
            },
          ],
        },
        {
          // Suspension condition for a single date:
          // Either there is no suspension, or the suspension does NOT cover isoDateTo.
          conditionType: 'AND',
          expressions: [
            {
              conditionType: 'OR',
              expressions: [
                {
                  category: '"suspensionPeriods"."id"',
                  operator: 'isEmpty',
                  conditionType: 'AND',
                },
                {
                  conditionType: 'NOT',
                  expressions: [
                    {
                      category: ContractCategoryTypes.suspensionEnd,
                      operator: '>=',
                      value: isoDateTo,
                      conditionType: 'AND',
                    },
                    {
                      category: ContractCategoryTypes.suspensionStart,
                      operator: '<=',
                      value: isoDateTo,
                      conditionType: 'AND',
                    },
                  ],
                },
              ],
            },
          ],
        },
      );
    }

    const contracts = await this.contractService.findMany(
      {
        expressions: expressionsToAdd,
        sortModel: [],
        relations: ['suspensionPeriods'],
      },
      fetchingUser,
    );

    if (!contracts.totalCount) {
      return new EntityWithCountDto<Vehicle>(Vehicle, [], 0);
    }

    // Extract unique vehicle IDs from the contracts
    const contractVehicleIds = [...new Set(contracts.data.map(contract => contract.vehicleId))];

    // Check if filters.vehicleIds is provided and filter accordingly
    const vehicleIds =
      filters && filters.vehicleIds
        ? filters.vehicleIds.filter(id => contractVehicleIds.includes(+id))
        : contractVehicleIds;

    const vehicleQuery: GetVehiclesDto = {
      ...filters,
      vehicleIds,
    };

    // Fetch vehicles associated with the extracted operator manager IDs
    const vehicles = await this.findMany(vehicleQuery, fetchingUser);

    return vehicles;
  }

  filterVehiclePricelists(
    vehicles: VehicleResponse[],
    pricelistIds: number[] = [],
    isOperatorManager: boolean = false,
  ): VehicleResponse[] {
    vehicles.forEach(vehicle => {
      if (vehicle.contracts?.length > 0) {
        // We guarantee form vehicle query that we return
        // only one contract for this vehicle.
        vehicle.assignedPricelist = vehicle.contracts[0].pricelist;
      } else {
        delete vehicle.contracts;
      }
      if (vehicle.pricelists?.length > 0 && vehicle.assignedPricelist == null) {
        const pricelists = vehicle.pricelists;

        if (isOperatorManager) {
          vehicle.assignedPricelist =
            pricelists.find(pricelist => pricelist.container.isDefault) || null;
          return;
        }

        const filteredPricelists = pricelistIds.length
          ? pricelists.filter(pricelist => pricelistIds.includes(pricelist.id))
          : pricelists;

        vehicle.assignedPricelist = filteredPricelists.length > 0 ? filteredPricelists[0] : null;
      }
    });

    return vehicles;
  }

  async getDistanceBetweenVehicleAndReservation(
    vehicleId: number,
    reservation: Reservation,
  ): Promise<number> {
    const [resLat, resLon] = reservation.location.coordinates;

    const result = await this.vehicleRepository.query(
      `
      SELECT
        ST_Distance(
          v.location::geography,
          ST_SetSRID(ST_MakePoint($2, $1), 4326)::geography
        ) / 1000 AS distance_in_km
      FROM vehicle v
      WHERE v.id = $3
      `,
      [resLat, resLon, vehicleId],
    );

    return parseFloat(result[0]?.distance_in_km);
  }

  applyLocationFilter(
    queryBuilder: SelectQueryBuilder<Vehicle>,
    coordinates: number[],
    radius: number,
  ) {
    if (!coordinates) return;

    const [lng, lat] = coordinates;

    queryBuilder.andWhere(
      `ST_DWithin(vehicle.location::geography, ST_SetSRID(ST_MakePoint(:lng, :lat), 4326)::geography, :radius)`,
      { lng, lat, radius },
    );
  }

  async updateVehicleStatus(vehicleIds: number[], status: Status): Promise<void> {
    if (vehicleIds.length === 0) return;

    await this.vehicleRepository.update(vehicleIds, { status });
    this.logger.log({
      method: 'updateVehicleStatus',
      message: `Updated vehicles (${vehicleIds.join(', ')}) to status: ${status}`,
    });
  }

  async refreshVehicleUniqueIdsForCompany(companyId: number) {
    this.logger.log({
      method: 'refreshVehicleUniqueIdsForCompany',
      companyId,
    });

    const { data: vehicles } = await this.findManySearch({
      limit: Number.MAX_SAFE_INTEGER,
      offset: 0,
      operatorCompanyIds: [companyId],
      searchType: 'All',
    });

    const vehiclesToUpdate: Vehicle[] = [];
    for (const vehicle of vehicles) {
      vehicle.uniqueIdentificationNumber = VehicleUtils.getVehicleUniqueId(vehicle);
      vehiclesToUpdate.push(vehicle);
    }

    return await this.vehicleRepository.save(vehiclesToUpdate);
  }

  async uploadImage(
    vehicleId: number,
    file: Express.Multer.File,
    updatingUserId: number,
  ): Promise<Vehicle> {
    this.logger.log({
      method: 'uploadVehicleImage',
      vehicleId,
      updatingUser: updatingUserId,
      fileName: file.originalname,
      fileSize: file.size,
    });

    const vehicle = await this.findOne(vehicleId);

    // Remove old image if exists
    if (vehicle.imageUrl) {
      try {
        await this.filesService.removeVehicleImage(vehicle.imageUrl);
      } catch (error) {
        this.logger.warn({
          message: 'Failed to remove old vehicle image',
          vehicleId,
          oldImageUrl: vehicle.imageUrl,
          error: error.message,
        });
      }
    }

    const fileExtension = `.${file.originalname.split('.').pop()}`;
    const filename = `vehicle-${vehicleId}-${Date.now()}`;

    const uploadResult = await this.filesService.uploadVehicleImage(
      file.buffer,
      filename,
      file.mimetype,
      fileExtension,
    );

    if (!uploadResult) {
      this.logger.error({
        message: 'Upload result was null',
        vehicleId,
        updatingUser: updatingUserId,
      });
      throw new BadRequestException('Failed to upload image to S3. Please check S3 configuration and bucket permissions.');
    }

    // Update vehicle with new image URL
    vehicle.imageUrl = uploadResult.Key;
    const savedVehicle = await this.vehicleRepository.save(vehicle);

    this.logger.log({
      method: 'uploadVehicleImage',
      vehicleId,
      updatingUser: updatingUserId,
      imageKey: uploadResult.Key,
    });

    return savedVehicle;
  }

  async removeImage(vehicleId: number, updatingUserId: number): Promise<Vehicle> {
    this.logger.log({
      method: 'removeVehicleImage',
      vehicleId,
      updatingUser: updatingUserId,
    });

    const vehicle = await this.findOne(vehicleId);

    if (!vehicle.imageUrl) {
      throw new BadRequestException('Vehicle has no image to remove');
    }

    try {
      await this.filesService.removeVehicleImage(vehicle.imageUrl);
    } catch (error) {
      this.logger.error({
        message: 'Failed to remove vehicle image from S3',
        vehicleId,
        imageUrl: vehicle.imageUrl,
        error: error.message,
      });
      throw new BadRequestException('Failed to remove image from storage');
    }

    vehicle.imageUrl = null;
    const savedVehicle = await this.vehicleRepository.save(vehicle);

    this.logger.log({
      method: 'removeVehicleImage',
      vehicleId,
      updatingUser: updatingUserId,
    });

    return savedVehicle;
  }

  async getImageUrl(vehicleId: number): Promise<{ imageUrl: string | null; presignedUrl?: string }> {
    this.logger.log({
      method: 'getVehicleImageUrl',
      vehicleId,
    });

    const vehicle = await this.findOne(vehicleId);

    if (!vehicle.imageUrl) {
      return { imageUrl: null };
    }

    try {
      const presignedUrl = await this.filesService.generateVehicleImageUrl(vehicle.imageUrl, 3600); // 1 hour expiry
      return {
        imageUrl: vehicle.imageUrl,
        presignedUrl,
      };
    } catch (error) {
      this.logger.error({
        message: 'Failed to generate presigned URL for vehicle image',
        vehicleId,
        imageUrl: vehicle.imageUrl,
        error: error.message,
      });
      return { imageUrl: vehicle.imageUrl };
    }
  }

  private async addPresignedUrlsToVehicles(vehicles: Vehicle[]): Promise<void> {
    const promises = vehicles.map(async (vehicle) => {
      if (vehicle.imageUrl) {
        try {
          const presignedUrl = await this.filesService.generateVehicleImageUrl(vehicle.imageUrl, 3600); // 1 hour expiry
          vehicle.imageUrl = presignedUrl;
        } catch (error) {
          this.logger.warn({
            message: 'Failed to generate presigned URL for vehicle image',
            vehicleId: vehicle.id,
            imageUrl: vehicle.imageUrl,
            error: error.message,
          });
        }
      }
    });

    await Promise.all(promises);
  }
}
