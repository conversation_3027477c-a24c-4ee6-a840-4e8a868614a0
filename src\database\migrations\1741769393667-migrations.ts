import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1741769393667 implements MigrationInterface {
  name = 'Migrations1741769393667';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "backupPumpPackage"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "secondTechnicianFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "backupPumpPackage"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "secondTechnicianFeeWeekend"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "secondTechnicianHourFee" double precision`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "secondTechnicianHourFeeNight" double precision`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "secondTechnicianHourFeeWeekend" double precision`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "secondTechnicianHourFeeWeekend"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "secondTechnicianHourFeeNight"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "secondTechnicianHourFee"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "secondTechnicianFeeWeekend" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "backupPumpPackage" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "secondTechnicianFee" double precision`);
    await queryRunner.query(`ALTER TABLE "job" ADD "backupPumpPackage" boolean`);
  }
}
