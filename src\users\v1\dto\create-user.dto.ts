import { BadRequestException } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsDateString, IsEmail, IsEnum, IsISO8601, IsOptional, IsString } from 'class-validator';
import { CompanyType } from 'src/common/constants/company.enum';
import { Role } from 'src/common/constants/roles.enum';
import { Status } from 'src/common/constants/status.enum';
import { turnDateIntoDateString } from 'src/common/helpers/processDate';
import { processNumber } from 'src/common/helpers/processNumbers';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';

export class CreateUserDto {
  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'firstName'))
  firstName: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'lastName'))
  lastName: string;

  @ApiProperty()
  @IsEmail()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'email'))
  email: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'phoneNumber'))
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  companyName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  companyAddress?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  zipCode?: number;

  @ApiProperty()
  @IsDateString({ strict: true }, { message: 'Ivalid birth date' })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const dateStr = turnDateIntoDateString(value, 'birthDate');
      const now = new Date();
      const bd = new Date(dateStr);
      if (bd > now) {
        throw new BadRequestException('Date of birth must be in the past');
      }

      return dateStr;
    }

    return value;
  })
  @IsOptional()
  birthdate?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  secondaryAddress?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  phoneNumberPersonal?: string;

  @ApiProperty({ enum: Status, enumName: 'Status' })
  @IsEnum(Status)
  status: Status;

  @ApiProperty()
  @IsISO8601()
  @IsOptional()
  from?: Date;

  @ApiProperty()
  @IsISO8601()
  @IsOptional()
  to?: Date;

  @ApiProperty({ enum: Role, enumName: 'Role' })
  @IsEnum(Role)
  role: Role;

  @ApiProperty()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  companyId?: number;

  @ApiProperty({ enum: CompanyType, enumName: 'CompanyType' })
  @IsOptional()
  @IsEnum(CompanyType)
  companyType?: CompanyType;
}
