import { BaseEntity } from '../../common/entities/base.entity';
import { Column, Entity, Index, JoinColumn, OneToMany, OneToOne } from 'typeorm';
import { Company } from 'src/company/entities/company.entity';
import { WorkScheduleSettings } from './work-schedule-settings.entity';
import { CancellationSettings } from './cancellation-settings.entity';
import { CompanyHoliday } from './company-holiday.entity';

@Entity()
export class CompanySettings extends BaseEntity {
  @Column()
  @Index()
  companyId: number;

  @OneToOne(() => Company, entity => entity.settings, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'companyId' })
  company?: Company;

  @OneToMany(() => WorkScheduleSettings, entity => entity.companySettings, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  workSchedules?: WorkScheduleSettings[];

  @OneToMany(() => CompanyHoliday, entity => entity.companySettings, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  holidays?: CompanyHoliday[];

  @Column({ nullable: true })
  @Index()
  cancellationId: number;

  @OneToOne(() => CancellationSettings, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'cancellationId' })
  cancellation?: CancellationSettings;
}
