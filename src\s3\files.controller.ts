import { Controller, Delete, Get, Param, VERSION_NEUTRAL } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { FilesService } from './files.service';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';

@ApiTags('Files')
@Controller({ path: 'file', version: VERSION_NEUTRAL })
export class FilesController {
  bucketName = process.env.S3_BUCKET_NAME;
  constructor(private filesService: FilesService) {}
  @Get(':key')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async getFile(@Param('key') key: string) {
    return await this.filesService.generatePresignedUrl(this.bucketName, key);
  }

  @Delete(':key')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async removeFile(@Param('key') key: string) {
    return await this.filesService.removeFile(key, this.bucketName);
  }
}
