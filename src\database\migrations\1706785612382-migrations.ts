import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706785612382 implements MigrationInterface {
  name = 'Migrations1706785612382';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "typeOfMotorization"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "typeOfMotorization" character varying NOT NULL`,
    );
  }
}
