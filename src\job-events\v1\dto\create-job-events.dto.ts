import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNumber, IsString } from 'class-validator';
import { processNumber } from 'src/common/helpers/processNumbers';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';

export class CreateJobEventsDto {
  @ApiProperty({ type: String })
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'statusDescription'))
  statusDescription: string;

  @ApiProperty({ type: String })
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'currentStatus'))
  currentStatus: string;

  @ApiProperty({ type: Number })
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  jobId: number;
}
