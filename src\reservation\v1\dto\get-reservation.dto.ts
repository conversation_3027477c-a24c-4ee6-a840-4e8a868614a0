import { ApiProperty } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import { IsOptional, IsEnum, ValidateNested, IsISO8601, IsBoolean, IsArray } from 'class-validator';
import { JobState, JobStatus } from 'src/common/constants/job.enum';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { processBoolean } from 'src/common/helpers/processBooleans';
import { transformFriendlyCategory } from 'src/common/utility/transform-category.util';
import { SortItem, Expression } from 'src/libs/helpers/CeQuery';

export enum ReservationCategoryTypes {
  reservationId = '"reservation"."id"',
  vehicleId = '"reservation"."vehicleId"',
  managerId = '"reservation"."managerId"',
  dispatcherId = '"reservation"."dispatcherId"',
  operatorId = '"reservation"."operatorId"',
  dateFrom = '"reservation"."dateFrom"',
  dateTo = '"reservation"."dateTo"',
  createdAt = '"reservation"."created_at"',
  vehicleName = '"reservation"."vehicleUniqueId"',
  hasBeenRead = '"reservation"."hasBeenRead"',
  siteAddress = '"reservation"."siteAddress"',
  clientDetailsCompanyName = '(("reservation"."clientDetails")::jsonb)->>\'companyName\'',
  amountOfConcrete = '"job"."amountOfConcrete"',
  totalSum = '"reservation"."totalSum"',
  invoiceNumber = '"reservation"."invoiceNumber"',
  comments = '"job"."comments"',
  invoiced = '"reservation"."invoiced"',
  jobStatus = '"job"."status"',
  jobState = '"job"."state"',
  operatorManagerName = '"manager"."firstName"',
  operatorName = '"operator"."firstName"',
  operatorCompanyId = '"operatorCompany"."id"',
  operatorCompanyName = '"operatorCompany"."name"',
  dispatcherCompanyId = '"dispatcherCompany"."id"',
  dispatcherCompanyName = '"dispatcherCompany"."name"',
}

class JobFilter {
  @ApiProperty({
    required: false,
    enum: JobStatus,
    isArray: true,
    description: 'Array of job statuses',
  })
  @IsOptional()
  @IsEnum(JobStatus, { each: true })
  readonly status?: JobStatus[] | null;

  @IsOptional()
  @IsEnum(JobState, { each: true })
  readonly state?: JobState[] | null;
}

export class GetReservationDto extends CommonQueryParams {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReservationSortItem)
  readonly sortModel?: ReservationSortItem[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReservationExpression)
  readonly expressions: ReservationExpression[];

  @ApiProperty({ required: false })
  @IsISO8601()
  @IsOptional()
  dateFrom?: Date;

  @ApiProperty({ required: false })
  @IsISO8601()
  @IsOptional()
  dateTo?: Date;

  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
    description: 'Array of vehicle IDs',
  })
  @IsOptional()
  readonly vehicleIds?: number[] | null;

  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
    description: 'Array of operator manager IDs',
  })
  @IsOptional()
  readonly managerIds?: number[] | null;

  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
    description: 'Array of dispatcher IDs',
  })
  @IsOptional()
  readonly dispatcherIds?: number[] | null;

  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
    description: 'Array of operator IDs',
  })
  @IsOptional()
  readonly operatorIds?: number[] | null;

  @ApiProperty({ type: () => JobFilter, required: false, description: 'Object for job filtering' })
  @IsOptional()
  @ValidateNested()
  @Type(() => JobFilter)
  readonly job?: JobFilter;

  @ApiProperty({
    required: false,
    type: Boolean,
    description: 'Filter by invoiced status (true or false)',
  })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  readonly invoiced?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Number)
  dispatcherCompanyId?: number;
}
export class ReservationSortItem extends SortItem {
  @ApiProperty({
    required: false,
    enum: ReservationCategoryTypes,
  })
  @Transform(({ value }) => transformFriendlyCategory(value, ReservationCategoryTypes))
  @IsEnum(ReservationCategoryTypes, { message: 'invalid sort field' })
  field: string;
}

export class ReservationExpression extends Expression {
  @IsOptional()
  @Transform(({ value }) => transformFriendlyCategory(value, ReservationCategoryTypes))
  @IsEnum(ReservationCategoryTypes, { message: 'invalid filter category' })
  category?: string | null | undefined;
}
