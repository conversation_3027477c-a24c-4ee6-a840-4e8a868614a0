import { ActionType } from 'src/common/constants/task.enum';
import { Entity, Column, ManyToOne, JoinColumn, Index } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Task } from './task.entity';

@Entity()
export class TaskActivity extends BaseEntity {
  @Column({ type: 'enum', enum: ActionType })
  actionType: ActionType;

  @Column()
  oldValue: string;

  @Column()
  @Index()
  taskId: number;

  @ManyToOne(() => Task, task => task.activities, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'taskId' })
  task: Task;
}
