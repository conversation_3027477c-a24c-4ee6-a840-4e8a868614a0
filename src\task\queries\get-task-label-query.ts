const groupBy = `
GROUP BY
  "task_label"."id"
`;

export const getTaskLabelsQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT 
      "task_label".*
      ${selects ? `, ${selects}` : ''}
    FROM "task_label"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getTaskLabelsCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "task_label"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  company: {
    join: `LEFT JOIN "company" AS "company" ON "task_label"."companyId" = "company"."id"`,
    select: `jsonb_build_object(
      'id', "company"."id",
      'name', "company"."name"
    ) AS "company"`,
    groupBy: `"company"."id"`,
  },
  tasks: {
    join: `LEFT JOIN "task" AS "tasks" ON "tasks"."labelId" = "task_label"."id"`,
    select: `COALESCE(json_agg(DISTINCT jsonb_build_object(
      'id', "tasks"."id"
    )) FILTER (WHERE "task_activity"."id" IS NOT NULL), '[]') AS "tasks"`,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
