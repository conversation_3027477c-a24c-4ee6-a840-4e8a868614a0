import { Status } from '../../common/constants/status.enum';
import { BaseEntity } from '../../common/entities/base.entity';
import { Entity, Column, Index, OneToMany, OneToOne } from 'typeorm';
import { User } from 'src/users/entities/user.entity';
import { Partner } from 'src/partner/entities/partner.entity';
import { Favorite } from 'src/favorite/entities/favorite.entity';
import { CompanyCategory, CompanyType } from 'src/common/constants/company.enum';
import { Task } from 'src/task/entities/task.entity';
import { CompanySettings } from 'src/company-settings/entities/company-settings.entity';
import { ContainerPricelists } from 'src/pricelist/entities/container-pricelists.entity';
import { Contract } from 'src/contract/entities/contract.entity';
import { UnavailablePeriod } from 'src/unavailable-period/entities/unavailable-period.entity';

interface Tax {
  name: string;
  percentage: number;
  isDefault?: boolean;
}

@Entity()
export class Company extends BaseEntity {
  @Column({ name: 'name' })
  name: string;

  @Column({
    type: 'enum',
    enum: CompanyType,
  })
  type: CompanyType;

  @Column({
    type: 'enum',
    enum: CompanyCategory,
    nullable: true,
  })
  category?: CompanyCategory;

  @Index({ unique: true })
  @Column()
  contactEmail: string;

  @Column({ nullable: true })
  billingEmail?: string;

  @Column({ nullable: true })
  planningEmail?: string;

  @Column({ nullable: true })
  phoneNumber?: string;

  @Column({ nullable: true })
  address?: string;

  @Column({ nullable: true })
  secondaryAddress?: string;

  @Column({ nullable: true })
  country?: string;

  @Column({ nullable: true })
  city?: string;

  @Column({ nullable: true })
  zipCode?: number;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.PENDING,
  })
  status: number;

  @Column({ nullable: true })
  vatNumber?: string;

  @Column({ nullable: true, type: 'jsonb' })
  taxes?: Tax[];

  @OneToMany(() => User, user => user.company, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  users?: User[];

  @OneToMany(() => Partner, partner => partner.dispatcherCompany)
  dispatcherPartners: Partner[];

  @OneToMany(() => Partner, partner => partner.operatorCompany)
  operatorPartners: Partner[];

  @OneToMany(() => Favorite, favorite => favorite.dispatcherCompany)
  favorites: Favorite[];

  @OneToMany(() => Favorite, favorite => favorite.operatorCompany)
  favoritedBy: Favorite[];

  @OneToMany(() => Task, task => task.company)
  tasks: Task[];

  @OneToOne(() => CompanySettings, entity => entity.company)
  settings: CompanySettings;

  @OneToMany(() => Task, task => task.company)
  containers: ContainerPricelists[];

  @OneToMany(() => Contract, contract => contract.dispatcherCompany)
  dispatcherContracts: Contract[];

  @OneToMany(() => Contract, contract => contract.operatorCompany)
  operatorContracts: Contract[];

  @OneToMany(() => UnavailablePeriod, period => period.company, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  unavailablePeriods?: UnavailablePeriod[];
}
