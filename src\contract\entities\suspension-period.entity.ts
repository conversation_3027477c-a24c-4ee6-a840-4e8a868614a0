import { Contract } from 'src/contract/entities/contract.entity';
import { Entity, Column, ManyToOne, JoinColumn, Index, Unique } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';

@Unique(['contractId', 'suspensionStart', 'suspensionEnd'])
@Entity()
export class SuspensionPeriod extends BaseEntity {
  @Column({ type: 'timestamptz', nullable: false })
  suspensionStart: Date;

  @Column({ type: 'timestamptz', nullable: false })
  suspensionEnd: Date;

  @Column()
  @Index()
  contractId: number;

  @ManyToOne(() => Contract, contract => contract.suspensionPeriods, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'contractId' })
  contract: Contract;
}
