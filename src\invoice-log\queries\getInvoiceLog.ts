import { SelectQueryBuilder } from 'typeorm';
import { InvoiceLog } from '../entities/invoice-log.entity';

export const getInvoiceLogQuery = async (
  queryBuilder: SelectQueryBuilder<InvoiceLog>,
  invoiceNumber?: string,
  reservationId?: number,
): Promise<InvoiceLog | null> => {
  try {
    queryBuilder
      .select([
        'invoiceLog.reservationIds',
        'invoiceLog.operatorCompanyId',
        'invoiceLog.dispatcherCompanyId',
        'invoiceLog.invoiceNumber',
        'invoiceLog.total',
      ])
      .leftJoinAndSelect('invoiceLog.operatorCompany', 'operatorCompany')
      .leftJoinAndSelect('invoiceLog.dispatcherCompany', 'dispatcherCompany');

    if (invoiceNumber) {
      queryBuilder.where('invoiceLog.invoiceNumber = :invoiceNumber', { invoiceNumber });
    } else if (reservationId) {
      queryBuilder.where(':reservationId = ANY(invoiceLog.reservationIds)', { reservationId });
    } else {
      throw new Error('Either invoiceNumber or reservationId must be provided.');
    }

    const invoiceLog = await queryBuilder.getOne();
    return invoiceLog;
  } catch (error) {
    console.error('Error fetching invoiceLog:', error);
    return null;
  }
};
