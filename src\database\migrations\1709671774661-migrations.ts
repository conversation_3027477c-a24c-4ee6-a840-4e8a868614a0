import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1709671774661 implements MigrationInterface {
  name = 'Migrations1709671774661';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomSize"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomSize" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "completedJobs"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "completedJobs" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "completedJobs"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "completedJobs" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomSize"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomSize" double precision`);
  }
}
