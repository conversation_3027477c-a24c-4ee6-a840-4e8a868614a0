import { Body, Controller, Post, HttpCode, HttpStatus, Response, Res } from '@nestjs/common';
import { AuthService } from './auth.service';
import { ApiTags } from '@nestjs/swagger';
import { SignInDto } from './dto/sign-in.dto';
import { Public } from 'src/common/decorators/public.decorator';
import { Response as ResponseType } from 'express';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @HttpCode(HttpStatus.OK)
  @Public()
  @Post('login')
  signIn(@Body() signInDto: SignInDto, @Response() res) {
    return this.authService.signIn(signInDto.email, signInDto.password, res);
  }

  @Post('logout')
  async logout(@Res({ passthrough: true }) res: ResponseType) {
    res.cookie('access_token', '', { expires: new Date() });
  }
}
