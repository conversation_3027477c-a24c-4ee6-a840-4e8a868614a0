/* eslint-disable @typescript-eslint/ban-types */
import { Exclude, Type } from 'class-transformer';
import { IsArray, IsNumber } from 'class-validator';

export class EntityWithCountDto<T> {
  @Exclude()
  private type: Function;

  //   @Type(() => User | Reservation | Vehicle)
  @Type(opt => (opt.newObject as EntityWithCountDto<T>).type)
  @IsArray()
  readonly data: T[];

  @IsNumber()
  readonly totalCount: number;

  constructor(type: Function, data: T[], totalCount: number) {
    this.type = type;
    this.data = data;
    this.totalCount = totalCount;
  }
}
