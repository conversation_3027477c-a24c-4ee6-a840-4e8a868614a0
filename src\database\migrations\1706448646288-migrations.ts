import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706448646288 implements MigrationInterface {
  name = 'Migrations1706448646288';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "companyName" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "companyAddress" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "vatNumber" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "status" SET DEFAULT '2'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "status" SET DEFAULT '1'`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "vatNumber" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "companyAddress" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "companyName" SET NOT NULL`);
  }
}
