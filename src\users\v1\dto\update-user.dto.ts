import { PartialType } from '@nestjs/mapped-types';
import { CreateUserDto } from './create-user.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsDate } from 'class-validator';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  remainingAttempts?: number;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  lockoutEndTime?: Date;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalWorkingHours?: number;
}
