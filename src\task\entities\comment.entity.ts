import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Task } from './task.entity';

@Entity()
export class TaskComment extends BaseEntity {
  @Column({ type: 'text' })
  content: string;

  @Column()
  @Index()
  taskId: number;

  @ManyToOne(() => Task, task => task.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'taskId' })
  task: Task;
}
