import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708164739201 implements MigrationInterface {
  name = 'Migrations1708164739201';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "unavailable_period" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "title" character varying NOT NULL, "comment" character varying, "from" TIMESTAMP NOT NULL, "to" TIMESTAMP NOT NULL, "vehicleId" integer, "operatorManagerId" integer, CONSTRAINT "PK_5c9463391660c9f10986b86c68f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_02ab9e053fe898666845b620eb8" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_25fad7f4feabe9a54449493001b" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_25fad7f4feabe9a54449493001b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_02ab9e053fe898666845b620eb8"`,
    );
    await queryRunner.query(`DROP TABLE "unavailable_period"`);
  }
}
