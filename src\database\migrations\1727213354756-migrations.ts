import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1727213354756 implements MigrationInterface {
  name = 'Migrations1727213354756';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_fa43f7b3050b011dd02fc5aa2b3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_ea78f3aadec5197454998ca2ee5"`,
    );
    await queryRunner.query(`ALTER TABLE "favorite" DROP COLUMN "dispatcherManagerId"`);
    await queryRunner.query(`ALTER TABLE "favorite" DROP COLUMN "operatorManagerId"`);
    await queryRunner.query(`ALTER TABLE "favorite" ADD "dispatcherCompanyId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "favorite" ADD "operatorCompanyId" integer NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_62dd5af91a0d20d09486118cca0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ALTER COLUMN "dispatcherCompanyId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ALTER COLUMN "operatorCompanyId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5c056f28210f1339fd7720dc58" ON "favorite" ("dispatcherCompanyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5f05906ab6125e9ffd34bf3789" ON "favorite" ("operatorCompanyId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_5c056f28210f1339fd7720dc582" FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_5f05906ab6125e9ffd34bf3789f" FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_62dd5af91a0d20d09486118cca0" FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2" FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_62dd5af91a0d20d09486118cca0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_5f05906ab6125e9ffd34bf3789f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_5c056f28210f1339fd7720dc582"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_5f05906ab6125e9ffd34bf3789"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5c056f28210f1339fd7720dc58"`);
    await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "operatorCompanyId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "contract" ALTER COLUMN "dispatcherCompanyId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2" FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_62dd5af91a0d20d09486118cca0" FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "favorite" DROP COLUMN "operatorCompanyId"`);
    await queryRunner.query(`ALTER TABLE "favorite" DROP COLUMN "dispatcherCompanyId"`);
    await queryRunner.query(`ALTER TABLE "favorite" ADD "operatorManagerId" integer`);
    await queryRunner.query(`ALTER TABLE "favorite" ADD "dispatcherManagerId" integer`);
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_ea78f3aadec5197454998ca2ee5" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_fa43f7b3050b011dd02fc5aa2b3" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
