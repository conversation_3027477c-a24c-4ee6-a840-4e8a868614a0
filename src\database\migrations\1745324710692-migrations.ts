import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1745324710692 implements MigrationInterface {
  name = 'Migrations1745324710692';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "companyId" integer`);
    await queryRunner.query(
      `CREATE INDEX "IDX_c87fbecd88840e75300baf9bec" ON "unavailable_period" ("companyId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_c87fbecd88840e75300baf9beca" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_c87fbecd88840e75300baf9beca"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_c87fbecd88840e75300baf9bec"`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "companyId"`);
  }
}
