import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1728158591452 implements MigrationInterface {
  name = 'Migrations1728158591452';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_1db0695b35006be185320812b9"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" DROP CONSTRAINT "UQ_2e5c0c48cc2271c11addc6902aa"`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "type"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "vehicleTypeId" integer NOT NULL`);
    await queryRunner.query(
      `CREATE INDEX "IDX_549a4acb7bf64f160d91e63486" ON "vehicle_size" ("vehicleTypeId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" ADD CONSTRAINT "UQ_f2c12138096001ab6a916e56338" UNIQUE ("operatorManagerId", "boomSize", "vehicleTypeId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" ADD CONSTRAINT "FK_549a4acb7bf64f160d91e634865" FOREIGN KEY ("vehicleTypeId") REFERENCES "vehicle_type"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" DROP CONSTRAINT "FK_549a4acb7bf64f160d91e634865"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" DROP CONSTRAINT "UQ_f2c12138096001ab6a916e56338"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_549a4acb7bf64f160d91e63486"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "vehicleTypeId"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "type" character varying`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" ADD CONSTRAINT "UQ_2e5c0c48cc2271c11addc6902aa" UNIQUE ("boomSize", "operatorManagerId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1db0695b35006be185320812b9" ON "vehicle_size" ("type") `,
    );
  }
}
