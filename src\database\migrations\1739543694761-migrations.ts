import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739543694761 implements MigrationInterface {
  name = 'Migrations1739543694761';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_1622f57e6fd5d12f7e36c1626c8"`,
    );
    await queryRunner.query(`ALTER TABLE "partner" ADD "pricelistId" integer`);
    await queryRunner.query(
      `ALTER TYPE "public"."partner_status_enum" RENAME TO "partner_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."partner_status_enum" AS ENUM('active', 'inactive')`,
    );
    await queryRunner.query(`ALTER TABLE "partner" DROP COLUMN "status"`);
    await queryRunner.query(
      `ALTER TABLE "partner" ADD "status" "public"."partner_status_enum" NOT NULL DEFAULT 'active'`,
    );
    await queryRunner.query(`DROP TYPE "public"."partner_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "partner" ADD CONSTRAINT "FK_09390d3b33e820f8b58d0834157" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "partner" DROP CONSTRAINT "FK_09390d3b33e820f8b58d0834157"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."partner_status_enum_old" AS ENUM('approved', 'pending', 'declined')`,
    );
    await queryRunner.query(`ALTER TABLE "partner" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "partner" ALTER COLUMN "status" TYPE "public"."partner_status_enum_old" USING "status"::"text"::"public"."partner_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "partner" ALTER COLUMN "status" SET DEFAULT 'pending'`);
    await queryRunner.query(`DROP TYPE "public"."partner_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."partner_status_enum_old" RENAME TO "partner_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "partner" DROP COLUMN "pricelistId"`);
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_1622f57e6fd5d12f7e36c1626c8" FOREIGN KEY ("partnerId") REFERENCES "partner"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
