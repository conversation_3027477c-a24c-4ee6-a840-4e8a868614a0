import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1747943573191 implements MigrationInterface {
  name = 'Migrations1747943573191';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_ef05556c304ed63583fc0881f6"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9c0cfc34d4336f986190f903b7"`);

    // Change column types without dropping data
    await queryRunner.query(`
            ALTER TABLE "unavailable_period"
            ALTER COLUMN "from" TYPE TIMESTAMP USING "from" AT TIME ZONE 'UTC'
        `);
    await queryRunner.query(`
            ALTER TABLE "unavailable_period"
            ALTER COLUMN "to" TYPE TIMESTAMP USING "to" AT TIME ZONE 'UTC'
        `);

    await queryRunner.query(
      `CREATE INDEX "IDX_ef05556c304ed63583fc0881f6" ON "unavailable_period" ("from")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9c0cfc34d4336f986190f903b7" ON "unavailable_period" ("to")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_9c0cfc34d4336f986190f903b7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ef05556c304ed63583fc0881f6"`);

    // Revert column types back to TIMESTAMP WITH TIME ZONE
    await queryRunner.query(`
            ALTER TABLE "unavailable_period"
            ALTER COLUMN "to" TYPE TIMESTAMP WITH TIME ZONE USING "to" AT TIME ZONE 'UTC'
        `);
    await queryRunner.query(`
            ALTER TABLE "unavailable_period"
            ALTER COLUMN "from" TYPE TIMESTAMP WITH TIME ZONE USING "from" AT TIME ZONE 'UTC'
        `);

    await queryRunner.query(
      `CREATE INDEX "IDX_9c0cfc34d4336f986190f903b7" ON "unavailable_period" ("to")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ef05556c304ed63583fc0881f6" ON "unavailable_period" ("from")`,
    );
  }
}
