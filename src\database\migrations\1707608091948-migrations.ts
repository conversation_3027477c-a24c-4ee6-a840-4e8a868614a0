import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1707608091948 implements MigrationInterface {
  name = 'Migrations1707608091948';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "pricelistId" integer`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "vehicleType" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "managerCompanyName" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "managerPhoneNumber" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "dateFrom" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "dateTo" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "siteAddress" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "amountOfConcrete" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "balance" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "extraCementBags" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "rigidPipeLength" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "flexiblePipeLength80Mm" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "flexiblePipeLength90Mm" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "distance" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_b639b0e8a6d8095d93f7397adfb" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_b639b0e8a6d8095d93f7397adfb"`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "distance" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "flexiblePipeLength90Mm" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "flexiblePipeLength80Mm" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "rigidPipeLength" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "extraCementBags" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "balance" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "amountOfConcrete" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "siteAddress" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "dateTo" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "dateFrom" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "managerPhoneNumber" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ALTER COLUMN "managerCompanyName" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "vehicleType" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "pricelistId"`);
  }
}
