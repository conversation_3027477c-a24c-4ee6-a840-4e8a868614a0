const groupBy = `
GROUP BY
  "company"."id"
`;

export const getCompaniesQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT 
      "company".*
      ${selects ? `, ${selects}` : ''}
    FROM "company"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getCompanyCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "company"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  users: {
    join: `LEFT JOIN "user" AS "users" ON "company"."id" = "users"."companyId"`,
    select: `COALESCE(json_agg(DISTINCT jsonb_build_object(
      'id', "users"."id",
      'firstName', "users"."firstName",
      'lastName', "users"."lastName",
      'email', "users"."email"
    )) FILTER (WHERE "users"."id" IS NOT NULL), '[]') AS "users"`,
  },
  settings: {
    join: `
      LEFT JOIN "company_settings" AS "settings" ON "company"."id" = "settings"."companyId"
      LEFT JOIN "work_schedule_settings" AS "workSchedules"
        ON "workSchedules"."companySettingsId" = "settings"."id"
      LEFT JOIN "company_holiday" AS "holidays"
        ON "holidays"."companySettingsId" = "settings"."id"
      LEFT JOIN "cancellation_settings"
        ON "settings"."cancellationId" = "cancellation_settings"."id"
    `,
    select: `jsonb_build_object(
      'id', "settings"."id",
      'workSchedules', COALESCE(json_agg(DISTINCT jsonb_build_object(
        'id', "workSchedules"."id",
        'day', "workSchedules"."day",
        'startTime', "workSchedules"."startTime",
        'endTime', "workSchedules"."endTime"
      ))
      FILTER (WHERE "workSchedules"."id" IS NOT NULL),
      '[]'),
      'holidays', COALESCE(json_agg(DISTINCT jsonb_build_object(
        'id', "holidays"."id",
        'date', "holidays"."date",
        'reason', "holidays"."reason"
      ))
      FILTER (WHERE "holidays"."id" IS NOT NULL),
      '[]'),
      'cancellation', CASE
        WHEN "cancellation_settings"."id" IS NULL THEN NULL
        ELSE jsonb_build_object(
          'id', "cancellation_settings"."id",
          'cancellationWindow', "cancellation_settings"."cancellationWindow",
          'reducedCancellationWindow', "cancellation_settings"."reducedCancellationWindow",
          'minTimeBetweenJobs', "cancellation_settings"."minTimeBetweenJobs"
        )
      END
    ) AS "settings"`,
    groupBy: `"settings"."id", "cancellation_settings"."id"`,
  },
  dispatcherPartners: {
    join: `
      LEFT JOIN (
        SELECT
          "partner"."id" AS "partnerId",
          "partner"."status",
          "partner"."dispatcherCompanyId",
          "partner"."operatorCompanyId",
          COALESCE(
            json_agg(
              DISTINCT jsonb_build_object(
                'id', "dispatcherContainer"."id",
                'title', "dispatcherContainer"."title",
                'pricelists', dispatcher_pricelist_agg."pricelists"
              )
            ) FILTER (WHERE "dispatcherContainer"."id" IS NOT NULL),
            '[]'
          ) AS "containerPricelists"
        FROM
          "partner"
        LEFT JOIN
          "partner_container_pricelists" AS "pcp" ON "partner"."id" = "pcp"."partnerId"
        LEFT JOIN
          "container_pricelists" AS "dispatcherContainer" ON "pcp"."containerPricelistsId" = "dispatcherContainer"."id"
        LEFT JOIN LATERAL (
          SELECT
            COALESCE(json_agg(DISTINCT to_jsonb(pricelists)) FILTER (WHERE "pricelists"."id" IS NOT NULL), '[]') AS "pricelists"
          FROM
            "pricelist" AS "pricelists"
          WHERE
            "pricelists"."containerId" = "dispatcherContainer"."id"
        ) dispatcher_pricelist_agg ON true
        WHERE
          "partner"."dispatcherCompanyId" IS NOT NULL
        GROUP BY
          "partner"."id", "partner"."status", "partner"."dispatcherCompanyId", "partner"."operatorCompanyId"
      ) "dispatcherPartners" ON "company"."id" = "dispatcherPartners"."dispatcherCompanyId"
    `,
    select: `
      COALESCE(json_agg(DISTINCT jsonb_build_object(
        'partnerId', "dispatcherPartners"."partnerId",
        'status', "dispatcherPartners"."status",
        'dispatcherCompanyId', "dispatcherPartners"."dispatcherCompanyId",
        'operatorCompanyId', "dispatcherPartners"."operatorCompanyId",
        'containerPricelists', "dispatcherPartners"."containerPricelists"
      )) FILTER (WHERE "dispatcherPartners"."partnerId" IS NOT NULL), '[]') AS "dispatcherPartners"
    `,
  },
  operatorPartners: {
    join: `
      LEFT JOIN (
        SELECT
          "partner"."id" AS "partnerId",
          "partner"."status",
          "partner"."dispatcherCompanyId",
          "partner"."operatorCompanyId",
          COALESCE(
            json_agg(
              DISTINCT jsonb_build_object(
                'id', "operatorContainer"."id",
                'title', "operatorContainer"."title",
                'pricelists', operator_pricelist_agg."pricelists"
              )
            ) FILTER (WHERE "operatorContainer"."id" IS NOT NULL),
            '[]'
          ) AS "containerPricelists"
        FROM
          "partner"
        LEFT JOIN
          "partner_container_pricelists" AS "pcp" ON "partner"."id" = "pcp"."partnerId"
        LEFT JOIN
          "container_pricelists" AS "operatorContainer" ON "pcp"."containerPricelistsId" = "operatorContainer"."id"
        LEFT JOIN LATERAL (
          SELECT
            COALESCE(json_agg(DISTINCT to_jsonb("operatorPricelist")) FILTER (WHERE "operatorPricelist"."id" IS NOT NULL), '[]') AS "pricelists"
          FROM
            "pricelist" AS "operatorPricelist"
          WHERE
            "operatorPricelist"."containerId" = "operatorContainer"."id"
        ) operator_pricelist_agg ON true
        WHERE
          "partner"."operatorCompanyId" IS NOT NULL
        GROUP BY
          "partner"."id", "partner"."status", "partner"."dispatcherCompanyId", "partner"."operatorCompanyId"
      ) "operatorPartners" ON "company"."id" = "operatorPartners"."operatorCompanyId"
    `,
    select: `
      COALESCE(json_agg(DISTINCT jsonb_build_object(
        'partnerId', "operatorPartners"."partnerId",
        'status', "operatorPartners"."status",
        'dispatcherCompanyId', "operatorPartners"."dispatcherCompanyId",
        'operatorCompanyId', "operatorPartners"."operatorCompanyId",
        'containerPricelists', "operatorPartners"."containerPricelists"
      )) FILTER (WHERE "operatorPartners"."partnerId" IS NOT NULL), '[]') AS "operatorPartners"
    `,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
