import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';

@ValidatorConstraint({ name: 'IsPasswordMatching', async: false })
export class IsPasswordMatchingConstraint implements ValidatorConstraintInterface {
  validate(newPasswordConfirmation: string, args: ValidationArguments): boolean {
    const [relatedPropertyName] = args.constraints;
    const newPassword = (args.object as Record<string, string>)[relatedPropertyName];
    return newPassword === newPasswordConfirmation;
  }

  defaultMessage(): string {
    return 'Password confirmation does not match the new password.';
  }
}

export function IsPasswordMatching(property: string, validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string): void {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [property],
      validator: IsPasswordMatchingConstraint,
    });
  };
}
