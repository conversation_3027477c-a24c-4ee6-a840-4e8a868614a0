import { Entity, Column, OneToOne, Index, JoinColumn } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { CompanySettings } from './company-settings.entity';

@Entity()
export class CancellationSettings extends BaseEntity {
  @Column({ type: 'interval' })
  cancellationWindow: string;

  @Column({ type: 'interval' })
  reducedCancellationWindow: string;

  @Column({ type: 'interval' })
  minTimeBetweenJobs: string;

  @Column()
  @Index()
  settingsId: number;

  @OneToOne(() => CompanySettings, entity => entity.cancellation, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'settingsId' })
  settings: CompanySettings;
}
