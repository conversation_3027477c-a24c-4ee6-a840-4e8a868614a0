import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1764182667916 implements MigrationInterface {
    name = 'Migrations1764182667916'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "isHidden" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "isHidden"`);
    }

}
