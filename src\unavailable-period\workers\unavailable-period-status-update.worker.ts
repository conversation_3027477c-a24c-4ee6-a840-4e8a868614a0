import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QUEUES } from 'src/libs/bullmq/constants';
import { UsersService } from 'src/users/v1/users.service';
import { VehicleService } from 'src/vehicle/v1/vehicle.service';

@Processor(QUEUES.UNAVAILABLE_PERIOD_STATUS_UPDATE)
export class UnavailablePeriodStatusUpdateConsumer extends WorkerHost {
  private readonly logger: Logger;

  constructor(
    private readonly vehicleService: VehicleService,
    private readonly usersService: UsersService,
  ) {
    super();
    this.logger = new Logger(UnavailablePeriodStatusUpdateConsumer.name);
  }

  /**
   * Processes a status update job.
   * Expected job data:
   * {
   *   unavailablePeriodId: string (or number),
   *   vehicleIds: number[],
   *   newStatus: string   // e.g., 'INACTIVE' or 'ACTIVE'
   * }
   */
  async process(job: Job<any>): Promise<any> {
    this.logger.log(
      `Job id:${job.id} is being processed for period ${job.data.unavailablePeriodId}`,
    );
    const { unavailablePeriodId, vehicleIds, operatorId, newStatus } = job.data;
    if (vehicleIds?.length) {
      await this.vehicleService.updateVehicleStatus(vehicleIds, newStatus);
      this.logger.log(
        `Updated vehicles [${vehicleIds.join(
          ', ',
        )}] to ${newStatus} for period ${unavailablePeriodId}`,
      );
    }
    if (operatorId) {
      await this.usersService.updateOperatorStatus([operatorId], newStatus);
      this.logger.log(
        `Updated operator ${operatorId} to ${newStatus} for period ${unavailablePeriodId}`,
      );
    }
    return { updated: true };
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job<unknown>) {
    this.logger.log(`Job id:${job.id} completed`);
  }

  @OnWorkerEvent('failed')
  async onFailed(job: Job<any>, error: Error) {
    this.logger.error(`Job id:${job.id} failed`, error.stack);
  }

  @OnWorkerEvent('error')
  onError(job: Job<unknown>) {
    this.logger.error(`Job id:${job.id} encountered an error`);
  }
}
