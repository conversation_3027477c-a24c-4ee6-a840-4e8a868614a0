import { VehicleType, VehicleTypeCode } from 'src/common/constants/vehicle-type.enum';
import { Vehicle } from '../entities/vehicle.entity';

export class VehicleUtils {
  static valueRanges = {
    boomSize: {
      min: 0,
      max: 100,
    },
    weight: {
      min: 0,
      max: 100,
    },
    height: {
      min: 0,
      max: 10,
    },
    length: {
      min: 0,
      max: 100,
    },
    width: {
      min: 0,
      max: 10,
    },
    maxVerticalReach: {
      min: 0,
      max: 100,
    },
    maxHorizontalReach: {
      min: 0,
      max: 100,
    },
    endHoseLength: {
      min: 0,
      max: 100,
    },
    maxFlowRate: {
      min: 0,
      max: 500,
    },
    maxConcretePressure: {
      min: 0,
      max: 500,
    },
    availableFlexiblePipeLength80Mm: {
      min: 0,
      max: 500,
    },
    availableFlexiblePipeLength90Mm: {
      min: 0,
      max: 500,
    },
    availableFlexiblePipeLength100Mm: {
      min: 0,
      max: 500,
    },
    availableFlexiblePipeLength120Mm: {
      min: 0,
      max: 500,
    },
    availableRigidPipeLength: {
      min: 0,
      max: 500,
    },
    maxDownwardReach: {
      min: 0,
      max: 100,
    },
    numberOfBoomSections: {
      min: 0,
      max: 100,
    },
    minUnfoldingHeight: {
      min: 0,
      max: 100,
    },
    boomRotation: {
      min: -500,
      max: 500,
    },
    frontOutriggerSpan: {
      min: 0,
      max: 100,
    },
    rearOutriggerSpan: {
      min: 0,
      max: 100,
    },
    frontPressureOnOutrigger: {
      min: 0,
      max: 100,
    },
    rearPressureOnOutrigger: {
      min: 0,
      max: 100,
    },
    completedJobs: {
      min: 0,
      max: 10000,
    },
  };

  static vehicleTypeCodeMap = {
    [VehicleType.PUMP]: VehicleTypeCode.PUMP,
    [VehicleType.CITY_PUMP]: VehicleTypeCode.CITY_PUMP,
    [VehicleType.MIXO_PUMP]: VehicleTypeCode.MIXO_PUMP,
    [VehicleType.STATIONARY_PUMP]: VehicleTypeCode.STATIONARY_PUMP,
  };

  static getVehicleUniqueId(vehicle: Vehicle): string {
    return `${this.vehicleTypeCodeMap[vehicle.type] || ''} ${vehicle.manager?.country || ''} ${
      vehicle.boomSize || ''
    } ${vehicle.manager?.company?.name || ''}`;
  }
}
