// Recursive function to handle filters dynamically, including nested objects
export const unpackAndApplyFilters = (queryBuilder, filters, prefix = 'vehicle') => {
  Object.entries(filters).forEach(([key, value]) => {
    // Determine the path for the field (e.g., prefix: job, key: status)
    key = key.endsWith('Ids') ? key.slice(0, -1) : key;
    const fieldPath = `${prefix}.${key}`;

    if (value !== undefined && value !== null) {
      if (typeof value === 'object' && !Array.isArray(value)) {
        // If the value is an object, recurse into it (e.g., job: { status: []})
        unpackAndApplyFilters(queryBuilder, value, key);
      } else if (Array.isArray(value) && value.length > 0) {
        // Handle array of values (e.g., status: [RUNNING, WAITING] or vehicleIds: [1, 2])
        queryBuilder.andWhere(`${fieldPath} IN (:...${key})`, {
          [key]: value,
        });
      } else {
        // Handle single value filters
        queryBuilder.andWhere(`${fieldPath} = :${key}`, { [key]: value });
      }
    }
  });
};
