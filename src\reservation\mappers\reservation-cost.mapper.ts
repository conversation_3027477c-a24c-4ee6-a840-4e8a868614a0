import { JobWithSignature } from 'src/job/v1/interfaces/jobWithSignature';
import { ReservationCost } from '../v1/interface/reservation-cost.interface';

export function mapJobToReservationCost(job: JobWithSignature): ReservationCost {
  return {
    totalCost: job.totalCost || 0,
    baseFee: job.baseFee || 0,
    baseHourBreakdown: job.baseHourBreakdown,
    concreteCost: job.concreteCost || 0,
    pipe80mmCost: job.pipe80mmCost || 0,
    pipe90mmCost: job.pipe90mmCost || 0,
    pipe100mmCost: job.pipe100mmCost || 0,
    pipe120mmCost: job.pipe120mmCost || 0,
    barbotineCost: job.barbotineCost || 0,
    supplyOfTheChemicalSlushieCost: job.supplyOfTheChemicalSlushieCost || 0,
    cleaningFee: job.cleaningFee || 0,
    extraCementBagCost: job.extraCementBagCost || 0,
    secondTechnicianFee: job.secondTechnicianFee || 0,
    secondTechnicianHourBreakdown: job.secondTechnicianHourBreakdown,
    pumpTiersCost: job.pumpTiersCost || 0,
    chargedTransportRate: job.chargedTransportRate,
    additionalHourFee: job.additionalHourFee || 0,
    additionalHours: job.additionalHours || 0,
    additionalHourBreakdown: job.additionalHourBreakdown,
  };
}
