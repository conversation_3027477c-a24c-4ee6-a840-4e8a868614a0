import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1719422555948 implements MigrationInterface {
  name = 'Migrations1719422555948';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "operator_client" ALTER COLUMN "name" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "operator_client" ALTER COLUMN "lastName" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "operator_client" ALTER COLUMN "email" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "operator_client" ALTER COLUMN "phoneNumber" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "operator_client" ALTER COLUMN "companyName" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "operator_client" ALTER COLUMN "companyVatNumber" DROP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "operator_client" ALTER COLUMN "companyVatNumber" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "operator_client" ALTER COLUMN "companyName" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "operator_client" ALTER COLUMN "phoneNumber" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "operator_client" ALTER COLUMN "email" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "operator_client" ALTER COLUMN "lastName" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "operator_client" ALTER COLUMN "name" SET NOT NULL`);
  }
}
