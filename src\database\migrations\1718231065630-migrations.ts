import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1718231065630 implements MigrationInterface {
  name = 'Migrations1718231065630';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."job_location_status_enum" AS ENUM('NOT_STARTED', 'DRIVING_TO_SITE', 'SITE_ARRIVAL', 'SECURITY_VALIDATION', 'START_SETUP', 'END_SETUP', 'START_PUMPING', 'END_PUMPING', 'REPORT', 'SIGNATURE', 'START_CLEANUP', 'END_CLEANUP', 'LEAVE_SITE', 'COMPLETE', 'CANCELLED')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."job_location_state_enum" AS ENUM('RUNNING', 'SUCCESS', 'FAILURE', 'CANCELLED', 'WAITING')`,
    );
    await queryRunner.query(
      `CREATE TABLE "job_location" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "status" "public"."job_location_status_enum" NOT NULL DEFAULT 'NOT_STARTED', "state" "public"."job_location_state_enum" NOT NULL DEFAULT 'WAITING', "progress" integer NOT NULL DEFAULT '0', "latitude" double precision NOT NULL, "longitude" double precision NOT NULL, "jobId" integer NOT NULL, CONSTRAINT "PK_9331dcc9601546cc211e12d9be6" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0b31c3a9761f15fb173df4254d" ON "job_location" ("jobId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location" ADD CONSTRAINT "FK_0b31c3a9761f15fb173df4254d1" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job_location" DROP CONSTRAINT "FK_0b31c3a9761f15fb173df4254d1"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_0b31c3a9761f15fb173df4254d"`);
    await queryRunner.query(`DROP TABLE "job_location"`);
    await queryRunner.query(`DROP TYPE "public"."job_location_state_enum"`);
    await queryRunner.query(`DROP TYPE "public"."job_location_status_enum"`);
  }
}
