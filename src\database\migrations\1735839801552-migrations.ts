import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1735839801552 implements MigrationInterface {
  name = 'Migrations1735839801552';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" ALTER COLUMN "vat" SET DEFAULT '{"name":"Standard Pumping Services","percentage":21}'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" ALTER COLUMN "vat" DROP DEFAULT`);
  }
}
