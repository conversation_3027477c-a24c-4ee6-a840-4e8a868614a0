import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1732627540234 implements MigrationInterface {
  name = 'Migrations1732627540234';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job" RENAME COLUMN "estimatedTotalCost" TO "estimatedTotalCost_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD "estimatedTotalCost" double precision NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `UPDATE "job" SET "estimatedTotalCost" = "estimatedTotalCost_old"::double precision`,
    );
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "estimatedTotalCost_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job" RENAME COLUMN "estimatedTotalCost" TO "estimatedTotalCost_temp"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD "estimatedTotalCost" integer NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `UPDATE "job" SET "estimatedTotalCost" = ROUND("estimatedTotalCost_temp")`,
    );
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "estimatedTotalCost_temp"`);
  }
}
