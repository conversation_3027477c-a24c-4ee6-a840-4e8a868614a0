import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';

export enum PartnerCategoryTypes {
  id = '"partner"."id"',
  partnerDispatcherCompanyId = '"partner"."dispatcherCompanyId"',
  partnerOperatorCompanyId = '"partner"."operatorCompanyId"',
  status = '"partner"."status"',
  dispatcherCompanyId = '"dispatcherCompany"."id"',
  dispatcherCompanyName = '"dispatcherCompany"."name"',
  dispatcherCompanyAddress = '"dispatcherCompany"."address"',
  dispatcherCompanyVatNumber = '"dispatcherCompany"."vatNumber"',
  dispatcherCompanyContactEmail = '"dispatcherCompany"."contactEmail"',
  dispatcherCompanyPhoneNumber = '"dispatcherCompany"."phoneNumber"',
  operatorCompanyId = '"operatorCompany"."id"',
  operatorCompanyName = '"operatorCompany"."name"',
  operatorCompanyAddress = '"operatorCompany"."address"',
  operatorCompanyVatNumber = '"operatorCompany"."vatNumber"',
  operatorCompanyContactEmail = '"operatorCompany"."contactEmail"',
  operatorCompanyPhoneNumber = '"operatorCompany"."phoneNumber"',
  containerId = '"container"."id"',
  containerTitle = '"container"."title"',
}

export class PartnerSortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: PartnerCategoryTypes,
  })
  @IsEnum(PartnerCategoryTypes, { message: 'invalid sort field' })
  field: PartnerCategoryTypes;
}

export class PartnerExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: PartnerCategoryTypes,
  })
  @IsOptional()
  @IsEnum(PartnerCategoryTypes, { message: 'invalid filter category' })
  category?: PartnerCategoryTypes | string | null | undefined;
}
export class GetPartnerDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: PartnerSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PartnerSortItem)
  readonly sortModel: PartnerSortItem[];

  @ApiProperty({ required: true, isArray: true, type: PartnerExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PartnerExpression)
  readonly expressions: PartnerExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
