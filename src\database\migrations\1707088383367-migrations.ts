import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1707088383367 implements MigrationInterface {
  name = 'Migrations1707088383367';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "frontSideOpening" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rearSideOpening" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "invoicingPipesFrom"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "invoicingPipesFrom" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "pipeLengthForSecondTechnician"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "pipeLengthForSecondTechnician" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "pipeLengthForSecondTechnician"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "pipeLengthForSecondTechnician" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "invoicingPipesFrom"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "invoicingPipesFrom" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rearSideOpening"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "frontSideOpening"`);
  }
}
