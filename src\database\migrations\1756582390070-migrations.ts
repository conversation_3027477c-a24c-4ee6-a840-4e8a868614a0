import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1756582390070 implements MigrationInterface {
    name = 'Migrations1756582390070'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "vehicle" ADD "imageUrl" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "imageUrl"`);
    }

}
