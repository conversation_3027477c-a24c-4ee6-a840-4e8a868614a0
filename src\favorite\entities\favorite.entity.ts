import { BaseEntity } from '../../common/entities/base.entity';
import { Entity, Column, ManyToOne, Index, JoinColumn } from 'typeorm';
import { Company } from 'src/company/entities/company.entity';

@Entity()
export class Favorite extends BaseEntity {
  @Column({ nullable: false })
  @Index()
  dispatcherCompanyId: number;

  @Column({ nullable: false })
  @Index()
  operatorCompanyId: number;

  @ManyToOne(() => Company, company => company.favorites, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'dispatcherCompanyId' })
  dispatcherCompany: Company;

  @ManyToOne(() => Company, company => company.favoritedBy, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'operatorCompanyId' })
  operatorCompany: Company;
}
