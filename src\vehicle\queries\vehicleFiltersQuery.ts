import { unpackAndApplyFilters } from 'src/common/queries/queryFilters';

// Exported function to get vehicle filters query
export const getVehicleFiltersQuery = (queryBuilder, filters) => {
  // Add special cases or any additional filters if needed
  for (const [key, value] of Object.entries(filters)) {
    if (value !== undefined && value !== null) {
      const columnName = `vehicle.${key}`;
      if (key === 'siteAddress') {
        queryBuilder.andWhere(`${columnName} LIKE :${key}`, {
          [key]: `%${value}%`,
        });
      } else if (
        key === 'availableFlexiblePipeLength80Mm' ||
        key === 'availableFlexiblePipeLength90Mm' ||
        key === 'availableFlexiblePipeLength100Mm' ||
        key === 'availableFlexiblePipeLength120Mm'
      ) {
        queryBuilder.andWhere(`${columnName} >= :${key}`, { [key]: value });
      } else {
        switch (key) {
          default:
            // Default case for other filters
            unpackAndApplyFilters(queryBuilder, { [key]: value }, 'vehicle');
            break;
        }
      }
    }
  }
};
