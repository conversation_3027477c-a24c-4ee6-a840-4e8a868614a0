import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1711051437997 implements MigrationInterface {
  name = 'Migrations1711051437997';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."job_cleaning_enum" AS ENUM('Worksite', 'Central')`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "cleaning" "public"."job_cleaning_enum"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "cleaning"`);
    await queryRunner.query(`DROP TYPE "public"."job_cleaning_enum"`);
  }
}
