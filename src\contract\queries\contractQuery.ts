const groupBy = `
GROUP BY
  "contract"."id"
`;

export const getContractQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT 
      "contract".*
      ${selects ? `, ${selects}` : ''}
    FROM "contract"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getContractCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "contract"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  operatorCompany: {
    join: `LEFT JOIN "company" AS "operatorCompany" ON "contract"."operatorCompanyId" = "operatorCompany"."id"`,
    select: `jsonb_build_object(
      'id', "operatorCompany"."id",
      'name', "operatorCompany"."name"
    ) AS "operatorCompany"`,
    groupBy: `"operatorCompany"."id"`,
  },
  dispatcherCompany: {
    join: `LEFT JOIN "company" AS "dispatcherCompany" ON "contract"."dispatcherCompanyId" = "dispatcherCompany"."id"`,
    select: `jsonb_build_object(
      'id', "dispatcherCompany"."id",
      'name', "dispatcherCompany"."name",
      'country', "dispatcherCompany"."country"
    ) AS "dispatcherCompany"`,
    groupBy: `"dispatcherCompany"."id"`,
  },
  pricelist: {
    join: `
    LEFT JOIN "pricelist" AS "pricelist" ON "contract"."pricelistId" = "pricelist"."id"
    `,
    select: `to_jsonb(pricelist) AS "pricelist"`,
    groupBy: `"pricelist"."id"`,
  },
  vehicle: {
    join: `
    LEFT JOIN "vehicle" AS "vehicle" ON "contract"."vehicleId" = "vehicle"."id"
    `,
    select: `to_jsonb(vehicle) AS "vehicle"`,
    groupBy: `"vehicle"."id"`,
  },
  comments: {
    join: `
      LEFT JOIN "contract_comment" AS "comments" ON "contract"."id" = "comments"."contractId"
      LEFT JOIN "user" AS "user" ON "comments"."created_by" = "user"."id"
    `,
    select: `
      COALESCE(json_agg(
        DISTINCT jsonb_build_object(
          'id', "comments"."id",
          'content', "comments"."content",
          'created_at', "comments"."created_at",
          'updated_at', "comments"."updated_at",
          'created_by', "comments"."created_by",
          'user', jsonb_build_object(
            'id', "user"."id",
            'firstName', "user"."firstName",
            'lastName', "user"."lastName",
            'email', "user"."email"
          )
        )
      ) FILTER (WHERE "comments"."id" IS NOT NULL), '[]') AS "comments"
    `,
  },
  suspensionPeriods: {
    join: `
      LEFT JOIN "suspension_period" AS "suspensionPeriods" ON "contract"."id" = "suspensionPeriods"."contractId"
    `,
    select: `
      COALESCE(json_agg(
        DISTINCT to_jsonb("suspensionPeriods")
      ) FILTER (WHERE "suspensionPeriods"."id" IS NOT NULL), '[]') AS "suspensionPeriods"
    `,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
