import { WorkScheduleSettings } from 'src/company-settings/entities/work-schedule-settings.entity';
import { HourTypes, JobWithSignature } from 'src/job/v1/interfaces/jobWithSignature';
import { Reservation } from 'src/reservation/entities/reservation.entity';
import { classifyHours, parseTime } from 'src/reservation/helpers/work-hours.helper';
import { Cleaning } from '../constants/job.enum';
import { formatNumber } from './number-processing';
import { TransportRate } from 'src/pricelist/entities/pricelist.entity';
import { CancellationSettings } from 'src/company-settings/entities/cancellation-settings.entity';
import { CancellationType } from '../constants/cancellation.enum';

export function calculateTotalCost(
  reservation: Reservation,
  job: JobWithSignature,
  workSchedule: WorkScheduleSettings[],
  distanceInKm?: number,
): void {
  const { pricelist } = reservation;

  if (!pricelist) {
    throw new Error('Reservation does not have a pricelist!');
  }

  if (!workSchedule) {
    throw new Error('Invalid work schedule configuration.');
  }
  const {
    minimumM3Charged,
    minimumChargeFlatFee,
    packageFlatFee: rawPackageFlatFee,
    packageFlatFeeWeekend,
    packageFlatFeeNight,
    additionalHour,
    additionalHourNight,
    additionalHourWeekend,
    secondTechnicianHourFee: rawSecondTechnicianHourFee,
    secondTechnicianHourFeeWeekend,
    secondTechnicianHourFeeNight,
    pricePerMeterPumped: rawPricePerMeterPumped,
    pricePerMeterOfFlexiblePipeLength80Mm: rawPipeLength80Mm,
    pricePerMeterOfFlexiblePipeLength90Mm: rawPipeLength90Mm,
    pricePerMeterOfFlexiblePipeLength100Mm: rawPipeLength100Mm,
    pricePerMeterOfRigidPipeLength120Mm: rawRigidPipeLength120Mm,
    barbotine,
    supplyOfTheChemicalSlushie,
    cleaningFee: rawCleaningFee,
    extraCementBagPrice,
    pumpTiers,
    packageFlatFeeDuration: rawFlatFeeDuration,
  } = pricelist;

  const packageFlatFee = rawPackageFlatFee || 0;
  const secondTechnicianHourFee = rawSecondTechnicianHourFee || 0;
  const pricePerMeterPumped = rawPricePerMeterPumped || 0;
  const pricePerMeterOfFlexiblePipeLength80Mm = rawPipeLength80Mm || 0;
  const pricePerMeterOfFlexiblePipeLength90Mm = rawPipeLength90Mm || 0;
  const pricePerMeterOfFlexiblePipeLength100Mm = rawPipeLength100Mm || 0;
  const pricePerMeterOfRigidPipeLength120Mm = rawRigidPipeLength120Mm || 0;
  const cleaningFee = rawCleaningFee || 0;
  const packageFlatFeeDuration = rawFlatFeeDuration || 4;

  // Calculate actual job hours
  const jobStart = new Date(job.start);
  const jobEnd = new Date(job.end);
  job.hourDuration =
    job.start && job.end ? (jobEnd.getTime() - jobStart.getTime()) / (1000 * 60 * 60) : 0;

  // Calculate estimated hours using reservation dates
  const reservationStart = new Date(reservation.dateFrom);
  const reservationEnd = new Date(reservation.dateTo);
  job.estimateHourDuration =
    reservation.dateFrom && reservation.dateTo
      ? (reservationEnd.getTime() - reservationStart.getTime()) / (1000 * 60 * 60)
      : 0;
  if (pricelist.isContract) {
    // For contract reservations, the detailed hourly breakdown is moot because
    // the pricing is determined per day with:
    //   - A flat Day contract fee (applied once per day)
    //   - An overtime rate applied if aggregated reservation hours exceed contract duration.
    // In this reservation calculation we simply record the overall duration.
    //
    // (The aggregated contract fee and overtime fee will be calculated
    // during the invoice generation phase, where all reservations for the day are summed.)

    // Simply record the total job duration.
    job.baseFee = 0; // No reservation base fee is applied.
    job.baseHourBreakdown = {
      work: formatNumber(job.hourDuration),
      night: 0,
      weekend: 0,
    };
    job.estimateBaseHourBreakdown = { ...job.baseHourBreakdown };
  } else {
    const rates = {
      work: packageFlatFee,
      night: packageFlatFeeNight ?? packageFlatFee,
      weekend: packageFlatFeeWeekend ?? packageFlatFee,
    };

    if (distanceInKm) {
      job.chargedTransportRate = getTransportRate(distanceInKm, pricelist.transportRates || []);
    }

    // Base Fee & Breakdown
    if (job.report.amountOfConcrete < minimumM3Charged) {
      // For small jobs, use the minimum charge.
      const baseFeeDuration = Math.min(job.hourDuration, packageFlatFeeDuration);
      job.baseFee = (baseFeeDuration / packageFlatFeeDuration) * minimumChargeFlatFee || 0;
      // For consistency, assign entire estimated flat-fee duration to work hours.
      job.baseHourBreakdown = {
        work: formatNumber(baseFeeDuration),
        night: 0,
        weekend: 0,
      };

      // For small jobs, use the minimum charge for the estimated values as well.
      const estimateBaseFeeDuration = Math.min(job.estimateHourDuration, packageFlatFeeDuration);
      job.estimateBaseHourBreakdown = {
        work: formatNumber(estimateBaseFeeDuration),
        night: 0,
        weekend: 0,
      };
    } else {
      const result = calculateBaseAndBreakdown(
        jobStart,
        jobEnd,
        job.hourDuration,
        packageFlatFeeDuration,
        rates,
        workSchedule,
        {
          work: additionalHour,
          night: additionalHourNight,
          weekend: additionalHourWeekend,
        },
      );
      job.baseFee = result.baseFee;
      job.baseHourBreakdown = result.baseHourBreakdown;
      job.additionalHourBreakdown = result.additionalHourBreakdown;
      job.additionalHours = result.additionalHours;
      job.additionalHourFee = result.additionalHoursFee;

      const estimateResult = calculateBaseAndBreakdown(
        reservationStart,
        reservationEnd,
        job.estimateHourDuration,
        packageFlatFeeDuration,
        rates,
        workSchedule,
        {
          work: additionalHour,
          night: additionalHourNight,
          weekend: additionalHourWeekend,
        },
      );
      job.estimateBaseHourBreakdown = estimateResult.baseHourBreakdown;
      job.estimateAdditionalHourBreakdown = estimateResult.additionalHourBreakdown;
    }
  }
  // --- Second Technician Calculations ---
  if (job.enlistSecondTechnician && job.report.secondTechnicianMinutes) {
    const technicianClassification = classifyHours(
      jobStart,
      new Date(jobStart.getTime() + job.report.secondTechnicianMinutes * 60 * 1000), // Convert minutes to milliseconds
      workSchedule,
    );

    job.secondTechnicianHourBreakdown = {
      work: formatNumber(technicianClassification.workHours),
      night: formatNumber(technicianClassification.nightHours),
      weekend: formatNumber(technicianClassification.weekendHours),
    };

    job.secondTechnicianFee = formatNumber(
      Number(job.secondTechnicianHourBreakdown.work) * secondTechnicianHourFee +
        Number(job.secondTechnicianHourBreakdown.night) *
          (secondTechnicianHourFeeNight ?? secondTechnicianHourFee) +
        Number(job.secondTechnicianHourBreakdown.weekend) *
          (secondTechnicianHourFeeWeekend ?? secondTechnicianHourFee),
    );
  }

  // --- Other Costs ---
  job.concreteCost = (job.report.amountOfConcrete || 0) * pricePerMeterPumped;

  job.pipe80mmCost =
    (job.report.flexiblePipeLength80Mm || 0) * pricePerMeterOfFlexiblePipeLength80Mm;
  job.pipe90mmCost =
    (job.report.flexiblePipeLength90Mm || 0) * pricePerMeterOfFlexiblePipeLength90Mm;
  job.pipe100mmCost =
    (job.report.rigidPipeLength100Mm || 0) * pricePerMeterOfFlexiblePipeLength100Mm;
  job.pipe120mmCost = (job.report.rigidPipeLength120Mm || 0) * pricePerMeterOfRigidPipeLength120Mm;

  job.barbotineCost = job.barbotine ? barbotine || 0 : 0;

  job.supplyOfTheChemicalSlushieCost = job.supplyOfTheChemicalSlushie
    ? supplyOfTheChemicalSlushie || 0
    : 0;

  job.cleaningFee = job.cleaning === Cleaning.CENTRAL ? cleaningFee || 0 : 0;

  job.extraCementBagCost = job.report.extraCementBags
    ? (job.report.cementBags || 0) * extraCementBagPrice
    : 0;

  if (Array.isArray(pumpTiers) && job.report.amountOfConcrete !== undefined) {
    const amount = job.report.amountOfConcrete;
    const selectedTier = pumpTiers.find(tier => amount >= tier.minimum && amount <= tier.maximum);
    job.pumpTiersCost = selectedTier ? selectedTier.price * amount : 0;
  } else {
    job.pumpTiersCost = 0;
  }

  job.totalCost =
    job.baseFee +
    job.concreteCost +
    job.pipe80mmCost +
    job.pipe90mmCost +
    job.pipe100mmCost +
    job.pipe120mmCost +
    job.barbotineCost +
    job.supplyOfTheChemicalSlushieCost +
    job.cleaningFee +
    job.extraCementBagCost +
    (job.secondTechnicianFee || 0) +
    job.pumpTiersCost +
    (job.chargedTransportRate?.tariff || 0) +
    (job.additionalHourFee || 0) +
    (job.contractDayFee || 0) +
    (job.contractOvertimeFee || 0);

  if (isNaN(job.totalCost)) {
    throw new Error(`Invalid total calculation for reservation ID: ${reservation.id}`);
  }
}

export function calculateCancellationCost(
  reservation: Reservation,
  job: JobWithSignature,
  cancellationSettings: Partial<CancellationSettings>,
): void {
  const { pricelist } = reservation;
  if (!pricelist) {
    throw new Error('Pricelist was not provided!');
  }

  const { cancellationFee, dayCancellationFee } = pricelist;
  const reservationStart = new Date(reservation.dateFrom);

  // Build the 2-days-before threshold (cancellation window)
  const cancellationThreshold = new Date(reservationStart);
  cancellationThreshold.setDate(cancellationThreshold.getDate() - 2);
  // Set the time on the cancellation threshold
  const cancellationTimeMinutes = parseTime(cancellationSettings.cancellationWindow);
  cancellationThreshold.setHours(
    Math.floor(cancellationTimeMinutes / 60),
    cancellationTimeMinutes % 60,
    0,
    0,
  );

  // Build the 1-day-before threshold (reduced cancellation window)
  const reducedThreshold = new Date(reservationStart);
  reducedThreshold.setDate(reducedThreshold.getDate() - 1);
  const reducedTimeMinutes = parseTime(cancellationSettings.reducedCancellationWindow);
  reducedThreshold.setHours(Math.floor(reducedTimeMinutes / 60), reducedTimeMinutes % 60, 0, 0);

  // Get current dateTime
  const now = new Date();

  // Determine cost based on the thresholds:
  if (now < cancellationThreshold) {
    // Current time is before 2 days before the reservation
    job.totalCost = 0;
  } else if (now >= cancellationThreshold && now < reducedThreshold) {
    // Current time is between the 2-day mark and the 1-day mark.
    job.totalCost = cancellationFee;
    job.cancellationFeeType = CancellationType.BASE;
  } else if (now >= reducedThreshold && now < reservationStart) {
    // Current time is between the day before (reduced) and the reservation start.
    job.totalCost = dayCancellationFee;
    job.cancellationFeeType = CancellationType.REDUCED;
  } else {
    // For clarity: if we are past the reservation time.
    job.totalCost = 0;
  }
}

function calculateBaseAndBreakdown(
  startTime: Date,
  endTime: Date,
  totalDuration: number,
  flatFeeDuration: number,
  rates: HourTypes,
  workSchedule: WorkScheduleSettings[],
  additionalHourFee?: HourTypes,
): {
  baseHourBreakdown: HourTypes;
  baseFee: number;
  additionalHourBreakdown: HourTypes;
  additionalHours: number;
  additionalHoursFee: number;
} {
  const hourClassification = classifyHours(startTime, endTime, workSchedule);

  // Rounding and classifying full breakdown into work, night, and weekend
  const fullHourBreakdown = {
    work: formatNumber(hourClassification.workHours),
    night: formatNumber(hourClassification.nightHours),
    weekend: formatNumber(hourClassification.weekendHours),
  };

  // Allocate the base fee hours across work, night, and weekend
  const baseDuration = Math.min(totalDuration, flatFeeDuration);
  let remainingBaseDuration = baseDuration;
  const baseWork = Math.min(Number(fullHourBreakdown.work), remainingBaseDuration);
  remainingBaseDuration -= baseWork;
  const baseNight = Math.min(Number(fullHourBreakdown.night), remainingBaseDuration);
  remainingBaseDuration -= baseNight;
  const baseWeekend = Math.min(Number(fullHourBreakdown.weekend), remainingBaseDuration);

  const baseHourBreakdown = {
    work: formatNumber(baseWork),
    night: formatNumber(baseNight),
    weekend: formatNumber(baseWeekend),
  };

  // Calculate the base fee using the designated fee rates
  const baseFee =
    (Number(baseHourBreakdown.work) / flatFeeDuration) * rates.work +
    (Number(baseHourBreakdown.night) / flatFeeDuration) * rates.night +
    (Number(baseHourBreakdown.weekend) / flatFeeDuration) * rates.weekend;

  const additionalWork = Number(fullHourBreakdown.work) - baseWork;
  const additionalNight = Number(fullHourBreakdown.night) - baseNight;
  const additionalWeekend = Number(fullHourBreakdown.weekend) - baseWeekend;

  const additionalHourBreakdown = {
    work: formatNumber(additionalWork),
    night: formatNumber(additionalNight),
    weekend: formatNumber(additionalWeekend),
  };

  const additionalHours = totalDuration - baseDuration;
  const additionalHourWork = additionalHourFee.work || 0;
  const additionalHoursFee =
    Number(additionalHourBreakdown.work) * additionalHourWork +
    Number(additionalHourBreakdown.night) * (additionalHourFee.night ?? additionalHourWork) +
    Number(additionalHourBreakdown.weekend) * (additionalHourFee.weekend ?? additionalHourWork);

  return {
    baseHourBreakdown,
    baseFee,
    additionalHourBreakdown,
    additionalHours,
    additionalHoursFee,
  };
}

function getTransportRate(distanceInKm: number, tariffs: TransportRate[]): TransportRate {
  for (const t of tariffs) {
    if (distanceInKm >= t.from && distanceInKm <= t.to) {
      return t;
    }
  }
  return tariffs[tariffs.length - 1];
}
