import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { SortItem, Expression } from 'src/libs/helpers/CeQuery';

export enum CompanyCategoryTypes {
  id = '"company"."id"',
  category = '"company"."category"',
  contactEmail = '"company"."contactEmail"',
  dispatcherPartnersStatus = '"dispatcherPartners"."status"',
  dispatcherPartnersOperatorCompanyId = '"dispatcherPartners"."operatorCompanyId"',
}

export class CompanySortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: CompanyCategoryTypes,
  })
  @IsEnum(CompanyCategoryTypes, { message: 'invalid sort field' })
  field: CompanyCategoryTypes;
}

export class CompanyExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: CompanyCategoryTypes,
  })
  @IsOptional()
  @IsEnum(CompanyCategoryTypes, { message: 'invalid filter category' })
  category?: CompanyCategoryTypes | string | null | undefined;
}
export class GetCompanyDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: CompanySortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CompanySortItem)
  readonly sortModel: CompanySortItem[];

  @ApiProperty({ required: true, isArray: true, type: CompanyExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CompanyExpression)
  readonly expressions: CompanyExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
