import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1735323420104 implements MigrationInterface {
  name = 'Migrations1735323420104';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "rubberEndHoseLength"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rubberEndHoseLength"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rubberEndHoseLength" double precision`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" ADD "rubberEndHoseLength" double precision`,
    );
  }
}
