import { JobStatus } from 'src/common/constants/job.enum';
import { SearchVehicleType } from 'src/common/types/search-vehicle.type';
import { SelectQueryBuilder } from 'typeorm';
import { Vehicle } from '../entities/vehicle.entity';
import { VehicleReservationsResponse } from '../v1/interfaces/get-vehicles-reservations.interface';
import { VehicleMapper } from '../v1/vehicle.mapper';
import { formatDate } from 'src/common/helpers/formatDate';
import { ContractedStatus } from 'src/common/constants/user.enum';
import { Role } from 'src/common/constants/roles.enum';

export const getVehiclesReservationsQuery = async (
  queryBuilder: SelectQueryBuilder<Vehicle>,
  dispatcherId: number,
  filters: {
    type?: string;
    boomSize?: number;
    siteAddress?: string;
    operatorName?: string;
    clientName?: string;
    dateFrom?: Date;
    dateTo?: Date;
    operatorCompanyIds?: number[];
  },
  pricelistIds: number[],
  companyId: number,
  searchType: SearchVehicleType = 'All',
  isDispatcher: boolean,
  roleId?: number,
): Promise<[VehicleReservationsResponse[], number]> => {
  const {
    type,
    boomSize,
    siteAddress,
    operatorName,
    clientName,
    dateFrom,
    dateTo,
    operatorCompanyIds,
  } = filters;

  queryBuilder
    .select([
      'vehicle.id',
      'vehicle.uniqueIdentificationNumber',
      'vehicle.boomSize',
      'vehicle.type',
      'vehicle.imageUrl',
      'operator.id',
      'operator.firstName',
      'operator.lastName',
      'operator.phoneNumber',
      'operator.status',
      'manager.id',
      'manager.companyId',
      'company.name',
      'company.phoneNumber',
      'company.contactEmail',
      'companySettings.id',
      'companyHolidays.id',
      'companyHolidays.date',
      'companyHolidays.reason',
      'reservations.id',
      'reservations.dateFrom',
      'reservations.dateTo',
      'reservations.hasBeenRead',
      'reservations.siteAddress',
      'reservations.city',
      'reservations.pricelistId',
      'reservations.clientDetails',
      'job.amountOfConcrete',
      'job.flowRate',
      'job.status',
      'job.state',
      'dispatcher.id',
      'dispatcherCompany.id',
      'reservationOperator.companyId',
      'reservationOperator.firstName',
    ])
    .leftJoin('vehicle.operator', 'operator')
    .leftJoin('vehicle.manager', 'manager')
    .leftJoin('manager.company', 'company')
    .leftJoin('company.settings', 'companySettings')
    .leftJoin('companySettings.holidays', 'companyHolidays')
    .leftJoin(
      'vehicle.reservations',
      'reservations',
      ':dateFrom BETWEEN reservations.dateFrom AND reservations.dateTo OR ' +
        ':dateTo BETWEEN reservations.dateFrom AND reservations.dateTo OR ' +
        'reservations.dateFrom BETWEEN :dateFrom AND :dateTo OR ' +
        'reservations.dateTo BETWEEN :dateFrom AND :dateTo ',
      { dateFrom, dateTo },
    )
    .leftJoin('reservations.job', 'job')
    .leftJoin('reservations.dispatcher', 'dispatcher')
    .leftJoin('reservations.operator', 'reservationOperator')
    .leftJoin('dispatcher.company', 'dispatcherCompany');

  if (roleId === Role.DISPATCHER || roleId === Role.DISPATCHER_MANAGER) {
    if (dateFrom && dateTo) {
      const hiddenCheckDateFrom = new Date(dateFrom).toISOString();
      const hiddenCheckDateTo = new Date(dateTo).toISOString();

      queryBuilder.andWhere(
        `NOT EXISTS (
          SELECT 1 FROM "unavailable_period_vehicles" upv
          INNER JOIN "unavailable_period" up ON upv."unavailablePeriodId" = up.id
          WHERE upv."vehicleId" = vehicle.id
            AND up."isHidden" = true
            AND up."from" <= :hiddenCheckDateTo
            AND up."to" >= :hiddenCheckDateFrom
            AND up."deleted_at" IS NULL
        )`,
        {
          hiddenCheckDateFrom: hiddenCheckDateFrom,
          hiddenCheckDateTo: hiddenCheckDateTo,
        },
      );
    } else {
      const currentDate = new Date().toISOString();

      queryBuilder.andWhere(
        `NOT EXISTS (
          SELECT 1 FROM "unavailable_period_vehicles" upv
          INNER JOIN "unavailable_period" up ON upv."unavailablePeriodId" = up.id
          WHERE upv."vehicleId" = vehicle.id
            AND up."isHidden" = true
            AND up."from" <= :hiddenCheckDate
            AND up."to" >= :hiddenCheckDate
            AND up."deleted_at" IS NULL
        )`,
        { hiddenCheckDate: currentDate },
      );
    }
  }

  if (dateFrom && dateTo) {
    queryBuilder.leftJoinAndSelect(
      'vehicle.unavailablePeriods',
      'unavailablePeriod',
      'unavailablePeriod.from <= :dateToUnavailable AND unavailablePeriod.to >= :dateFromUnavailable AND unavailablePeriod.isHidden = false',
      {
        dateFromUnavailable: new Date(dateFrom).toISOString(),
        dateToUnavailable: new Date(dateTo).toISOString(),
      },
    );
  } else {
    queryBuilder.leftJoinAndSelect(
      'vehicle.unavailablePeriods',
      'unavailablePeriod',
      'unavailablePeriod.isHidden = false',
    );
  }
  queryBuilder.leftJoinAndSelect('unavailablePeriod.contract', 'unavailablePeriodContract');

  if (isDispatcher) {
    if (searchType !== 'Contracted') {
      if (pricelistIds?.length) {
        queryBuilder.leftJoinAndSelect(
          'vehicle.pricelists',
          'pricelists',
          'pricelists.id IN (:...pricelistIds)',
          { pricelistIds },
        );
      }
      if (operatorCompanyIds?.length) {
        queryBuilder.andWhere('company.id IN (:...operatorCompanyIds)', { operatorCompanyIds });
      }
    } else {
      // For contracted vehicles, use an inner join to enforce that there is an approved contract.
      queryBuilder.innerJoinAndSelect(
        'vehicle.contracts',
        'contract',
        'contract.status = :approvedStatus',
        { approvedStatus: ContractedStatus.APPROVED },
      );

      if (operatorCompanyIds?.length) {
        queryBuilder.andWhere('contract.operatorCompanyId IN (:...operatorCompanyIds)', {
          operatorCompanyIds,
        });
      }
      if (companyId) {
        queryBuilder.andWhere('contract.dispatcherCompanyId = :companyId', {
          companyId,
        });
      }
      if (dateFrom && dateTo) {
        queryBuilder.andWhere(
          '(contract.startDate BETWEEN :dateFromContract AND :dateToContract OR ' +
            'contract.endDate BETWEEN :dateFromContract AND :dateToContract OR ' +
            '(contract.startDate <= :dateFromContract AND contract.endDate >= :dateToContract))',
          {
            dateFromContract: new Date(dateFrom).toISOString(),
            dateToContract: new Date(dateTo).toISOString(),
          },
        );
      } else if (dateFrom) {
        queryBuilder.andWhere(
          'contract.startDate <= :dateFromContract AND contract.endDate >= :dateFromContract',
          { dateFromContract: new Date(dateFrom).toISOString() },
        );
      } else if (dateTo) {
        queryBuilder.andWhere(
          'contract.startDate <= :dateToContract AND contract.endDate >= :dateToContract',
          { dateToContract: new Date(dateTo).toISOString() },
        );
      }

      queryBuilder.leftJoinAndSelect('contract.pricelist', 'pricelist');
    }
  } else {
    queryBuilder
      .leftJoinAndSelect('vehicle.pricelists', 'allPricelists')
      .leftJoinAndSelect('allPricelists.container', 'allContainer')
      .leftJoinAndSelect(
        'vehicle.pricelists',
        'pricelists',
        'pricelists.containerId = allContainer.id AND allContainer.companyId = :companyId AND allContainer.isDefault = true',
        { companyId },
      )
      .leftJoinAndSelect('pricelists.container', 'container');

    queryBuilder.andWhere('company.id = :companyId', { companyId });
  }

  if (type) {
    queryBuilder.andWhere('vehicle.type = :type', { type });
  }
  if (boomSize) {
    queryBuilder.andWhere('vehicle.boomSize = :boomSize', { boomSize });
  }
  if (siteAddress) {
    queryBuilder.andWhere('reservations.siteAddress ILIKE :siteAddress', {
      siteAddress: `%${siteAddress}%`,
    });
  }
  if (operatorName) {
    queryBuilder.andWhere(
      "(operator.firstName ILIKE :operatorName OR operator.lastName ILIKE :operatorName OR CONCAT(operator.firstName, ' ', operator.lastName) ILIKE :operatorName)",
      { operatorName: `%${operatorName}%` },
    );
  }
  if (clientName) {
    queryBuilder.andWhere(
      '(("reservations"."clientDetails")::jsonb->>\'name\' ILIKE :clientName OR ("reservations"."clientDetails")::jsonb->>\'lastName\' ILIKE :clientName OR ("reservations"."clientDetails")::jsonb->>\'companyName\' ILIKE :clientName OR CONCAT(("reservations"."clientDetails")::jsonb->>\'name\', \' \', ("reservations"."clientDetails")::jsonb->>\'lastName\') ILIKE :clientName)',
      { clientName: `%${clientName}%` },
    );
  }

  queryBuilder.orderBy('vehicle.boomSize', 'ASC');

  const [vehicles, totalCount] = await queryBuilder.getManyAndCount();

  const result: VehicleReservationsResponse[] = vehicles.map(vehicle => ({
    id: vehicle.id,
    vehicleUniqueId: vehicle.uniqueIdentificationNumber,
    vehicleCompanyId: vehicle.manager.companyId,
    operatorName: `${vehicle.operator?.firstName} ${vehicle.operator?.lastName}`,
    operatorId: vehicle.operator?.id,
    operatorPhoneNumber: vehicle.operator?.phoneNumber || '',
    operatorStatus: vehicle.operator?.status?.toString() || '',
    managerId: vehicle.manager?.id,
    companyName: vehicle.manager?.company?.name || '',
    companyEmail: vehicle.manager?.company?.contactEmail || '',
    companyPhoneNumber: vehicle.manager?.company?.phoneNumber || '',
    boomSize: vehicle.boomSize,
    type: vehicle.type,
    imageUrl: vehicle.imageUrl,
    unavailablePeriods: vehicle.unavailablePeriods?.map(period => ({
      id: period.id,
      from: period.from ? formatDate(new Date(period.from)) : 'N/A',
      to: period.to ? formatDate(new Date(period.to)) : 'N/A',
      operatorId: period.operatorId ?? null,
      contract: period.contract,
    })),
    hasAssignedPricelist:
      searchType === 'Contracted'
        ? // For contracted searches, check if at least one contract has an attached pricelist.
          vehicle.contracts?.some(contract => !!contract.pricelist)
        : vehicle.pricelists?.length > 0,
    reservations: vehicle.reservations
      ?.filter(reservation => reservation.job?.status !== JobStatus.CANCELLED)
      .map(reservation =>
        VehicleMapper.mapReservationToVehicleReservationResponse(
          reservation,
          dispatcherId,
          companyId,
        ),
      ),
    company: vehicle.manager?.company?.settings
      ? {
          settings: {
            holidays: vehicle.manager.company.settings.holidays?.map(holiday => ({
              id: holiday.id,
              date: formatDate(new Date(holiday.date)),
              reason: holiday.reason,
            })),
          },
        }
      : undefined,
  }));

  return [result, totalCount];
};
