import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNumber, IsOptional, IsBoolean, IsString, IsEnum, IsNotEmpty } from 'class-validator';
import {
  Cleaning,
  JobStatus,
  ParkingOn,
  TerrainStability,
  JobType,
} from 'src/common/constants/job.enum';
import { processBoolean } from 'src/common/helpers/processBooleans';
import { processNumber } from 'src/common/helpers/processNumbers';

export class CreateJobDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  amountOfConcrete?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  flowRate?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  balance?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  barbotine?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  flexiblePipeLength80Mm?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  flexiblePipeLength90Mm?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  frontOutriggersSpan?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  rearOutriggersSpan?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  frontSideOpening?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  rearSideOpening?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  rigidPipeLength100Mm?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  rigidPipeLength120Mm?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  extraCementBag?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  units?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  presenceOfPowerLines?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  voltage?: number;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  parkingPermitAcquired: boolean;

  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  pipeStartingFromBAC: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  comments?: string;

  @ApiProperty({ enum: TerrainStability, enumName: 'TerrainStability', required: false })
  @IsEnum(TerrainStability)
  @IsOptional()
  terrainStability?: TerrainStability;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  tonnageRestriction?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  authorizedWeight?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  heightRestriction?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  heightLimit?: number;

  @ApiProperty({ enum: ParkingOn, enumName: 'ParkingOn', required: false })
  @IsEnum(ParkingOn)
  @IsOptional()
  parkingOn?: ParkingOn | null;

  @ApiProperty({ enum: JobStatus, enumName: 'JobStatus' })
  @IsNotEmpty()
  @IsEnum(JobStatus)
  status: JobStatus;

  @ApiProperty({ enum: Cleaning, enumName: 'Cleaning', required: false })
  @IsEnum(Cleaning)
  @IsOptional()
  cleaning?: Cleaning;

  @ApiProperty({ enum: JobType, enumName: 'JobType', required: false })
  @IsEnum(JobType)
  @IsOptional()
  jobType?: JobType;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  supplyOfTheChemicalSlushie?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  ciaw?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  reasonForDelay?: string;
}
