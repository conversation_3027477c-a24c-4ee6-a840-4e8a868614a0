import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1732005100470 implements MigrationInterface {
  name = 'Migrations1732005100470';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "electricalRiskPath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "accessCompliancePath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingCompliancePath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "terrainStabilityPath"`);
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP COLUMN "localAdministrationAuthorizationPath"`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "trafficPlanPath"`);
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "parkingPermitAcquiredPath"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "parkingPermitAcquiredPath" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ADD "trafficPlanPath" character varying`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD "localAdministrationAuthorizationPath" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "terrainStabilityPath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingCompliancePath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "accessCompliancePath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "electricalRiskPath" character varying`);
  }
}
