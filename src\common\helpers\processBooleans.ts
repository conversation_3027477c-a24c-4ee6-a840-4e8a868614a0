export const processBoolean = (value: string): boolean => {
  if (typeof value !== 'string') {
    return value;
  }
  const transformedValue = value.trim().toLowerCase();

  if (typeof transformedValue === 'boolean') {
    return transformedValue;
  }

  if (
    transformedValue === 'true' ||
    transformedValue === 'yes' ||
    transformedValue === 'y' ||
    transformedValue === '1' ||
    transformedValue === 'active'
  ) {
    return true;
  }
  if (
    transformedValue === 'false' ||
    transformedValue === 'no' ||
    transformedValue === 'n' ||
    transformedValue === '0' ||
    transformedValue === 'inactive' ||
    transformedValue === 'never been active'
  )
    return false;
};
