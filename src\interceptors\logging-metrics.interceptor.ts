import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { InjectMetric } from '@willsoto/nestjs-prometheus';
import { Counter, Histogram } from 'prom-client';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingAndMetricsInterceptor implements NestInterceptor {
  private readonly excludedPaths = ['/metrics', '/health'];

  constructor(
    @InjectMetric('http_requests_total') private counter: Counter<string>,
    @InjectMetric('http_request_duration_milliseconds')
    private responseTimeHistogram: Histogram<string>,
    @InjectMetric('http_request_size_bytes') private requestSizeHistogram: Histogram<string>,
    @InjectMetric('http_response_size_bytes') private responseSizeHistogram: Histogram<string>,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (context.getType() === 'http') {
      const request = context.switchToHttp().getRequest();
      const { url } = request;
      if (!this.excludedPaths.includes(url)) {
        return this.logAndMeasureHttpCall(context, next);
      }
    }
    return next.handle();
  }

  private logAndMeasureHttpCall(context: ExecutionContext, next: CallHandler) {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const { method, path: url } = request;

    const start = process.hrtime();

    return next.handle().pipe(
      tap(() => {
        const statusCode = response.statusCode.toString();
        const responseTime = process.hrtime(start);
        const durationMs = (responseTime[0] * 1e3 + responseTime[1] / 1e6).toFixed(3);
        const contentLength = response.get('content-length');

        this.responseTimeHistogram.observe(
          { method, route: url, statusCode: statusCode },
          parseFloat(durationMs),
        );
        this.requestSizeHistogram.observe(
          { method, route: url },
          parseFloat(request.headers['content-length'] || '0'),
        );
        this.responseSizeHistogram.observe(
          { method, route: url },
          parseFloat(contentLength || '0'),
        );

        this.counter
          .labels({
            statusGroup: `${statusCode[0]}XX`,
            status: `${statusCode}`,
            method: `${method}`,
          })
          .inc();
      }),
    );
  }
}
