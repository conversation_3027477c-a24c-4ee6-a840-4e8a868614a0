import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QUEUES } from './constants';

@Module({
  imports: [
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        connection: {
          host: config.get('REDIS_HOST', 'localhost'),
          port: config.get('REDIS_PORT', 6379),
          retryStrategy: (times: number) => {
            return times < 2 ? 500 : 60000;
          },
        },
      }),
    }),
    BullModule.registerQueue(
      { name: QUEUES.RESERVATION_INVOICE },
      { name: QUEUES.RESERVATION_BULK_INVOICE },
      { name: QUEUES.RESERVATION_CANCEL },
      { name: QUEUES.UNAVAILABLE_PERIOD_STATUS_UPDATE },
    ),
  ],
  exports: [BullModule],
})
export class BullmqModule {}
