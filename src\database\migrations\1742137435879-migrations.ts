import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1742137435879 implements MigrationInterface {
  name = 'Migrations1742137435879';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_02ab9e053fe898666845b620eb8"`,
    );
    await queryRunner.query(
      `CREATE TABLE "unavailable_period_vehicles" ("unavailablePeriodId" integer NOT NULL, "vehicleId" integer NOT NULL, CONSTRAINT "PK_c32675f6a36814ee8d18573441c" PRIMARY KEY ("unavailablePeriodId", "vehicleId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0147ecd23bf4e24a72272d2cdb" ON "unavailable_period_vehicles" ("unavailablePeriodId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e5adaa570c4eca584b24f5cdca" ON "unavailable_period_vehicles" ("vehicleId") `,
    );
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "vehicleId"`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "title"`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "comment"`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "operatorId" integer`);
    await queryRunner.query(
      `ALTER TYPE "public"."user_status_enum" RENAME TO "user_status_enum_old"`,
    );
    await queryRunner.query(`CREATE TYPE "public"."user_status_enum" AS ENUM('1', '2', '3', '4')`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "status" TYPE "public"."user_status_enum" USING "status"::"text"::"public"."user_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "status" SET DEFAULT '2'`);
    await queryRunner.query(`DROP TYPE "public"."user_status_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."company_status_enum" RENAME TO "company_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."company_status_enum" AS ENUM('1', '2', '3', '4')`,
    );
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "status" TYPE "public"."company_status_enum" USING "status"::"text"::"public"."company_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "status" SET DEFAULT '1'`);
    await queryRunner.query(`DROP TYPE "public"."company_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_ee63a6f789e0971745a3c55f65b" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period_vehicles" ADD CONSTRAINT "FK_0147ecd23bf4e24a72272d2cdbe" FOREIGN KEY ("unavailablePeriodId") REFERENCES "unavailable_period"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period_vehicles" ADD CONSTRAINT "FK_e5adaa570c4eca584b24f5cdca0" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unavailable_period_vehicles" DROP CONSTRAINT "FK_e5adaa570c4eca584b24f5cdca0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period_vehicles" DROP CONSTRAINT "FK_0147ecd23bf4e24a72272d2cdbe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_ee63a6f789e0971745a3c55f65b"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."company_status_enum_old" AS ENUM('1', '2', '3')`,
    );
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "status" TYPE "public"."company_status_enum_old" USING "status"::"text"::"public"."company_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "status" SET DEFAULT '1'`);
    await queryRunner.query(`DROP TYPE "public"."company_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."company_status_enum_old" RENAME TO "company_status_enum"`,
    );
    await queryRunner.query(`CREATE TYPE "public"."user_status_enum_old" AS ENUM('1', '2', '3')`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "status" TYPE "public"."user_status_enum_old" USING "status"::"text"::"public"."user_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "status" SET DEFAULT '2'`);
    await queryRunner.query(`DROP TYPE "public"."user_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."user_status_enum_old" RENAME TO "user_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "operatorId"`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "comment" character varying`);
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD "title" character varying NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "vehicleId" integer`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e5adaa570c4eca584b24f5cdca"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0147ecd23bf4e24a72272d2cdb"`);
    await queryRunner.query(`DROP TABLE "unavailable_period_vehicles"`);
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_02ab9e053fe898666845b620eb8" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
