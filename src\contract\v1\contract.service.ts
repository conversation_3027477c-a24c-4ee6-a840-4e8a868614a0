import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Contract } from '../entities/contract.entity';
import { CreateContractDto } from './dto/create-contract.dto';
import { ContractedStatus } from 'src/common/constants/user.enum';
import { ContractCategoryTypes, GetContractDto } from './dto/get-contract.dto';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { getContractCountQuery, getContractQuery } from '../queries/contractQuery';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { ContractComment } from '../entities/contract-comment.entity';
import {
  Expression,
  QueryOperator,
  SortDirections,
  addExpressions,
  ConditionType,
  getWhereQuery,
  getQuerySort,
  getQueryLimit,
} from 'src/libs/helpers/CeQuery';
import { UpdateContractDto } from './dto/update-contract.dto';
import { SuspensionPeriod } from '../entities/suspension-period.entity';
import { CreateVehicleUnavailablePeriodDto } from 'src/unavailable-period/v1/dto/create-vehicle-unavailable-period.dto';
import {
  GetUnavailablePeriodsDto,
  UnavailablePeriodCategoryTypes,
} from 'src/unavailable-period/v1/dto/get-unavailable-period.dto';
import { UnavailablePeriodService } from 'src/unavailable-period/v1/unavailable-period.service';
import { VehicleService } from 'src/vehicle/v1/vehicle.service';
import { UnavailablePeriod } from 'src/unavailable-period/entities/unavailable-period.entity';
import { PricelistService } from 'src/pricelist/v1/pricelist.service';
import { ReservationService } from 'src/reservation/v1/reservation.service';
import { ReservationCategoryTypes } from 'src/reservation/v1/dto/get-reservation.dto';
import { CreateReservationDto } from 'src/reservation/v1/dto/create-reservation.dto';
import { ClientDetails, ReservationType } from 'src/reservation/entities/reservation.entity';
import { JobStatus } from 'src/common/constants/job.enum';
import { parseTime } from 'src/reservation/helpers/work-hours.helper';

@Injectable()
export class ContractService {
  private readonly logger = new Logger(ContractService.name);
  constructor(
    @InjectRepository(Contract)
    private readonly contractRepository: Repository<Contract>,
    @InjectRepository(ContractComment)
    private commentRepository: Repository<ContractComment>,
    @InjectRepository(SuspensionPeriod)
    private suspensionPeriodRepository: Repository<SuspensionPeriod>,
    @Inject(forwardRef(() => VehicleService))
    private vehicleService: VehicleService,
    @Inject(forwardRef(() => UnavailablePeriodService))
    private unavailablePeriodService: UnavailablePeriodService,
    @Inject(forwardRef(() => PricelistService))
    private pricelistService: PricelistService,
    @Inject(forwardRef(() => ReservationService))
    private reservationService: ReservationService,
    private dataSource: DataSource,
  ) {}

  async findMany(
    body: GetContractDto,
    fetchingUser: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<Contract>> {
    this.logger.log({ method: 'findMany', body, fetchingUser });
    const { limit, offset, sortModel = [], relations = [] } = body;
    const companyId = fetchingUser?.companyId;
    const roleId = fetchingUser?.roleId;
    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    if ([Role.DISPATCHER_MANAGER, Role.DISPATCHER].includes(roleId)) {
      expressionsToAdd.push({
        category: ContractCategoryTypes.dispatcherCompanyId,
        operator: '=' as QueryOperator,
        value: companyId,
        conditionType: 'AND',
      });
    } else {
      expressionsToAdd.push({
        category: ContractCategoryTypes.operatorCompanyId,
        operator: '=' as QueryOperator,
        value: companyId,
        conditionType: 'AND',
      });
    }

    if (!sortModel.length) {
      sortModel.push({ field: ContractCategoryTypes.id, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);
    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const contractSQL = getContractQuery(whereQuery.query, querySort, queryLimit, relations);

    try {
      let totalCount = 0;

      const contracts = await queryRunner.manager.query(contractSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getContractCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }
      this.logger.log({
        method: 'findMany',
        query: body,
        contracts: contracts.length,
      });
      return new EntityWithCountDto<Contract>(Contract, contracts, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser: SerializedUser): Promise<Contract> {
    this.logger.log({ method: 'findOneById', id });

    const body: GetContractDto = {
      expressions: [
        {
          category: ContractCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
      ],
      limit: 1,
      offset: 0,
      sortModel: [],
      relations: ['pricelist', 'vehicle', 'comments'],
    };
    const result = await this.findMany(body, fetchingUser, false);

    const [contract] = result.data;

    if (!contract) {
      this.logger.error({ method: 'findOneById', id, error: 'Contract was not found' });
      throw new HttpException('Contract was not found!', HttpStatus.NOT_FOUND);
    }
    this.logger.log({ method: 'findOneById', id, contractId: contract.id });
    return contract;
  }

  async requestContract(
    createContractDto: CreateContractDto,
    creatingUser: SerializedUser,
  ): Promise<Contract> {
    const userId = creatingUser.sub;
    const companyId = creatingUser.companyId;
    const { pricelist: pricelistDto, ...contractDto } = createContractDto;
    this.logger.log({
      method: 'requestContract',
      contractDto,
      creatingUser: userId,
    });

    const isVehicleAvailable = await this.vehicleService.checkVehicleAvailability({
      id: createContractDto.vehicleId,
      dateFrom: createContractDto.startDate,
      dateTo: createContractDto.endDate,
      currentReservationId: null,
    });

    if (!isVehicleAvailable) {
      throw new BadRequestException('Vehicle is unavailable for contracting on the given dates!');
    }

    if (pricelistDto) {
      const createdPricelist = await this.pricelistService.create(
        {
          isContract: true,
          ...pricelistDto,
        },
        creatingUser.sub,
      );
      contractDto.pricelistId = createdPricelist.id;
    }

    const newContract = this.contractRepository.create({
      ...contractDto,
      created_by: userId,
      operatorCompanyId: companyId,
    });

    const savedContract = await this.contractRepository.save(newContract);
    await this.createVehicleUnavailablePeriod(
      savedContract,
      savedContract.vehicleId,
      savedContract.id,
      creatingUser,
    );
    this.logger.log({ method: 'requestContract', success: true, contractId: savedContract.id });
    return savedContract;
  }

  async update(
    contractId: number,
    updateContractDto: UpdateContractDto,
    updatingUser: SerializedUser,
  ): Promise<Contract> {
    const userId = updatingUser.sub;
    const { vehicleId, ...options } = updateContractDto;
    this.logger.log({
      method: 'update',
      updateContractDto,
      updatingUser: userId,
      contractId,
    });

    const existingContract = await this.contractRepository.findOneBy({ id: contractId });

    if (!existingContract) {
      throw new BadRequestException(`Contract with ID ${contractId} not found`);
    }

    if (vehicleId && existingContract.vehicleId != vehicleId) {
      // replace vehicle, add/remove unavailble period
      const vehicle = await this.vehicleService.findOne(vehicleId);
      // removes by existingContract options
      await this.removeVehicleUnavailablePeriod(
        existingContract,
        existingContract.vehicleId,
        updatingUser,
      );
      // uses updated values in options
      await this.createVehicleUnavailablePeriod(
        options,
        existingContract.vehicleId,
        contractId,
        updatingUser,
      );
      existingContract.vehicleId = vehicle.id;
    }

    const updatedContract = {
      ...existingContract,
      updated_by: userId,
      updated_at: new Date(),
    };

    const savedContract = await this.contractRepository.save(updatedContract);

    this.logger.log({ method: 'update', success: true, contractId });
    return savedContract;
  }

  async acceptContract(
    id: number,
    content?: string,
    creatingUser?: SerializedUser,
  ): Promise<Contract> {
    this.logger.log({ method: 'accept', id });
    const contract = await this.findOneById(id, creatingUser);
    if (content?.length) {
      await this.addComment(id, content, creatingUser, contract);
    }
    return await this.contractRepository.save({
      id: contract.id,
      status: ContractedStatus.APPROVED,
    });
  }

  async rejectContract(
    id: number,
    content?: string,
    creatingUser?: SerializedUser,
  ): Promise<Contract> {
    this.logger.log({ method: 'reject', id });
    const contract = await this.findOneById(id, creatingUser);
    if (content?.length) {
      await this.addComment(id, content, creatingUser, contract);
    }

    if (contract.status == ContractedStatus.APPROVED) {
      await this.processReservationCancellation(
        contract,
        new Date(new Date().getTime() + 60 * 1000), // 1 minute offset
        contract.endDate,
        creatingUser,
      );
    }
    await this.removeVehicleUnavailablePeriod(contract, contract.vehicleId, creatingUser);
    return await this.contractRepository.save({
      id: contract.id,
      status: ContractedStatus.DECLINED,
    });
  }

  async addComment(
    contractId: number,
    content: string,
    creatingUser: SerializedUser,
    _contract?: Contract,
  ): Promise<ContractComment> {
    this.logger.log({ method: 'addComment', contractId });
    const contract = _contract ?? (await this.findOneById(contractId, creatingUser));
    const comment = this.commentRepository.create({
      content,
      contract,
      created_by: creatingUser.sub,
    });
    const savedComment = await this.commentRepository.save(comment);
    this.logger.log({ method: 'addComment', success: true });
    return savedComment;
  }

  // Helper function: cancellation fee logic for suspension periods.
  async processReservationCancellation(
    contract: Contract,
    suspensionStart: Date,
    suspensionEnd: Date,
    creatingUser: SerializedUser,
  ): Promise<void> {
    const pricelist = contract.pricelist;
    if (!pricelist) {
      throw new Error('Pricelist was not provided!');
    }

    const now = new Date();

    // First, check if we are within a valid cancellation window
    if (now >= suspensionStart) {
      // Cancellations cannot be processed once the suspension has started.
      throw new BadRequestException('Cancellations must be applied before the suspension start.');
    }

    // Ensure no conflicting reservations for the suspension period
    const reservations = await this.reservationService.findMany({
      expressions: [
        {
          category: ReservationCategoryTypes.vehicleId,
          operator: '=',
          value: contract.vehicleId,
          conditionType: 'AND',
        },
        {
          category: ReservationCategoryTypes.jobStatus,
          operator: '!=',
          value: JobStatus.CANCELLED,
          conditionType: 'AND',
        },
        {
          category: ReservationCategoryTypes.dateFrom,
          operator: '<',
          value: new Date(suspensionEnd).toISOString(),
          conditionType: 'AND',
        },
        {
          category: ReservationCategoryTypes.dateTo,
          operator: '>',
          value: new Date(suspensionStart).toISOString(),
          conditionType: 'AND',
        },
      ],
    });
    if (reservations && reservations.totalCount > 0) {
      throw new BadRequestException('Please cancel reservations before suspending the contract.');
    }

    let shouldCreateCancellation: boolean = false;
    let cancellationPeriod = pricelist.contractCancellationPeriod;
    if (cancellationPeriod == null) {
      cancellationPeriod = pricelist.contractLateCancellationPeriod;
    }

    if (cancellationPeriod != null) {
      const cancellationTimeMinutes = parseTime(cancellationPeriod);

      const cancellationThreshold = new Date(suspensionStart);
      cancellationThreshold.setDate(cancellationThreshold.getDate() - 2);
      cancellationThreshold.setHours(
        Math.floor(cancellationTimeMinutes / 60),
        cancellationTimeMinutes % 60,
        0,
        0,
      );

      shouldCreateCancellation = now >= cancellationThreshold;
    }

    if (shouldCreateCancellation) {
      const timestampPart = Date.now().toString().slice(-6);
      const randomPart = Math.floor(Math.random() * 1000000)
        .toString()
        .padStart(6, '0');
      const generatedOrderNumber = `${timestampPart}${randomPart}`;
      const createReservationDto: CreateReservationDto = {
        dateFrom: suspensionStart,
        dateTo: suspensionEnd,
        vehicleId: contract.vehicleId,
        siteAddress: '',
        city: '',
        plz: 0,
        location: { type: '', coordinates: [] },
        clientDetails: new ClientDetails(),
        trafficPlanKey: '',
        parkingPermitAcquiredKey: '',
        pricelistId: pricelist.id,
        managerId: contract.created_by,
        operatorId: contract.created_by,
        dispatcherId: creatingUser.sub,
        orderNumber: generatedOrderNumber,
        reservationType: ReservationType.RESERVATION,
        job: {
          presenceOfPowerLines: false,
          pipeStartingFromBAC: false,
          status: JobStatus.CANCELLED,
          parkingPermitAcquired: false,
        },
      };
      await this.reservationService.createCancelled(createReservationDto, pricelist, creatingUser);
    }
  }

  async addSuspensionPeriod(
    contractId: number,
    suspensionStart: Date,
    suspensionEnd: Date,
    creatingUser: SerializedUser,
    _contract?: Contract,
  ): Promise<SuspensionPeriod> {
    this.logger.log({ method: 'addSuspensionPeriod', contractId });
    const contract = _contract ?? (await this.findOneById(contractId, creatingUser));

    // Validate the suspension period against the contract criteria.
    this.validateSuspensionAgainstContract(contract, suspensionStart, suspensionEnd);

    // Create and persist the suspension period.
    const suspensionPeriod = this.suspensionPeriodRepository.create({
      contract,
      suspensionStart,
      suspensionEnd,
      created_by: creatingUser.sub,
    });

    await this.processReservationCancellation(
      contract,
      suspensionStart,
      suspensionEnd,
      creatingUser,
    );

    const savedSuspensionPeriod = await this.suspensionPeriodRepository.save(suspensionPeriod);

    await this.updateVehicleUnavailablePeriod(
      contract,
      contract.vehicleId,
      suspensionStart,
      suspensionEnd,
      creatingUser,
    );

    this.logger.log({ method: 'addSuspensionPeriod', success: true });
    return savedSuspensionPeriod;
  }

  async removeOneById(id: number, removingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removeOneById', id });
    const contract = await this.findOneById(id, removingUser);
    await this.removeVehicleUnavailablePeriod(contract, contract.vehicleId, removingUser);
    await this.contractRepository.delete(contract.id);
  }

  async removeSuspensionPeriodById(
    suspensionPeriodId: number,
    creatingUser: SerializedUser,
  ): Promise<SuspensionPeriod> {
    this.logger.log({ method: 'removeSuspensionPeriodById', suspensionPeriodId });

    // Retrieve the suspension period by its ID.
    const suspensionPeriod = await this.suspensionPeriodRepository.findOne({
      where: { id: suspensionPeriodId },
    });
    if (!suspensionPeriod) {
      throw new Error(`Suspension period with ID ${suspensionPeriodId} not found.`);
    }

    // Retrieve the associated contract using the suspensionPeriod's contractId.
    const contract = await this.findOneById(suspensionPeriod.contractId, creatingUser);
    if (!contract) {
      throw new Error(`Contract with ID ${suspensionPeriod.contractId} not found.`);
    }

    // Remove the suspension period record.
    const removedSuspension = await this.suspensionPeriodRepository.remove(suspensionPeriod);

    await this.mergeUnavailablePeriodAfterSuspensionRemoval(
      contract,
      suspensionPeriod.suspensionStart,
      suspensionPeriod.suspensionEnd,
      creatingUser,
    );

    this.logger.log({ method: 'removeSuspensionPeriodById', success: true });
    return removedSuspension;
  }

  async createVehicleUnavailablePeriod(
    options: Partial<CreateContractDto>,
    vehicleId: number,
    contractId: number,
    creatingUser: SerializedUser,
  ): Promise<UnavailablePeriod> {
    const creatingUserId = creatingUser.sub;
    this.logger.log({
      method: 'createVehicleUnavailablePeriod',
      options,
      vehicleId,
      creatingUserId,
    });

    this.validateContractDates(options);

    const payload: CreateVehicleUnavailablePeriodDto = {
      from: options.startDate,
      to: options.endDate,
      vehicleIds: [vehicleId],
      contractId,
    };

    const unavailablePeriod = await this.unavailablePeriodService.createVehiclesUnavailablePeriod(
      payload,
      creatingUser,
    );

    this.logger.log({
      method: 'createVehicleUnavailablePeriod',
      message: 'Vehicle unavailable period created successfully',
      vehicleId,
      unavailablePeriodId: unavailablePeriod.id,
    });
    return unavailablePeriod;
  }

  async updateVehicleUnavailablePeriod(
    options: Partial<Contract>,
    vehicleId: number,
    suspensionStart: Date = new Date(),
    suspensionEnd: Date = new Date(),
    updatingUser: SerializedUser,
  ): Promise<UnavailablePeriod[]> {
    const suspensionStartDate = new Date(suspensionStart);
    const suspensionEndDate = new Date(suspensionEnd);
    this.logger.log({
      method: 'updateVehicleUnavailablePeriod',
      options,
      vehicleId,
      suspensionStart: suspensionStartDate,
      suspensionEnd: suspensionEndDate,
      updatingUser: updatingUser.sub,
    });

    this.validateContractDates(options);
    this.validateSuspensionAgainstContract(options, suspensionStart, suspensionEnd);

    // Retrieve all overlapping periods.
    const existingPeriods = await this.getExistingUnavailablePeriods(options);

    // Prepare an array to collect updated periods.
    const updatedPeriods: UnavailablePeriod[] = [];

    if (existingPeriods && existingPeriods.length) {
      // Handle each unavailable period.
      for (const existingPeriod of existingPeriods) {
        const updatedPeriod = await this.handleExistingPeriod(
          existingPeriod,
          options,
          vehicleId,
          suspensionStartDate,
          suspensionEndDate,
          updatingUser,
        );
        updatedPeriods.push(updatedPeriod);
      }
    } else {
      // No existing period found: create a new unavailable period.
      const newPeriod = await this.handleNoExistingPeriod(
        options,
        vehicleId,
        suspensionEndDate,
        updatingUser,
      );
      updatedPeriods.push(newPeriod);
    }

    this.logger.log({ method: 'updateVehicleUnavailablePeriod', success: true });
    return updatedPeriods;
  }

  private async getExistingUnavailablePeriods(
    options: Partial<Contract>,
  ): Promise<UnavailablePeriod[]> {
    // Finds the periods overlapping the contract’s timeframe.
    // We assume that an unavailable period overlaps the contract period if its
    // "to" is on/after the contract start and its "from" is on/before the contract end.
    const body: GetUnavailablePeriodsDto = {
      expressions: [
        {
          category: UnavailablePeriodCategoryTypes.to,
          operator: '>=', // The period ends after (or at) contract start.
          value: new Date(options.startDate).toISOString(),
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.from,
          operator: '<=', // The period starts before (or at) contract end.
          value: new Date(options.endDate).toISOString(),
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.vehicleIds,
          operator: '=' as QueryOperator,
          value: options.vehicleId,
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.contractId,
          operator: '=' as QueryOperator,
          value: options.id,
          conditionType: 'AND',
        },
      ],
      relations: ['vehicles'],
      sortModel: [],
    };

    this.logger.log({
      method: 'getExistingUnavailablePeriods',
      query: {
        start: new Date(options.startDate).toISOString(),
        end: new Date(options.endDate).toISOString(),
      },
    });

    const result = await this.unavailablePeriodService.findMany(body, null, false);
    return result.data || [];
  }

  private async handleExistingPeriod(
    existingPeriod: UnavailablePeriod,
    options: Partial<CreateContractDto>,
    vehicleId: number,
    suspensionStart: Date,
    suspensionEnd: Date,
    updatingUser: SerializedUser,
  ): Promise<UnavailablePeriod> {
    this.logger.log({
      method: 'handleExistingPeriod',
      unavailablePeriodId: existingPeriod.id,
      vehicleId,
    });

    const periodStart = existingPeriod.from;
    const periodEnd = existingPeriod.to;
    const contractEnd = options.endDate;

    // Check if this period fully covers the suspension window.
    const coversSuspension = periodStart <= suspensionStart && periodEnd >= suspensionEnd;

    if (!coversSuspension) {
      this.logger.log({
        method: 'handleExistingPeriod',
        unavailablePeriodId: existingPeriod.id,
        message:
          'Period does not fully cover the suspension window, so no update was performed on this period.',
      });
      return existingPeriod;
    }

    // Check if the new from and to dates are equal or the from date is later than the to date.
    // Here we assume options.startDate is the new "from" and suspensionStart as the new "to".
    const newFromDate = options.startDate;
    const newToDate = suspensionStart;
    if (newFromDate >= newToDate) {
      await this.unavailablePeriodService.removeOneById(existingPeriod.id);
      return existingPeriod;
    }

    // Update the found period so that its "to" date becomes the suspensionStart.
    const updatedPayload: CreateVehicleUnavailablePeriodDto = {
      from: options.startDate,
      to: suspensionStart,
      vehicleIds: [vehicleId],
    };

    const updatedPeriod = await this.unavailablePeriodService.updateVehiclesUnavailablePeriod(
      existingPeriod.id,
      updatedPayload,
      updatingUser,
    );
    this.logger.log({
      method: 'handleExistingPeriod',
      unavailablePeriodId: updatedPeriod.id,
      message: `Updated period end to suspensionStart: ${new Date(suspensionStart).toISOString()}`,
    });

    // If the contract end extends beyond the suspensionEnd, create a new period for the remainder.
    if (contractEnd > suspensionEnd) {
      const result = await this.unavailablePeriodService.findMany(
        {
          expressions: [
            {
              category: UnavailablePeriodCategoryTypes.vehicleIds,
              operator: '=' as QueryOperator,
              value: vehicleId,
              conditionType: 'AND',
            },
            {
              category: UnavailablePeriodCategoryTypes.from,
              operator: '<' as QueryOperator,
              value: new Date(options.endDate).toISOString(),
              conditionType: 'AND',
            },
            {
              category: UnavailablePeriodCategoryTypes.to,
              operator: '>' as QueryOperator,
              value: new Date(suspensionEnd).toISOString(),
              conditionType: 'AND',
            },
          ],
          relations: ['vehicles'],
          limit: 1,
          offset: 0,
          sortModel: [],
        },
        updatingUser,
        false,
      );

      const [existingUnavailablePeriod] = result.data;

      if (existingUnavailablePeriod) {
        this.logger.log({
          method: 'handleExistingPeriod',
          message: `Remainder period from ${new Date(suspensionEnd).toISOString()} to ${new Date(
            options.endDate,
          ).toISOString()} already exists. No action taken.`,
          vehicleId,
        });
      } else {
        const newPayload: CreateVehicleUnavailablePeriodDto = {
          from: suspensionEnd,
          to: options.endDate,
          vehicleIds: [vehicleId],
        };
        const newPeriod = await this.unavailablePeriodService.createVehiclesUnavailablePeriod(
          newPayload,
          updatingUser,
        );
        this.logger.log({
          method: 'handleExistingPeriod',
          message: `Created new period from ${new Date(suspensionEnd).toISOString()} to ${new Date(
            options.endDate,
          ).toISOString()}`,
          vehicleId,
          newUnavailablePeriodId: newPeriod.id,
        });
      }
    }
    return updatedPeriod;
  }

  private async handleNoExistingPeriod(
    options: Partial<CreateContractDto>,
    vehicleId: number,
    suspensionEnd: Date,
    updatingUser: SerializedUser,
  ): Promise<UnavailablePeriod> {
    this.logger.log({
      method: 'handleNoExistingPeriod',
      message: 'No existing period was found. Creating a new unavailable period.',
      vehicleId,
    });
    const startDate = suspensionEnd;
    const endDate = options.endDate;
    if (startDate.toISOString() >= endDate.toISOString()) return null;
    const newPayload: CreateVehicleUnavailablePeriodDto = {
      from: startDate,
      to: endDate,
      vehicleIds: [vehicleId],
    };
    const updatedPeriod = await this.unavailablePeriodService.createVehiclesUnavailablePeriod(
      newPayload,
      updatingUser,
    );
    this.logger.log({
      method: 'handleNoExistingPeriod',
      message: `Created new period from ${startDate.toISOString()} to ${endDate.toISOString()}`,
      vehicleId,
      newUnavailablePeriodId: updatedPeriod.id,
    });

    return updatedPeriod;
  }

  private validateContractDates(options: Partial<CreateContractDto>): void {
    if (!options.startDate || !options.endDate) {
      this.logger.error({
        method: 'validateContractDates',
        error: 'Missing contract startDate or endDate in options.',
        options,
      });
      throw new Error('Contract startDate and endDate must be provided.');
    }

    if (options.startDate >= options.endDate) {
      this.logger.error({
        method: 'validateContractDates',
        error: 'Invalid contract dates: startDate must be before endDate.',
        contractStart: options.startDate.toISOString(),
        contractEnd: options.endDate.toISOString(),
      });
      throw new Error('Contract startDate must be before contract endDate.');
    }
  }

  private validateSuspensionAgainstContract(
    options: Partial<CreateContractDto>,
    suspensionStart: Date,
    suspensionEnd: Date,
  ): void {
    if (suspensionStart >= suspensionEnd) {
      this.logger.error({
        method: 'validateSuspensionAgainstContract',
        error: 'Invalid suspension dates: suspensionStart must be before suspensionEnd.',
        suspensionStart: new Date(suspensionStart).toISOString(),
        suspensionEnd: new Date(suspensionEnd).toISOString(),
      });
      throw new Error('Suspension start must be before suspension end.');
    }

    if (options.startDate > suspensionStart) {
      this.logger.error({
        method: 'validateSuspensionAgainstContract',
        error: 'Suspension start is before contract startDate.',
        contractStart: new Date(options.startDate).toISOString(),
        suspensionStart: new Date(suspensionStart).toISOString(),
      });
      throw new Error('Suspension cannot start before the contract startDate.');
    }

    if (suspensionEnd > options.endDate) {
      this.logger.error({
        method: 'validateSuspensionAgainstContract',
        error: 'Suspension end is after contract endDate.',
        suspensionEnd: new Date(suspensionEnd).toISOString(),
        contractEnd: new Date(options.endDate).toISOString(),
      });
      throw new Error('Suspension cannot end after the contract endDate.');
    }
  }

  async removeVehicleUnavailablePeriod(
    options: Partial<Contract>,
    vehicleId: number,
    removingUser: SerializedUser,
  ) {
    this.logger.log({
      method: 'removeVehicleUnavailablePeriod',
      options,
      vehicleId,
      removingUser: removingUser.sub,
    });
    this.validateContractDates(options);

    const existingPeriods = await this.getExistingUnavailablePeriods(options);

    if (existingPeriods && existingPeriods.length) {
      for (const period of existingPeriods) {
        await this.unavailablePeriodService.removeOneById(period.id, removingUser);
        this.logger.log({
          method: 'removeVehicleUnavailablePeriod',
          removedPeriodId: period.id,
        });
      }
      this.logger.log({ method: 'removeVehicleUnavailablePeriod', success: true });
    } else {
      this.logger.log({
        method: 'removeVehicleUnavailablePeriod',
        message: 'No unavailable periods found for removal.',
      });
    }

    return { status: 'ok' };
  }

  private async mergeUnavailablePeriodAfterSuspensionRemoval(
    contract: Contract,
    suspensionStart: Date,
    suspensionEnd: Date,
    updatingUser: SerializedUser,
  ): Promise<void> {
    const vehicleId = contract.vehicleId;

    // Determine the new 'to' date.
    // If the suspension end equals the contract end, then we want the period to extend right to contract.end.
    // Otherwise, we want it to extend exactly to the suspensionEnd.
    const newEndDate =
      new Date(suspensionEnd).getTime() === new Date(contract.endDate).getTime()
        ? contract.endDate
        : suspensionEnd;

    // Find the unavailable period that ends exactly at the suspension start.
    const resultPeriod = await this.unavailablePeriodService.findMany({
      expressions: [
        {
          category: UnavailablePeriodCategoryTypes.vehicleIds,
          operator: '=' as QueryOperator,
          value: vehicleId,
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.to,
          operator: '=',
          value: new Date(suspensionStart).toISOString(),
          conditionType: 'AND',
        },
      ],
      relations: ['vehicles'],
      sortModel: [],
      limit: 1,
      offset: 0,
    });
    const [beforePeriod] = resultPeriod.data;
    if (beforePeriod) {
      // Extend the before period's "to" date to the newEndDate.
      await this.unavailablePeriodService.updateVehiclesUnavailablePeriod(
        beforePeriod.id,
        {
          from: beforePeriod.from,
          to: newEndDate,
          vehicleIds: [vehicleId],
        },
        updatingUser,
      );
      this.logger.log({
        method: 'mergeUnavailablePeriodAfterSuspensionRemoval',
        message: `Updated period ${beforePeriod.id}: extended to ${newEndDate}`,
        vehicleId,
      });
    } else {
      await this.unavailablePeriodService.createVehiclesUnavailablePeriod(
        {
          from: contract.startDate,
          to: newEndDate,
          vehicleIds: [vehicleId],
        },
        updatingUser,
      );
      this.logger.log({
        method: 'mergeUnavailablePeriodAfterSuspensionRemoval',
        message: `Created new period from ${contract.startDate} to ${newEndDate}`,
        vehicleId,
      });
    }
  }
}
