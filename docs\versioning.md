### API Versioning Documentation for NestJS

#### Overview
This document details the implementation of API versioning in a NestJS application, the use of `VERSION_NEUTRAL`, and provides examples of versioned routes.

#### File Structure
The project is structured to support versioning with separate folders for each API version:

```
src/
├── users/
│   ├── v1/
│   │   ├── controllers/
│   │   │   ├── users.controller.ts
│   │   ├── dto/
│   │   │   ├── create-user.dto.ts
│   │   ├── services/
│   │   │   ├── users.service.ts
│   ├── v2/
│   │   ├── controllers/
│   │   │   ├── users.controller.ts
│   │   ├── dto/
│   │   │   ├── create-user.dto.ts
│   │   ├── services/
│   │   │   ├── users.service.ts
│   ├── entities/
│   │   ├── user.entity.ts
│   ├── users.module.ts
```

#### Enabling Versioning
Versioning is enabled in the main application module using the `VersioningType.URI` strategy.

```typescript
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { VersioningType, VERSION_NEUTRAL } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: VERSION_NEUTRAL,
  });
  await app.listen(3000);
}
bootstrap();
```

#### Understanding `VERSION_NEUTRAL`
`VERSION_NEUTRAL` is used to indicate that an endpoint is accessible regardless of the version specified. This is useful for endpoints that should be available across all versions of the API.

### Controller Setup
Two versions of the `UsersController` have been created, along with a neutral version that is accessible regardless of the version specified.

**Neutral Version (Default)**
```typescript
import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { UsersService } from '../services/users.service';

@ApiTags('Users')
@Controller({ path: 'users', version: VERSION_NEUTRAL })
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  findAll() {
    return this.usersService.findAll();
  }
}
```

**Version 2**
```typescript
import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { UsersService } from '../services/users.service';

@ApiTags('Users')
@Controller({ path: 'users', version: '2' })
export class UsersControllerV2 {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  findAll() {
    return this.usersService.findAllV2();
  }
}
```

### Module Configuration
The `UsersModule` is configured to include both versions of the controller and service.

```typescript
import { Module } from '@nestjs/common';
import { UsersController as UsersControllerV1 } from './v1/controllers/users.controller';
import { UsersService as UsersServiceV1 } from './v1/services/users.service';
import { UsersController as UsersControllerV2 } from './v2/controllers/users.controller';
import { UsersService as UsersServiceV2 } from './v2/services/users.service';

@Module({
  controllers: [UsersControllerV1, UsersControllerV2, UsersController],
  providers: [UsersServiceV1, UsersServiceV2, UsersService],
  exports: [UsersServiceV1, UsersServiceV2, UsersService],
})
export class UsersModule {}
```

### Example Routes
- **Neutral Version**: Accessible without version or with any version prefix.
  - `http://localhost:3001/users`
  - `http://localhost:3001/v1/users`
  - `http://localhost:3001/v2/users`
- **Version 2**: Specifically for version 2.
  - `http://localhost:3001/v2/users`

### Summary of Steps
1. **File Structure**: Organize codebase by version folders within each module.
2. **Versioned Controllers**: Define controllers with different versions using `@Controller` decorator and `version` property.
3. **Document APIs**: If needed use `@ApiTags` to differentiate API versions in your documentation.
