import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1751922501466 implements MigrationInterface {
  name = 'Migrations1751922501466';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "frontSideOpening" double precision`);
    await queryRunner.query(`ALTER TABLE "job" ADD "rearSideOpening" double precision`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "rearSideOpening"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "frontSideOpening"`);
  }
}
