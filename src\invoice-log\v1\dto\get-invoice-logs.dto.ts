import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { InvoiceStatus } from 'src/invoice-log/enums/invoice-status.enum';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { InvoiceType } from 'src/invoice-log/enums/invoice-type.enum';

export class GetInvoiceLogsDto extends CommonQueryParams {
  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
    description: 'Array of operator Company IDs',
  })
  @IsOptional()
  readonly operatorCompanyIds?: number[] | null;

  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
    description: 'Array of dispatcher Company IDs',
  })
  @IsOptional()
  readonly dispatcherCompanyIds?: number[] | null;

  @ApiProperty({ enum: InvoiceStatus, description: 'Status' })
  @IsEnum(InvoiceStatus)
  @IsOptional()
  status: InvoiceStatus;

  @ApiProperty({ enum: InvoiceType, description: 'Type' })
  @IsEnum(InvoiceType)
  type: InvoiceType;
}
