import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708635677409 implements MigrationInterface {
  name = 'Migrations1708635677409';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "start" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "start" DROP DEFAULT`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "start" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "start" SET NOT NULL`);
  }
}
