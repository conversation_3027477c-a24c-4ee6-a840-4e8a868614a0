import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1745524650654 implements MigrationInterface {
  name = 'Migrations1745524650654';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "contractCancellationPeriod"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "contractCancellationPeriod" interval`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "contractLateCancellationPeriod"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "contractLateCancellationPeriod" interval`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "contractLateCancellationPeriod"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "contractLateCancellationPeriod" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "contractCancellationPeriod"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "contractCancellationPeriod" double precision`,
    );
  }
}
