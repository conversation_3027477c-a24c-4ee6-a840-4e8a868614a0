import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { CreatePricelistDto } from './create-pricelist.dto';

export class CreateManyPricelistsDto {
  @ApiProperty({
    type: CreatePricelistDto,
    isArray: true,
    required: true,
    description: 'An array of CreatePricelistDto objects',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePricelistDto)
  pricelistsDto: (CreatePricelistDto & { id?: number })[];

  @ApiProperty()
  @IsOptional()
  containerId?: number;
}
