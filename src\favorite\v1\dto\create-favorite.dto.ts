import { IsNotEmpty, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { processNumber } from 'src/common/helpers/processNumbers';
import { Transform } from 'class-transformer';

export class CreateFavoriteDto {
  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({ type: Number })
  @Transform(({ value }) => processNumber(value))
  dispatcherCompanyId: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({ type: Number })
  @Transform(({ value }) => processNumber(value))
  operatorCompanyId: number;
}
