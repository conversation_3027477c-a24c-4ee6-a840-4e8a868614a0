import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { JobEvent } from '../entities/job-event.entity';
import { CreateJobEventsDto } from './dto/create-job-events.dto';
import { UpdateJobEventsDto } from './dto/update-job-events.dto';
import { JobService } from 'src/job/v1/job.service';
import { GetJobEventsDto, JobEventCategoryTypes } from './dto/get-job-events.dto';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import {
  addExpressions,
  ConditionType,
  Expression,
  getQueryLimit,
  getQuerySort,
  getWhereQuery,
  SortDirections,
} from 'src/libs/helpers/CeQuery';
import { getJobEventCountQuery, getJobEventsQuery } from '../queries/get-job-event-query';

@Injectable()
export class JobEventsService {
  private readonly logger = new Logger(JobEventsService.name);

  constructor(
    @InjectRepository(JobEvent)
    private jobEventsRepository: Repository<JobEvent>,
    @Inject(forwardRef(() => JobService))
    private jobService: JobService,
    private dataSource: DataSource,
  ) {}

  async create(createJobEventDto: CreateJobEventsDto, userId: number): Promise<JobEvent> {
    this.logger.log({
      method: 'createJobEvent',
      userId,
    });

    const jobEvent = this.jobEventsRepository.create({
      ...createJobEventDto,
      created_by: userId,
    });

    if (createJobEventDto.jobId) {
      const job = await this.jobService.findOneById(createJobEventDto.jobId);
      if (job) {
        jobEvent.job = job;
      } else {
        jobEvent.job = null;
      }
    }

    const savedJobEvent = await this.jobEventsRepository.save(jobEvent);

    this.logger.log({
      method: 'createJobEvent',
      jobEventId: savedJobEvent.id,
      userId,
    });

    return savedJobEvent;
  }

  async findMany(
    body: GetJobEventsDto,
    shouldReturnTotalCount = true,
  ): Promise<EntityWithCountDto<JobEvent>> {
    this.logger.log({ method: 'findMany', body });
    const { limit, offset, sortModel = [], relations = [], expressions = [] } = body;
    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    if (!sortModel.length) {
      sortModel.push({ field: JobEventCategoryTypes.jobEventId, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const jobEventsSQL = getJobEventsQuery(whereQuery.query, querySort, queryLimit, relations);

    try {
      let totalCount = 0;

      const jobEvents = await queryRunner.manager.query(jobEventsSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getJobEventCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({
        method: 'findMany',
        query: body,
        jobEventsLength: jobEvents.length,
      });
      return new EntityWithCountDto<JobEvent>(JobEvent, jobEvents, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number): Promise<JobEvent> {
    this.logger.log({ method: 'findOneById', id });

    const { data: jobEvents } = await this.findMany(
      {
        expressions: [{ category: JobEventCategoryTypes.jobEventId, operator: '=', value: id }],
        limit: 1,
        sortModel: [],
      },
      false,
    );

    if (jobEvents.length == 0) {
      throw new NotFoundException('Job Event was not found.');
    }

    this.logger.log({
      method: 'findOneById',
      id,
    });
    return jobEvents[0];
  }

  async update(
    id: number,
    updateJobEventDto: UpdateJobEventsDto,
    updatingUserId?: number,
  ): Promise<JobEvent> {
    this.logger.log({ method: 'updateJobEvent', jobEventId: id, updatingUser: updatingUserId });

    const jobEvent = await this.findOneById(id);
    if (!jobEvent) {
      throw new NotFoundException(`JobEvent with ID ${id} not found`);
    }

    Object.assign(jobEvent, updateJobEventDto);

    if (updateJobEventDto.jobId) {
      const job = await this.jobService.findOneById(updateJobEventDto.jobId);
      if (job) {
        jobEvent.job = job;
      } else {
        jobEvent.job = null;
      }
    }
    const savedJobEvent = await this.jobEventsRepository.save(jobEvent);

    this.logger.log({
      method: 'updateJobEvent',
      jobEventId: savedJobEvent.id,
      updatingUser: updatingUserId,
    });
    return savedJobEvent;
  }

  async remove(id: number): Promise<void> {
    this.logger.log({ method: 'removeJobEvent', id });

    const result = await this.jobEventsRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`JobEvent with ID ${id} not found`);
    }
  }
}
