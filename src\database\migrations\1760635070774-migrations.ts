import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1760635070774 implements MigrationInterface {
    name = 'Migrations1760635070774'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "reason" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "reason"`);
    }

}
