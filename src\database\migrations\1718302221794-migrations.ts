import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1718302221794 implements MigrationInterface {
  name = 'Migrations1718302221794';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job_location" DROP COLUMN "latitude"`);
    await queryRunner.query(`ALTER TABLE "job_location" DROP COLUMN "longitude"`);
    await queryRunner.query(`ALTER TABLE "job_location" ADD "coords" json NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job_location" DROP COLUMN "coords"`);
    await queryRunner.query(`ALTER TABLE "job_location" ADD "longitude" double precision NOT NULL`);
    await queryRunner.query(`ALTER TABLE "job_location" ADD "latitude" double precision NOT NULL`);
  }
}
