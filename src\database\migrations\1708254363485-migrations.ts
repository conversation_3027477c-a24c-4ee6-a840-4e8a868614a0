import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708254363485 implements MigrationInterface {
  name = 'Migrations1708254363485';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" ADD "operatorId" integer`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888"`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "operatorId"`);
  }
}
