import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { QueryFailedError } from 'typeorm';
import { ValidationError } from 'class-validator';
import { PostgresError } from '../constants/pg-errors.enum';
import { InjectMetric } from '@willsoto/nestjs-prometheus';
import { Counter } from 'prom-client';

@Catch()
export class AllExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionFilter.name);

  constructor(@InjectMetric('http_errors_total') private readonly errorCounter: Counter<string>) {}

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let errorDetails: string | undefined;

    if (exception instanceof QueryFailedError) {
      // Handle PostgreSQL errors
      ({ status, message, errorDetails } = this.handleDatabaseError(exception));
    } else if (exception instanceof HttpException) {
      const responseError = exception.getResponse();
      if (this.isValidationError(responseError)) {
        // Handle validation errors (class-validator)
        status = HttpStatus.BAD_REQUEST;
        message = this.handleValidationErrors(responseError as ValidationError[]);
      } else {
        // Handle HTTP exceptions
        status = exception.getStatus();
        message =
          typeof responseError === 'string'
            ? responseError
            : (responseError as any).message || message;
      }
    } else {
      // Handle unknown/unexpected internal errors
      errorDetails = (exception as Error)?.message || 'Unknown error';
    }
    const statusCode = status.toString();
    // Log error to Prometheus
    this.errorCounter
      .labels({
        statusGroup: `${statusCode[0]}XX`,
        status: statusCode,
        method: request.method,
      })
      .inc();

    // Log error details
    this.logError(request, exception, errorDetails || message);

    // Send response to client
    response.status(status).json({
      statusCode,
      message,
      method: request.method,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }

  private handleDatabaseError(exception: QueryFailedError) {
    const pgError = exception as any;
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Database error occurred';
    let errorDetails: string | undefined;

    switch (pgError.code) {
      case PostgresError.UNIQUE_VIOLATION:
        status = HttpStatus.BAD_REQUEST;
        message = 'Duplicate entry, this value already exists.';
        break;
      case PostgresError.FOREIGN_KEY_VIOLATION:
        status = HttpStatus.BAD_REQUEST;
        message = 'Cannot proceed due to reference constraint violation.';
        break;
      case PostgresError.NOT_NULL_VIOLATION:
        status = HttpStatus.BAD_REQUEST;
        message = 'Required field is missing.';
        break;
      case PostgresError.CHECK_VIOLATION:
        status = HttpStatus.BAD_REQUEST;
        message = 'Check constraint failed.';
        break;
      default:
        message = 'Unknown database error occurred.';
        errorDetails = pgError.message;
        break;
    }

    return { status, message, errorDetails };
  }

  private handleValidationErrors(errors: ValidationError[]): string {
    return (
      errors
        .map(error => {
          const constraints = Object.values(error.constraints || {});
          return constraints.join(', ');
        })
        .join('; ') || 'Validation failed'
    );
  }

  private isValidationError(response: any): boolean {
    return Array.isArray(response) && response[0] instanceof ValidationError;
  }

  private logError(request: Request, exception: unknown, errorDetails: string) {
    this.logger.error(
      `Error in ${request.method} ${request.url}: ${errorDetails}`,
      exception instanceof Error ? exception.stack : undefined,
    );
  }
}
