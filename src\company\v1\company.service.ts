import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Company } from '../entities/company.entity';
import { DataSource, Repository } from 'typeorm';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { CreateCompanyDto } from './dto/create-company.dto';
import { CompanyCategoryTypes, GetCompanyDto } from './dto/get-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { getCompaniesQuery, getCompanyCountQuery } from '../queries/get-company-query';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { CompanySettingsService } from 'src/company-settings/v1/company-settings.service';
import {
  addExpressions,
  ConditionType,
  Expression,
  getQueryLimit,
  getQuerySort,
  getWhereQuery,
  QueryOperator,
  SortDirections,
} from 'src/libs/helpers/CeQuery';
import { CompanyCategory } from 'src/common/constants/company.enum';
import { Role } from 'src/common/constants/roles.enum';
import { VehicleService } from 'src/vehicle/v1/vehicle.service';

@Injectable()
export class CompanyService {
  private readonly logger = new Logger(CompanyService.name);
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @Inject(forwardRef(() => CompanySettingsService))
    private readonly companySettingsService: CompanySettingsService,
    private dataSource: DataSource,
    @Inject(forwardRef(() => VehicleService))
    private vehicleService: VehicleService,
  ) {}
  async create(createCompanyDto: CreateCompanyDto, creatingUserId: number): Promise<Company> {
    this.logger.log({ method: 'createCompany' });

    const emailCompany = await this.companyRepository.findOne({
      where: { contactEmail: createCompanyDto.contactEmail },
    });

    if (emailCompany) {
      throw new HttpException('Email already in use!', HttpStatus.UNPROCESSABLE_ENTITY);
    }

    const company = this.companyRepository.create({
      ...createCompanyDto,
      created_by: creatingUserId,
    });

    await this.companyRepository.save(company);

    // Use CompanySettingsService to create default settings
    await this.companySettingsService.createDefaultSettings(company.id);

    this.logger.log({
      method: 'createCompany',
      creatingUserId,
      companyId: company.id,
    });

    return company;
  }

  async findMany(
    body: GetCompanyDto,
    fetchingUser: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<Company>> {
    this.logger.log({ method: 'findMany' });
    const { limit, offset, sortModel = [], relations = [] } = body;
    const userRoleId = fetchingUser?.roleId;
    const companyId = fetchingUser?.companyId;
    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    switch (userRoleId) {
      case Role.OPERATOR_MANAGER:
        expressionsToAdd.push(
          ...[
            {
              category: CompanyCategoryTypes.category,
              operator: '=' as QueryOperator,
              value: CompanyCategory.DISPATCHER,
              conditionType: 'AND',
            },
            {
              category: CompanyCategoryTypes.id,
              operator: '=' as QueryOperator,
              value: companyId,
              conditionType: 'OR',
            },
          ],
        );
        break;
      case Role.DISPATCHER_MANAGER:
        expressionsToAdd.push(
          ...[
            {
              category: CompanyCategoryTypes.category,
              operator: '=' as QueryOperator,
              value: CompanyCategory.OPERATOR,
              conditionType: 'AND',
            },
            {
              category: CompanyCategoryTypes.id,
              operator: '=' as QueryOperator,
              value: companyId,
              conditionType: 'OR',
            },
          ],
        );
        break;
      default:
        break;
    }

    if (!sortModel.length) {
      sortModel.push({ field: CompanyCategoryTypes.id, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const companiesSQL = getCompaniesQuery(whereQuery.query, querySort, queryLimit, relations);

    try {
      let totalCount = 0;

      const companies = await queryRunner.manager.query(companiesSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getCompanyCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({
        method: 'findMany',
        query: body,
        companiesLength: companies.length,
      });
      return new EntityWithCountDto<Company>(Company, companies, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser?: SerializedUser): Promise<Company> {
    this.logger.log({ method: 'findOneById', id });

    const body: GetCompanyDto = {
      expressions: [
        {
          category: CompanyCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
      ],
      relations: ['users', 'settings'],
      limit: 1,
      offset: 0,
      sortModel: [],
    };

    const result = await this.findMany(body, fetchingUser, false);

    const company = result.data[0];

    if (!company) {
      this.logger.error({ method: 'findOneById', id, error: 'Company not found' });
      throw new HttpException('Company was not found!', HttpStatus.NOT_FOUND);
    }

    this.logger.log({ method: 'findOneById', id, companyId: company.id });
    return company;
  }

  async findOneByUserId(userId: number): Promise<Company> {
    this.logger.log(`findOneByUserId id: ${userId}`);

    const result = await this.findMany(
      {
        limit: 1,
        offset: 0,
        expressions: [
          {
            category: '"users"."id"',
            operator: '=',
            value: userId,
            conditionType: 'AND',
          },
        ],
        sortModel: [],
        relations: ['users', 'settings', 'workScheduleSettings', 'cancellation'],
      },
      null,
      false,
    );

    const [company] = result.data;

    if (!company) {
      throw new NotFoundException(`No Company found for userId = ${userId}`);
    }

    return company;
  }

  async findOneDispatcherWithPartners(
    dispatcherCompanyId: number,
    operatorCompanyId?: number,
  ): Promise<Company> {
    this.logger.log({
      method: 'findOneDispatcherWithPartners',
      dispatcherCompanyId,
      operatorCompanyId,
    });

    const body: GetCompanyDto = {
      expressions: [
        {
          category: CompanyCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: dispatcherCompanyId,
          conditionType: 'AND',
        },
      ],
      relations: ['dispatcherPartners', 'operatorPartners'],
      limit: 1,
      offset: 0,
      sortModel: [],
    };

    // Add conditionally filter by operatorCompanyId if it's provided
    if (operatorCompanyId) {
      body.expressions.push({
        category: CompanyCategoryTypes.dispatcherPartnersOperatorCompanyId,
        operator: '=' as QueryOperator,
        value: operatorCompanyId,
        conditionType: 'AND',
      });
    }

    const result = await this.findMany(body, null, false);

    const [company] = result.data;

    if (!company) {
      throw new HttpException('Company was not found!', HttpStatus.NOT_FOUND);
    }

    this.logger.log({ method: 'findOneDispatcherWithPartners', success: true });
    return company;
  }

  async findOneWithPartners(id: number): Promise<Company> {
    this.logger.log({ method: 'findOneWithPartners', id });

    const result = await this.findMany(
      {
        limit: 1,
        offset: 0,
        expressions: [
          {
            category: CompanyCategoryTypes.id,
            operator: '=',
            value: id,
            conditionType: 'AND',
          },
        ],
        sortModel: [],
        relations: ['operatorPartners', 'dispatcherPartners'],
      },
      null,
      false,
    );

    const [company] = result.data;

    if (!company) {
      throw new HttpException('Company was not found!', HttpStatus.NOT_FOUND);
    }

    return company;
  }

  async updateOneById(
    id: number,
    updateCompanyDto: UpdateCompanyDto,
    updatingUserId?: number,
  ): Promise<Company> {
    this.logger.log({
      method: 'updateOneById',
      companyId: id,
      updatingCompany: updatingUserId,
    });

    const existingCompany = await this.findOneById(id);

    if (updateCompanyDto.contactEmail) {
      const emailCompany = await this.findMany(
        {
          limit: 1,
          offset: 0,
          expressions: [
            {
              category: CompanyCategoryTypes.contactEmail,
              operator: '=',
              value: updateCompanyDto.contactEmail,
              conditionType: 'AND',
            },
            {
              category: CompanyCategoryTypes.id,
              operator: '!=',
              value: id,
              conditionType: 'AND',
            },
          ],
          sortModel: [],
        },
        null,
        false,
      );

      if (emailCompany.data.length) {
        throw new HttpException('Email already in use!', HttpStatus.UNPROCESSABLE_ENTITY);
      }
    }

    const company = await this.companyRepository.save({
      id: id,
      ...updateCompanyDto,
    });

    if (updateCompanyDto.name != null && updateCompanyDto.name !== existingCompany.name) {
      await this.vehicleService.refreshVehicleUniqueIdsForCompany(company.id);
    }

    this.logger.log({
      method: 'updateOneById',
      id,
      companyId: company.id,
      updatingCompany: updatingUserId,
    });
    return company;
  }

  async removeOneById(id: number): Promise<void> {
    this.logger.log({ method: 'removeOneById', id });
    await this.companyRepository.delete(id);
  }
}
