import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1709594501196 implements MigrationInterface {
  name = 'Migrations1709594501196';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomSize"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomSize" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "licensePlateNumber"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "licensePlateNumber" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "weight"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "weight" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "height"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "height" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "length"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "length" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "width"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "width" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxVerticalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxVerticalReach" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxHorizontalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxHorizontalReach" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "endHoseLength"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "endHoseLength" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxFlowRate"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxFlowRate" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxConcretePressure"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxConcretePressure" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength80Mm"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength80Mm" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength90Mm"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength90Mm" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength100Mm"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength100Mm" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength120Mm"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength120Mm" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableRigidPipeLength"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "availableRigidPipeLength" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxDownwardReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxDownwardReach" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "numberOfBoomSections"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "numberOfBoomSections" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "minUnfoldingHeight"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "minUnfoldingHeight" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomRotation"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomRotation" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rubberEndHoseLength"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rubberEndHoseLength" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "frontOutriggerSpan"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "frontOutriggerSpan" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rearOutriggerSpan"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rearOutriggerSpan" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "frontPressureOnOutrigger"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "frontPressureOnOutrigger" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rearPressureOnOutrigger"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rearPressureOnOutrigger" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "frontSideOpening"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "frontSideOpening" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rearSideOpening"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rearSideOpening" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "invoicingPipesFrom"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "invoicingPipesFrom" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "pipeLengthForSecondTechnician"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "pipeLengthForSecondTechnician" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "uniqueIdentificationNumber"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "uniqueIdentificationNumber" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "completedJobs"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "completedJobs" double precision`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_849e0cc4d98a094f3b21a01578" ON "vehicle" ("licensePlateNumber") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f9119b941e097329b73c3ce10f" ON "vehicle" ("uniqueIdentificationNumber") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_f9119b941e097329b73c3ce10f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_849e0cc4d98a094f3b21a01578"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "completedJobs"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "completedJobs" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "uniqueIdentificationNumber"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "uniqueIdentificationNumber" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "pipeLengthForSecondTechnician"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "pipeLengthForSecondTechnician" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "invoicingPipesFrom"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "invoicingPipesFrom" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rearSideOpening"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rearSideOpening" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "frontSideOpening"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "frontSideOpening" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rearPressureOnOutrigger"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rearPressureOnOutrigger" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "frontPressureOnOutrigger"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "frontPressureOnOutrigger" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rearOutriggerSpan"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rearOutriggerSpan" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "frontOutriggerSpan"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "frontOutriggerSpan" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "rubberEndHoseLength"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "rubberEndHoseLength" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomRotation"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomRotation" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "minUnfoldingHeight"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "minUnfoldingHeight" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "numberOfBoomSections"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "numberOfBoomSections" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxDownwardReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxDownwardReach" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableRigidPipeLength"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "availableRigidPipeLength" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength120Mm"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength120Mm" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength100Mm"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength100Mm" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength90Mm"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength90Mm" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "availableFlexiblePipeLength80Mm"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "availableFlexiblePipeLength80Mm" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxConcretePressure"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxConcretePressure" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxFlowRate"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxFlowRate" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "endHoseLength"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "endHoseLength" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxHorizontalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxHorizontalReach" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxVerticalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxVerticalReach" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "width"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "width" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "length"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "length" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "height"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "height" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "weight"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "weight" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "licensePlateNumber"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "licensePlateNumber" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomSize"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomSize" integer`);
  }
}
