import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1709223387984 implements MigrationInterface {
  name = 'Migrations1709223387984';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "units" integer`);
    await queryRunner.query(`ALTER TABLE "job" ADD "voltage" integer`);
    await queryRunner.query(`ALTER TABLE "job" ADD "authorizedWeight" integer`);
    await queryRunner.query(`ALTER TABLE "job" ADD "heightLimit" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "extraCementBag"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "extraCementBag" boolean`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "presenceOfPowerLines"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "presenceOfPowerLines" boolean`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "tonnageRestriction"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "tonnageRestriction" boolean`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "heightRestriction"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "heightRestriction" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "heightRestriction"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "heightRestriction" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "tonnageRestriction"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "tonnageRestriction" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "presenceOfPowerLines"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "presenceOfPowerLines" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "extraCementBag"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "extraCementBag" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "heightLimit"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "authorizedWeight"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "voltage"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "units"`);
  }
}
