import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Partner } from '../entities/partner.entity';
import { CreatePartnerDto } from './dto/create-partner.dto';
import { PartnerStatus } from 'src/common/constants/user.enum';
import { GetPartnerDto, PartnerCategoryTypes } from './dto/get-partner.dto';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { getPartnerCountQuery, getPartnerQuery } from '../queries/partnerQuery';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import {
  addExpressions,
  ConditionType,
  Expression,
  getQueryLimit,
  getQuerySort,
  getWhereQuery,
  QueryOperator,
  SortDirections,
} from 'src/libs/helpers/CeQuery';
import { AssignContainerPricelistsDto } from './dto/assign-container-pricelists-partner.dto';
import { RemoveContainerPricelistsDto } from './dto/remove-container-pricelists-partner.dto';
import { ContainerPricelistsService } from 'src/pricelist/v1/container-pricelists.service';
import { CreateManyPartnersDto } from './dto/create-multiple-partners.dto';

@Injectable()
export class PartnerService {
  private readonly logger = new Logger(PartnerService.name);
  constructor(
    @InjectRepository(Partner)
    private readonly partnerRepository: Repository<Partner>,
    @Inject(forwardRef(() => ContainerPricelistsService))
    private readonly containerService: ContainerPricelistsService,
    private dataSource: DataSource,
  ) {}

  async findMany(
    body: GetPartnerDto,
    fetchingUser: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<Partner>> {
    this.logger.log({ method: 'findMany', fetchingUser });
    const {
      limit,
      offset,
      sortModel = [],
      relations = ['dispatcherCompany', 'operatorCompany', 'containerPricelists'],
    } = body;
    const companyId = fetchingUser?.companyId;
    const roleId = fetchingUser?.roleId;
    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    if ([Role.DISPATCHER_MANAGER, Role.DISPATCHER].includes(roleId)) {
      expressionsToAdd.push({
        category: PartnerCategoryTypes.partnerDispatcherCompanyId,
        operator: '=' as QueryOperator,
        value: companyId,
        conditionType: 'AND',
      });
    } else {
      expressionsToAdd.push({
        category: PartnerCategoryTypes.partnerOperatorCompanyId,
        operator: '=' as QueryOperator,
        value: companyId,
        conditionType: 'AND',
      });
    }

    if (!sortModel.length) {
      sortModel.push({ field: PartnerCategoryTypes.id, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);
    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const partnerSQL = getPartnerQuery(whereQuery.query, querySort, queryLimit, relations);

    try {
      let totalCount = 0;

      const partners = await queryRunner.manager.query(partnerSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getPartnerCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }
      this.logger.log({
        method: 'findMany',
        query: body,
        partners: partners.length,
      });
      return new EntityWithCountDto<Partner>(Partner, partners, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser: SerializedUser): Promise<Partner> {
    this.logger.log({ method: 'findOneById', id });

    const body: GetPartnerDto = {
      expressions: [
        {
          category: PartnerCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
      ],
      limit: 1,
      offset: 0,
      sortModel: [],
      relations: ['workSchedules', 'cancellation'],
    };

    const result = await this.findMany(body, fetchingUser, false);

    const [partner] = result.data;

    if (!partner) {
      this.logger.error({ method: 'findOneById', id, error: 'Partner was not found' });
      throw new HttpException('Partner was not found!', HttpStatus.NOT_FOUND);
    }
    this.logger.log({ method: 'findOneById', id, partnerId: partner.id });
    return partner;
  }

  async create(createPartnerDto: CreatePartnerDto, creatingUser: SerializedUser): Promise<Partner> {
    this.logger.log({
      method: 'create',
      createPartnerDto,
      creatingUser: creatingUser.sub,
    });
    const body: GetPartnerDto = {
      expressions: [
        {
          category: PartnerCategoryTypes.partnerOperatorCompanyId,
          operator: '=' as QueryOperator,
          value: createPartnerDto.operatorCompanyId,
          conditionType: 'AND',
        },
      ],
      limit: 1,
      offset: 0,
      sortModel: [],
      relations: [],
    };

    const result = await this.findMany(body, creatingUser, false);
    const [existingPartner] = result.data;

    if (existingPartner) {
      throw new BadRequestException(`Partner already exists for this operator company`);
    }

    const newPartner = this.partnerRepository.create(createPartnerDto);

    newPartner.dispatcherCompanyId = creatingUser.companyId;
    newPartner.created_by = creatingUser.sub;

    const savedPartner = await this.partnerRepository.save(newPartner);

    this.logger.log({ method: 'create', success: true });
    return savedPartner;
  }

  async createMany(
    createPartnerDto: CreateManyPartnersDto,
    creatingUser: SerializedUser,
  ): Promise<Partner[]> {
    const { operatorCompanyIds, ...otherData } = createPartnerDto;

    this.logger.log({
      method: 'createMany',
      operatorCompanyIds: operatorCompanyIds.length,
      creatingUser: creatingUser.sub,
    });

    const body: GetPartnerDto = {
      expressions: [
        {
          category: PartnerCategoryTypes.partnerOperatorCompanyId,
          operator: 'in' as QueryOperator,
          value: operatorCompanyIds,
          conditionType: 'AND',
        },
      ],
      limit: operatorCompanyIds.length,
      offset: 0,
      sortModel: [],
      relations: [],
    };

    const existingPartnersResult = await this.findMany(body, creatingUser, false);

    const existingIds = new Set(
      existingPartnersResult.data.map(partner => partner.operatorCompanyId),
    );

    // Filter out operatorCompanyIds that already have partners
    const newOperatorCompanyIds = operatorCompanyIds.filter(
      operatorCompanyId => !existingIds.has(operatorCompanyId),
    );

    // Create new partners for the remaining operatorCompanyIds
    const newPartners = newOperatorCompanyIds.map(operatorCompanyId =>
      this.partnerRepository.create({
        ...otherData,
        operatorCompanyId,
        dispatcherCompanyId: creatingUser.companyId,
        created_by: creatingUser.sub,
      }),
    );

    const savedPartners = await this.partnerRepository.save(newPartners);

    this.logger.log({
      method: 'createMany',
      createdCount: savedPartners.length,
      skippedCount: operatorCompanyIds.length - savedPartners.length,
    });

    return savedPartners;
  }

  async assignContainersById(
    id: number,
    body: AssignContainerPricelistsDto,
    skipContainerCheck: boolean = false,
  ): Promise<Partner> {
    this.logger.log({ method: 'assignContainersById', id, containerIds: body.containerIds });

    const partner = await this.partnerRepository.findOne({
      where: { id },
      relations: ['containerPricelists'],
    });

    if (!partner) {
      throw new HttpException('Partner was not found!', HttpStatus.NOT_FOUND);
    }

    let containers = [];
    if (skipContainerCheck) {
      containers = body.containerIds.map(containerId => ({ id: containerId }));
    } else {
      containers = await Promise.all(
        body.containerIds.map(containerId => this.containerService.findOneById(containerId)),
      );
    }

    const existingIds = new Set(partner.containerPricelists?.map(c => c.id) || []);
    const newContainers = containers.filter(container => !existingIds.has(container.id));

    partner.containerPricelists = [...(partner.containerPricelists || []), ...newContainers];

    return await this.partnerRepository.save(partner);
  }

  async removeContainersById(id: number, body: RemoveContainerPricelistsDto): Promise<Partner> {
    this.logger.log({ method: 'removeContainersById', id, containerIds: body.containerIds });

    await this.partnerRepository
      .createQueryBuilder()
      .relation(Partner, 'containerPricelists')
      .of(id)
      .remove(body.containerIds);

    return this.partnerRepository.findOne({
      where: { id },
      relations: ['containerPricelists'],
    });
  }

  async acceptOneById(id: number, updatingUser: SerializedUser): Promise<Partner> {
    this.logger.log({ method: 'acceptOneById', id });
    const partner = await this.findOneById(id, updatingUser);
    partner.status = PartnerStatus.ACTIVE;
    return await this.partnerRepository.save(partner);
  }

  async rejectOneById(id: number, updatingUser: SerializedUser): Promise<Partner> {
    this.logger.log({ method: 'rejectOneById', id });
    const partner = await this.findOneById(id, updatingUser);
    partner.status = PartnerStatus.INACTIVE;
    return this.partnerRepository.save(partner);
  }

  async removeOneById(id: number, deletingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'remove', id });
    const user = await this.findOneById(id, deletingUser);
    await this.partnerRepository.delete(user.id);
  }
}
