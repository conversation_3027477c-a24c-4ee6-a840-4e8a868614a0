import { Module, forwardRef } from '@nestjs/common';
import { JobEventsService } from './v1/job-events.service';
import { JobEventsController } from './v1/job-events.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobEvent } from './entities/job-event.entity';
import { JobModule } from 'src/job/job.module';
@Module({
  imports: [TypeOrmModule.forFeature([JobEvent]), forwardRef(() => JobModule)],
  providers: [JobEventsService],
  controllers: [JobEventsController],
  exports: [JobEventsService],
})
export class JobEventsModule {}
