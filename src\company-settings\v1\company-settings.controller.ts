import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { CompanySettingsService } from './company-settings.service';
import { CreateCompanySettingsDto } from './dto/create-company-settings.dto';
import { UpdateCompanySettingsDto } from './dto/update-company-settings.dto';

@ApiTags('Company-Settings')
@Controller({ path: 'company-settings', version: VERSION_NEUTRAL })
export class CompanySettingsController {
  constructor(private readonly companySettingsService: CompanySettingsService) {}

  @Post()
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
  ])
  async create(@Body() createCompanySettingsDto: CreateCompanySettingsDto, @Req() req) {
    return await this.companySettingsService.create(createCompanySettingsDto, req?.user);
  }

  @Get(':id')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR },
    { role: Role.OPERATOR_MANAGER },
  ])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.companySettingsService.findOneById(+id, req?.user);
  }

  @Patch(':id')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
  ])
  updateOneById(
    @Param('id') id: string,
    @Body() updateCompanySettingsDto: UpdateCompanySettingsDto,
    @Req() req,
  ) {
    return this.companySettingsService.updateOneById(+id, updateCompanySettingsDto, req?.user);
  }

  @Delete(':id')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
  ])
  removeOneById(@Param('id') id: string, @Req() req) {
    return this.companySettingsService.removeOneById(+id, req);
  }
}
