import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1740824312220 implements MigrationInterface {
  name = 'Migrations1740824312220';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" DROP CONSTRAINT "FK_cb009f4b6d93f0b9751c624490c"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_cb009f4b6d93f0b9751c624490"`);
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" RENAME COLUMN "partnerId" TO "isDefault"`,
    );
    await queryRunner.query(`ALTER TABLE "container_pricelists" DROP COLUMN "isDefault"`);
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" ADD "isDefault" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_df1ab3ffaffb7cb9403a5aaec6" ON "container_pricelists" ("companyId") WHERE "isDefault" = true`,
    );
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" ADD CONSTRAINT "FK_31ea38cbe9e249f385117e677b8" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" DROP CONSTRAINT "FK_31ea38cbe9e249f385117e677b8"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_df1ab3ffaffb7cb9403a5aaec6"`);
    await queryRunner.query(`ALTER TABLE "container_pricelists" DROP COLUMN "isDefault"`);
    await queryRunner.query(`ALTER TABLE "container_pricelists" ADD "isDefault" integer`);
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" RENAME COLUMN "isDefault" TO "partnerId"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cb009f4b6d93f0b9751c624490" ON "container_pricelists" ("partnerId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" ADD CONSTRAINT "FK_cb009f4b6d93f0b9751c624490c" FOREIGN KEY ("partnerId") REFERENCES "partner"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
