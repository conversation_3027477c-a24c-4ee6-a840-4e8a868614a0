import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1733473066959 implements MigrationInterface {
  name = 'Migrations1733473066959';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "contract_comment" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "content" text NOT NULL, "contractId" integer NOT NULL, CONSTRAINT "PK_d18caf4a8249d130ef52b7abe8d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_15bfcb8f8de8a202405bbf5ffa" ON "contract_comment" ("contractId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_1622f57e6fd5d12f7e36c1626c8"`,
    );
    await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "partnerId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "partner" ADD CONSTRAINT "FK_866bd06d0d18cf9f3ba62ee3d95" FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner" ADD CONSTRAINT "FK_95f1e55c9721f42df2a7443bbf2" FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract_comment" ADD CONSTRAINT "FK_15bfcb8f8de8a202405bbf5ffa8" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_1622f57e6fd5d12f7e36c1626c8" FOREIGN KEY ("partnerId") REFERENCES "partner"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_1622f57e6fd5d12f7e36c1626c8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract_comment" DROP CONSTRAINT "FK_15bfcb8f8de8a202405bbf5ffa8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner" DROP CONSTRAINT "FK_95f1e55c9721f42df2a7443bbf2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner" DROP CONSTRAINT "FK_866bd06d0d18cf9f3ba62ee3d95"`,
    );
    await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "partnerId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_1622f57e6fd5d12f7e36c1626c8" FOREIGN KEY ("partnerId") REFERENCES "partner"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_15bfcb8f8de8a202405bbf5ffa"`);
    await queryRunner.query(`DROP TABLE "contract_comment"`);
  }
}
