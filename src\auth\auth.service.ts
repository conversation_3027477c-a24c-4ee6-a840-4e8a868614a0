import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { UsersService } from 'src/users/v1/users.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { Response } from 'express';
import { instanceToPlain } from 'class-transformer';
import { Status } from 'src/common/constants/status.enum';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async signIn(email: string, pass: string, res: Response): Promise<void> {
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      throw new UnauthorizedException(
        'Invalid credentials: No user with the provided email address found.',
      );
    }

    if (user.status === Status.INACTIVE) {
      throw new UnauthorizedException(
        'Unable to authenticate. Please contact your support administrator!',
      );
    }

    if (user.lockoutEndTime && new Date() < user.lockoutEndTime) {
      const remainingTime = Math.ceil(
        (user.lockoutEndTime.getTime() - new Date().getTime()) / 60000,
      );
      throw new UnauthorizedException(
        `You are locked out of your account for ${remainingTime} minutes.`,
      );
    }

    if (!user.password) {
      throw new UnauthorizedException(
        'Account is not verified. Please create a new password by clicking forgot password!',
      );
    }

    const isMatch = await bcrypt.compare(pass, user.password);
    if (!isMatch) {
      user.remainingAttempts -= 1;

      if (user.remainingAttempts <= 0) {
        user.lockoutEndTime = new Date(new Date().getTime() + 15 * 60000); // Lock for 15 minutes
        const remainingTime = Math.ceil(
          (user.lockoutEndTime.getTime() - new Date().getTime()) / 60000,
        );
        user.remainingAttempts = 5; // Reset attempts for the next lockout period
        await this.usersService.updateOneById(user.id, {
          remainingAttempts: user.remainingAttempts,
          lockoutEndTime: user.lockoutEndTime,
        });
        throw new UnauthorizedException(
          `You are locked out of your account for ${remainingTime} minutes.`,
        );
      }

      await this.usersService.updateOneById(user.id, {
        remainingAttempts: user.remainingAttempts,
        lockoutEndTime: user.lockoutEndTime,
      });
      throw new UnauthorizedException(
        `Invalid credentials, ${user.remainingAttempts} attempts left! `,
      );
    }

    user.remainingAttempts = 5;
    user.lockoutEndTime = null;
    await this.usersService.updateOneById(user.id, {
      remainingAttempts: user.remainingAttempts,
      lockoutEndTime: user.lockoutEndTime,
    });

    const payload = {
      sub: user.id,
      email: user.email,
      roleId: user.role,
      companyId: user.companyId,
    };
    this.logger.log({ method: 'signIn', payload });
    const token = await this.jwtService.signAsync(payload);

    // Set the token as a cookie in the response
    res.cookie('access_token', token, {
      httpOnly: true,
      maxAge: this.configService.get('JWT_EXPIRES_SECONDS') * 1000,
    }); // Convert to milliseconds

    // You can also send the token in the response body if needed
    res.json({ access_token: token, user: instanceToPlain(user) });

    this.logger.log({ method: 'signIn', payload, user });
  }
}
