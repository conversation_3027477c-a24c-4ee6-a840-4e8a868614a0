name: Deployment To TST Environment

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

permissions:
  id-token: write
  contents: read
  actions: read

env:
  AWS_REGION: eu-central-1
  ECR_REPOSITORY: backend
  ECS_SERVICE: backend_service
  ECS_CLUSTER: concreteeasy-ecs-tst
  ECS_TASK_FAMILY: backend
  ECS_TASK_DEFINITION: task-definition.json
  CONTAINER_NAME: backend
  ENVIRONMENT: tst

jobs:
  slackNotificationPipelineStarted:
    name: Pipeline Started
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: BackendBot
          SLACK_TITLE: "Pipeline Execution Started"
          SLACK_MESSAGE: "Pipeline Execution in Backend Repository Started"

  lint:
    name: Lint
    if: github.event_name == 'pull_request' || github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Use Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Install Dependencies
        run: npm ci
      - name: Run Lint
        run: npm run lint

  audit:
    name: Audit
    if: github.event_name == 'pull_request' || github.event_name == 'push'
    needs: lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Use Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Run Audit
        run: echo "npm audit --omit=dev --audit-level=high"

  unitTest:
    name: Unit Test
    if: github.event_name == 'pull_request' || github.event_name == 'push'
    needs: audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Use Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: 18
      - name: Install Dependencies
        run: npm ci
      - name: Run Unit Test
        run: echo "npm run test:cov"
      - name: List Coverage Directory
        run: |
          ls -la
          find coverage -print | sed -e "s;[^/]*/;|____;g;s;____|; |;g"
      - name: Upload Test Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: unit_test_artifact
          retention-days: 1
          path: |
            coverage

  build:
    name: Build
    if: github.event_name == 'push'
    needs:
      - slackNotificationPipelineStarted
      - unitTest
    runs-on: ubuntu-latest
    outputs:
      image: ${{ steps.login-ecr.outputs.registry }}${{ steps.build-image.outputs.image }}
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Declare Commit Short Sha Variable
        shell: bash
        run: echo "commit_short_sha=$(git rev-parse --short ${{ github.sha }})" >> $GITHUB_ENV
      - name: Check Variables from previous step
        run: |
          echo "shortha: ${{ env.commit_short_sha }}"
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.MAIN_GITHUB_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build, tag, and push docker image to Amazon ECR
        id: build-image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: ${{ env.ECR_REPOSITORY }}
          IMAGE_TAG: ${{ env.ENVIRONMENT }}-${{ env.commit_short_sha }}
        run: |
          docker build -t $REGISTRY/$REPOSITORY:$IMAGE_TAG .
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy:
    name: Deployment
    if: github.event_name == 'push'
    needs: build
    runs-on: ubuntu-latest
    env:
      IMAGE: ${{needs.build.outputs.image}}
    steps:
      - name: Check Variable Value
        run: echo $IMAGE
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.STAGING_GITHUB_ROLE }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Download Task Definition
        run: |
          aws ecs describe-task-definition --task-definition ${{ env.ECS_TASK_FAMILY }} --query taskDefinition > ${{ env.ECS_TASK_DEFINITION }}
          echo $ECS_TASK_DEFINITION
          TEMP_DEFINITION=$( jq -c . $ECS_TASK_DEFINITION)
          echo $TEMP_DEFINITION | jq -c 'del(.taskDefinitionArn)' | jq -c 'del(.registeredBy)' | jq -c 'del(.registeredAt)' | jq -c 'del(.status)' | jq -c 'del(.revision)' | jq -c 'del(.requiresAttributes)' | jq -c 'del(.compatibilities)' | jq > $ECS_TASK_DEFINITION
          aws ecs describe-task-definition --include TAGS --task-definition ${{ env.ECS_TASK_FAMILY }} > tags.json
          TAGS=$(cat tags.json | jq -c .tags)
          echo $(jq '. += {"tags": '"$TAGS"'}' $ECS_TASK_DEFINITION) > $ECS_TASK_DEFINITION
          cat $ECS_TASK_DEFINITION
      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: ${{ env.ECS_TASK_DEFINITION }}
          container-name: ${{ env.CONTAINER_NAME }}
          image: ${{ env.IMAGE }}
      - name: Deploy Amazon ECS task definition
        id: ecs-deploy
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: ${{ env.ECS_SERVICE }}
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
      - name: Check if deployment was successful
        id: check-deployment
        run: |
          CURRENT_TASK_DEF_ARN=$(aws ecs describe-services --cluster ${{ env.ECS_CLUSTER }} --services ${{ env.ECS_SERVICE }} --query services[0].deployments[0].taskDefinition | jq -r ".")
          NEW_TASK_DEF_ARN=${{ steps.ecs-deploy.outputs.task-definition-arn }}
          echo "Current task arn: $CURRENT_TASK_DEF_ARN"
          echo "New task arn: $NEW_TASK_DEF_ARN"
          if [ "$CURRENT_TASK_DEF_ARN" != "$NEW_TASK_DEF_ARN" ]; then
            echo "Deployment failed."
            exit 1
          fi
      - name: Update Parameter Store with image
        run: |
          aws ssm put-parameter --type "String" --overwrite --name "/images/${{ env.CONTAINER_NAME }}" --value $IMAGE

  slackNotificationPipelineFinished:
    name: Pipeline Finished
    if: github.event_name == 'push' && always()
    needs: deploy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: martialonline/workflow-status@v4.1
        id: check
      - name: Notification Succeded
        if: steps.check.outputs.status == 'success'
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: BackendBot
          SLACK_TITLE: "Pipeline Execution Succeeded"
          SLACK_MESSAGE: "Pipeline Execution in Backend Repository Succeeded"
      - name: Notification Failed
        if: steps.check.outputs.status == 'failure'
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: BackendBot
          SLACK_TITLE: "Pipeline Execution Failed"
          SLACK_MESSAGE: "Pipeline Execution in Backend Repository Failed"
      - name: Notification Cancelled
        if: steps.check.outputs.status == 'cancelled'
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: BackendBot
          SLACK_TITLE: "Pipeline Execution Cancelled"
          SLACK_MESSAGE: "Pipeline Execution in Backend Repository Cancelled"
