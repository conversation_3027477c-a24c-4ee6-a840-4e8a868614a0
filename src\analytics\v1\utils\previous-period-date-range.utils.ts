export class PreviousPeriodDateRangeUtils {
  static getPreviousPeriodDateRange(startDateStr: string): {
    previousStartDate: string;
    previousEndDate: string;
  } {
    const startDate = new Date(startDateStr);

    // Get the current year and month
    const year = startDate.getFullYear();
    const month = startDate.getMonth(); // Months are 0-based (0 = January, 11 = December)

    let previousYear = year;
    let previousMonth = month - 1;
    if (previousMonth < 0) {
      previousMonth = 11;
      previousYear -= 1;
    }

    const previousStartDate = new Date(previousYear, previousMonth, 1);

    const previousEndDate = new Date(previousYear, previousMonth + 1, 0);

    // Format the dates as 'YYYY-MM-DD'
    const formatDate = (date: Date): string => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    return {
      previousStartDate: formatDate(previousStartDate),
      previousEndDate: formatDate(previousEndDate),
    };
  }
}
