import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsNumber } from 'class-validator';

export class InvoiceReservationDto {
  @ApiProperty({
    description: 'Array of Reservation IDs',
    type: Number,
    isArray: true,
    required: true,
  })
  @IsArray()
  @IsInt({ each: true })
  reservationIds: number[];

  @ApiProperty({
    description: 'Dispatcher Company ID',
    required: true,
  })
  @IsNumber()
  dispatcherCompanyId: number;
}
