import { BaseEntity } from 'src/common/entities/base.entity';
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { InvoiceStatus } from '../enums/invoice-status.enum';
import { Company } from 'src/company/entities/company.entity';
import { InvoiceType } from '../enums/invoice-type.enum';

@Entity('invoice_log')
@Index(['operatorCompanyId', 'dispatcherCompanyId'])
export class InvoiceLog extends BaseEntity {
  @Column('int', { array: true })
  reservationIds: number[];

  @Column({ nullable: true })
  @Index()
  operatorCompanyId?: number;

  @ManyToOne(() => Company, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'operatorCompanyId' })
  operatorCompany?: Company;

  @Column({ nullable: true })
  @Index()
  dispatcherCompanyId?: number;

  @ManyToOne(() => Company, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'dispatcherCompanyId' })
  dispatcherCompany?: Company;

  @Column({ nullable: true })
  invoiceNumber?: string;

  @Column({
    type: 'enum',
    enum: InvoiceStatus,
    default: InvoiceStatus.PENDING,
  })
  status: InvoiceStatus;

  @Column({
    type: 'enum',
    enum: InvoiceType,
    default: InvoiceType.BULK,
  })
  type: InvoiceType;

  @Column({ type: 'numeric', precision: 15, scale: 2 })
  total: number;
}
