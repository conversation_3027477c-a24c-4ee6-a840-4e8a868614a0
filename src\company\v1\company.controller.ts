import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { GetCompanyDto } from './dto/get-company.dto';
import { CompanyService } from './company.service';

@ApiTags('Companies')
@Controller({ path: 'companies', version: VERSION_NEUTRAL })
export class CompanyController {
  constructor(private readonly companiesService: CompanyService) {}

  @Post()
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
  ])
  async create(@Body() createCompanyDto: CreateCompanyDto, @Req() req) {
    return await this.companiesService.create(createCompanyDto, req?.user.sub);
  }

  @Post('get')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER_MANAGER },
  ])
  findMany(@Body() body: GetCompanyDto, @Req() req) {
    return this.companiesService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR },
    { role: Role.OPERATOR_MANAGER },
  ])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.companiesService.findOneById(+id, req.user);
  }

  @Patch(':id')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
  ])
  updateOneById(@Param('id') id: string, @Body() updateCompanyDto: UpdateCompanyDto, @Req() req) {
    return this.companiesService.updateOneById(+id, updateCompanyDto, req?.user?.sub);
  }

  @Delete(':id')
  @Roles([{ role: Role.MANAGER }])
  removeOneById(@Param('id') id: string) {
    return this.companiesService.removeOneById(+id);
  }
}
