import { Injectable, Inject, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import Redis from 'ioredis';
import { Repository } from 'typeorm';
import { Reservation } from 'src/reservation/entities/reservation.entity';
import { VehicleUtils } from 'src/vehicle/helpers/vehicle.utils';
import { Vehicle } from 'src/vehicle/entities/vehicle.entity';
import { AnalyticsBaseDto } from './dto/analytics-base.dto';
import { AverageM3PerCompanyDto } from './dto/average-m3-per-company.dto';
import { FrequentlyUsedVehiclesDto } from './dto/frequently-used-vehicles.dto';
import { FrequentlyUsedVehicle } from './interface/frequently-used-vehicle';
import { MostActiveContractor } from './interface/most-active-contractor';
import { HighestExpensesPerPumpingCompany } from './interface/highest-expenses-per-pumping-company';
import { MostActiveContractorsDto } from './dto/most-active-contractors.dto';
import { PreviousPeriodDateRangeUtils } from './utils/previous-period-date-range.utils';
import { CalculationUtils } from './utils/calculation.utils';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);
  private calculationUtils: CalculationUtils;

  constructor(
    @Inject('REDIS_CLIENT')
    private redisClient: Redis,

    @InjectRepository(Reservation)
    private reservationRepository: Repository<Reservation>,

    @InjectRepository(Vehicle)
    private vehicleRepository: Repository<Vehicle>,
  ) {
    this.calculationUtils = new CalculationUtils(reservationRepository);
  }

  async getAverageM3PerJob(query: AnalyticsBaseDto): Promise<{
    averageM3PerJob: number;
    progress: number | null;
  }> {
    const { dispatcherCompanyId, operatorCompanyId, startDate, endDate } = query;
    const companyId = dispatcherCompanyId || operatorCompanyId;
    const companyType = dispatcherCompanyId ? 'dispatcher' : 'operator';
    const cacheKey = `average_m3_per_job:${companyType}:${companyId}:${startDate}:${endDate}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return cachedResult;
      }

      this.logger.log(`Cache miss for key: ${cacheKey}. Calculating average m³ per job.`);

      const currentAverage = await this.calculationUtils.calculateAverageM3PerJob(
        startDate,
        endDate,
        dispatcherCompanyId,
        operatorCompanyId,
      );

      const { previousStartDate, previousEndDate } =
        PreviousPeriodDateRangeUtils.getPreviousPeriodDateRange(startDate);

      const previousAverage = await this.calculationUtils.calculateAverageM3PerJob(
        previousStartDate,
        previousEndDate,
        dispatcherCompanyId,
        operatorCompanyId,
      );

      let progress: number = 0;

      if (previousAverage !== null && previousAverage > 0) {
        progress = parseFloat(
          (((currentAverage - previousAverage) / previousAverage) * 100).toFixed(2),
        );
      }

      const result = {
        averageM3PerJob: currentAverage,
        progress,
      };

      await this.redisClient.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL_SECONDS);

      return result;
    } catch (error) {
      this.logger.error('Error calculating average m³ per job', error.stack);
      throw new BadRequestException('Failed to calculate average m³ per job.');
    }
  }

  async getAverageM3PerCompany(
    query: AverageM3PerCompanyDto,
  ): Promise<{ averageM3PerCompany: number; progress: number | null }> {
    const { dispatcherCompanyId, startDate, endDate, operatorCompanyId } = query;
    const cacheKey = `average_m3_per_company:${dispatcherCompanyId}:${operatorCompanyId}:${startDate}:${endDate}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return cachedResult;
      }

      this.logger.log(
        `Cache miss for key: ${cacheKey}. Calculating average m³ per hour for operator company.`,
      );

      const currentAverage = await this.calculationUtils.calculateAverageM3PerCompany(
        dispatcherCompanyId,
        operatorCompanyId,
        startDate,
        endDate,
      );

      const { previousStartDate, previousEndDate } =
        PreviousPeriodDateRangeUtils.getPreviousPeriodDateRange(startDate);

      const previousAverage = await this.calculationUtils.calculateAverageM3PerCompany(
        dispatcherCompanyId,
        operatorCompanyId,
        previousStartDate,
        previousEndDate,
      );

      let progress: number | null = null;
      if (previousAverage !== null && previousAverage > 0) {
        progress = parseFloat(
          (((currentAverage - previousAverage) / previousAverage) * 100).toFixed(2),
        );
      } else {
        progress = 0;
      }

      const result = {
        averageM3PerCompany: currentAverage,
        progress,
      };

      await this.redisClient.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL_SECONDS);

      return result;
    } catch (error) {
      this.logger.error('Error calculating average m³ per hour for operator company', error.stack);
      throw new BadRequestException(
        'Failed to calculate average m³ per hour for operator company.',
      );
    }
  }

  async getAverageM3PerHour(query: AnalyticsBaseDto): Promise<{
    averageM3PerHour: number;
    progress: number | null;
  }> {
    const { dispatcherCompanyId, operatorCompanyId, startDate, endDate } = query;
    const companyId = dispatcherCompanyId || operatorCompanyId;
    const companyType = dispatcherCompanyId ? 'dispatcher' : 'operator';
    const cacheKey = `average_m3_per_hour:${companyType}:${companyId}:${startDate}:${endDate}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return cachedResult;
      }

      this.logger.log(`Cache miss for key: ${cacheKey}. Calculating overall average m³ per hour.`);

      const currentAverage = await this.calculationUtils.calculateAverageM3PerHour(
        startDate,
        endDate,
        dispatcherCompanyId,
        operatorCompanyId,
      );

      const { previousStartDate, previousEndDate } =
        PreviousPeriodDateRangeUtils.getPreviousPeriodDateRange(startDate);

      const previousAverage = await this.calculationUtils.calculateAverageM3PerHour(
        previousStartDate,
        previousEndDate,
        dispatcherCompanyId,
        operatorCompanyId,
      );

      let progress: number | null = null;
      if (previousAverage !== null && previousAverage > 0) {
        progress = parseFloat(
          (((currentAverage - previousAverage) / previousAverage) * 100).toFixed(2),
        );
      } else {
        progress = 0;
      }

      const result = {
        averageM3PerHour: currentAverage,
        progress,
      };

      await this.redisClient.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL_SECONDS);

      return result;
    } catch (error) {
      this.logger.error('Error calculating overall average m³ per hour', error.stack);
      throw new BadRequestException('Failed to calculate overall average m³ per hour.');
    }
  }

  async getAverageJobCompletionTime(query: AnalyticsBaseDto): Promise<{
    averageCompletionTimeInHours: number;
    progress: number | null;
  }> {
    const { dispatcherCompanyId, operatorCompanyId, startDate, endDate } = query;
    const companyId = dispatcherCompanyId || operatorCompanyId;
    const companyType = dispatcherCompanyId ? 'dispatcher' : 'operator';
    const cacheKey = `average_job_completion_time:${companyType}:${companyId}:${startDate}:${endDate}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return cachedResult;
      }

      this.logger.log(`Cache miss for key: ${cacheKey}. Calculating average job completion time.`);

      const currentAverage = await this.calculationUtils.calculateAverageCompletionTime(
        startDate,
        endDate,
        dispatcherCompanyId,
        operatorCompanyId,
      );

      const { previousStartDate, previousEndDate } =
        PreviousPeriodDateRangeUtils.getPreviousPeriodDateRange(startDate);

      const previousAverage = await this.calculationUtils.calculateAverageCompletionTime(
        previousStartDate,
        previousEndDate,
        dispatcherCompanyId,
        operatorCompanyId,
      );

      let progress: number | null = null;

      if (previousAverage !== null && previousAverage > 0) {
        progress = parseFloat(
          (((currentAverage - previousAverage) / previousAverage) * 100).toFixed(2),
        );
      } else {
        progress = 0;
      }

      const result = {
        averageCompletionTimeInHours: currentAverage,
        progress,
      };

      await this.redisClient.set(cacheKey, JSON.stringify(result), 'EX', CACHE_TTL_SECONDS);

      return result;
    } catch (error) {
      this.logger.error('Error calculating average job completion time', error.stack);
      throw new BadRequestException('Failed to calculate average job completion time.');
    }
  }

  async getAverageReservationsPerDispatcher(
    query: AnalyticsBaseDto,
  ): Promise<{ averageReservationsPerDispatcher: number }> {
    const { dispatcherCompanyId, operatorCompanyId, startDate, endDate } = query;
    const companyId = dispatcherCompanyId || operatorCompanyId;
    const companyType = dispatcherCompanyId ? 'dispatcher' : 'operator';
    const cacheKey = `average_reservations_per_dispatcher:${companyType}:${companyId}:${startDate}:${endDate}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return { averageReservationsPerDispatcher: cachedResult };
      }

      this.logger.log(
        `Cache miss for key: ${cacheKey}. Calculating average reservations per dispatcher.`,
      );

      const queryBuilder = this.reservationRepository
        .createQueryBuilder('reservation')
        .innerJoin('reservation.dispatcher', 'dispatcher')
        .innerJoin('dispatcher.company', 'dispatcherCompany');

      if (dispatcherCompanyId) {
        queryBuilder
          .select('reservation.dispatcherId', 'dispatcherId')
          .addSelect('COUNT(*)', 'reservationCount')
          .where('dispatcherCompany.id = :dispatcherCompanyId', { dispatcherCompanyId })
          .andWhere('reservation.dispatcherId IS NOT NULL')
          .groupBy('reservation.dispatcherId');
      } else if (operatorCompanyId) {
        queryBuilder
          .innerJoin('reservation.operator', 'operator')
          .innerJoin('operator.company', 'operatorCompany')
          .select('reservation.dispatcherId', 'dispatcherId')
          .addSelect('COUNT(*)', 'reservationCount')
          .where('operatorCompany.id = :operatorCompanyId', { operatorCompanyId })
          .andWhere('reservation.dispatcherId IS NOT NULL')
          .groupBy('reservation.dispatcherId');
      }

      const result = await queryBuilder
        .andWhere('reservation.dateFrom BETWEEN :startDate AND :endDate', {
          startDate,
          endDate,
        })
        .andWhere('reservation.dateFrom IS NOT NULL')
        .andWhere('reservation.dateTo IS NOT NULL')
        .getRawMany();

      if (result.length === 0) {
        this.logger.warn('No reservations found for dispatchers in the specified date range.');
        await this.redisClient.set(cacheKey, '0', 'EX', CACHE_TTL_SECONDS);
        return { averageReservationsPerDispatcher: 0 };
      }

      const totalReservations = result.reduce(
        (sum, record) => sum + parseInt(record.reservationCount, 10),
        0,
      );
      const dispatcherCount = result.length;
      const averageReservationsPerDispatcher = parseFloat(
        (totalReservations / dispatcherCount).toFixed(2),
      );

      await this.redisClient.set(
        cacheKey,
        averageReservationsPerDispatcher.toString(),
        'EX',
        CACHE_TTL_SECONDS,
      );

      return { averageReservationsPerDispatcher };
    } catch (error) {
      this.logger.error('Error calculating average reservations per dispatcher', error.stack);
      throw new BadRequestException('Failed to calculate average reservations per dispatcher.');
    }
  }

  async getTotalReservationsPerMonth(query: AnalyticsBaseDto): Promise<{
    totalReservationsPerMonth: {
      month: string;
      totalReservations: number;
      progress: number | null;
    }[];
  }> {
    const { dispatcherCompanyId, operatorCompanyId, startDate, endDate } = query;
    const companyId = dispatcherCompanyId || operatorCompanyId;
    const companyType = dispatcherCompanyId ? 'dispatcher' : 'operator';

    const cacheKey = `total_reservations_per_month:${companyType}:${companyId}:${startDate}:${endDate}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return { totalReservationsPerMonth: cachedResult };
      }

      this.logger.log(`Cache miss for key: ${cacheKey}. Calculating total reservations per month.`);

      const { previousStartDate } =
        PreviousPeriodDateRangeUtils.getPreviousPeriodDateRange(startDate);

      const extendedStartDate = previousStartDate;
      const extendedEndDate = endDate;

      const queryBuilder = this.reservationRepository.createQueryBuilder('reservation');

      if (dispatcherCompanyId) {
        queryBuilder
          .innerJoin('reservation.dispatcher', 'dispatcher')
          .innerJoin('dispatcher.company', 'company')
          .where('company.id = :dispatcherCompanyId', { dispatcherCompanyId })
          .andWhere('dispatcher.deleted_at IS NULL');
      } else if (operatorCompanyId) {
        queryBuilder
          .innerJoin('reservation.operator', 'operator')
          .innerJoin('operator.company', 'company')
          .where('company.id = :operatorCompanyId', { operatorCompanyId })
          .andWhere('operator.deleted_at IS NULL');
      }

      const result = await queryBuilder
        .select([
          "TO_CHAR(reservation.dateFrom, 'YYYY-MM') AS month",
          'COUNT(*)::int AS "totalReservations"',
        ])
        .andWhere('reservation.dateFrom BETWEEN :extendedStartDate AND :extendedEndDate', {
          extendedStartDate,
          extendedEndDate,
        })
        .andWhere('reservation.dateFrom IS NOT NULL')
        .andWhere('reservation.deleted_at IS NULL')
        .andWhere('company.deleted_at IS NULL')
        .groupBy('month')
        .orderBy('month', 'ASC')
        .getRawMany();

      const dbResultMap = new Map<string, number>();
      result.forEach(row => {
        dbResultMap.set(row.month, parseInt(row.totalReservations, 10));
      });

      const months = this.calculationUtils.getMonthsBetweenDates(
        extendedStartDate,
        extendedEndDate,
      );

      const totalReservationsPerMonth = months.map(month => ({
        month,
        totalReservations: dbResultMap.get(month) || 0,
      }));

      let previousValue: number | null = null;

      const totalReservationsWithProgress = totalReservationsPerMonth.map((current, index) => {
        let progress: number | null = null;

        if (index === 0) {
          previousValue = current.totalReservations;
          return {
            ...current,
            progress: null,
          };
        } else {
          if (previousValue !== null && previousValue !== 0) {
            progress = ((current.totalReservations - previousValue) / previousValue) * 100;
            progress = parseFloat(progress.toFixed(2));
          }

          previousValue = current.totalReservations;

          return {
            ...current,
            progress,
          };
        }
      });

      // Remove previous period from result
      const filteredResults = totalReservationsWithProgress.filter(item => {
        return item.month >= startDate.slice(0, 7);
      });

      await this.redisClient.set(
        cacheKey,
        JSON.stringify(filteredResults),
        'EX',
        CACHE_TTL_SECONDS,
      );

      return { totalReservationsPerMonth: filteredResults };
    } catch (error) {
      this.logger.error('Error calculating total reservations per month', error.stack);
      throw new BadRequestException('Failed to calculate total reservations per month.');
    }
  }

  async getTotalExpensesPerMonth(query: AnalyticsBaseDto): Promise<{
    totalExpensesPerMonth: { month: string; totalExpenses: number; progress: number | null }[];
  }> {
    const { dispatcherCompanyId, operatorCompanyId, startDate, endDate } = query;
    const companyId = dispatcherCompanyId || operatorCompanyId;
    const companyType = dispatcherCompanyId ? 'dispatcher' : 'operator';

    const cacheKey = `total_expenses_per_month:${companyType}:${companyId}:${startDate}:${endDate}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return { totalExpensesPerMonth: cachedResult };
      }

      this.logger.log(`Cache miss for key: ${cacheKey}. Calculating total expenses per month.`);

      const { previousStartDate } =
        PreviousPeriodDateRangeUtils.getPreviousPeriodDateRange(startDate);

      const queryStartDate = previousStartDate;
      const queryEndDate = endDate;

      const queryBuilder = this.reservationRepository.createQueryBuilder('reservation');

      if (dispatcherCompanyId) {
        queryBuilder
          .innerJoin('reservation.dispatcher', 'dispatcher')
          .innerJoin('dispatcher.company', 'company')
          .where('company.id = :dispatcherCompanyId', { dispatcherCompanyId })
          .andWhere('dispatcher.deleted_at IS NULL');
      } else if (operatorCompanyId) {
        queryBuilder
          .innerJoin('reservation.operator', 'operator')
          .innerJoin('operator.company', 'company')
          .where('company.id = :operatorCompanyId', { operatorCompanyId })
          .andWhere('operator.deleted_at IS NULL');
      }

      // Query database for the adjusted date range
      const result = await queryBuilder
        .select([
          "TO_CHAR(reservation.dateFrom, 'YYYY-MM') AS month",
          'SUM(reservation.totalSum)::float AS "totalExpenses"',
        ])
        .andWhere('reservation.dateFrom BETWEEN :queryStartDate AND :queryEndDate', {
          queryStartDate,
          queryEndDate,
        })
        .andWhere('reservation.totalSum IS NOT NULL')
        .andWhere('reservation.deleted_at IS NULL')
        .andWhere('company.deleted_at IS NULL')
        .groupBy('month')
        .orderBy('month', 'ASC')
        .getRawMany();

      const dbResultMap = new Map<string, number>();
      result.forEach(row => {
        dbResultMap.set(row.month, parseFloat(row.totalExpenses));
      });

      const months = this.calculationUtils.getMonthsBetweenDates(queryStartDate, endDate);

      const totalExpensesPerMonth = months.map(month => ({
        month,
        totalExpenses: dbResultMap.get(month) || 0,
      }));

      let previousValue: number | null = null;

      const totalExpensesWithProgress = totalExpensesPerMonth.map(current => {
        let progress: number | null = null;

        if (previousValue !== null && previousValue !== 0) {
          progress = ((current.totalExpenses - previousValue) / previousValue) * 100;
          progress = parseFloat(progress.toFixed(2));
        }

        previousValue = current.totalExpenses;

        return {
          ...current,
          progress,
        };
      });

      const filteredResults = totalExpensesWithProgress.filter(item => {
        return item.month >= startDate.slice(0, 7);
      });

      await this.redisClient.set(
        cacheKey,
        JSON.stringify(filteredResults),
        'EX',
        CACHE_TTL_SECONDS,
      );

      return { totalExpensesPerMonth: filteredResults };
    } catch (error) {
      this.logger.error('Error calculating total expenses per month', error.stack);
      throw new BadRequestException('Failed to calculate total expenses per month.');
    }
  }

  async getFrequentlyUsedVehicles(
    query: FrequentlyUsedVehiclesDto,
  ): Promise<{ frequentlyUsedVehicles: FrequentlyUsedVehicle[]; total: number }> {
    const { dispatcherCompanyId, operatorCompanyId, startDate, endDate, limit, offset } = query;
    const companyId = dispatcherCompanyId || operatorCompanyId;
    const companyType = dispatcherCompanyId ? 'dispatcher' : 'operator';
    const cacheKey = `frequently_used_vehicles:${companyType}:${companyId}:${startDate}:${endDate}:${
      limit || 'all'
    }:${offset || 'all'}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return { frequentlyUsedVehicles: cachedResult, total: cachedResult.length };
      }

      this.logger.log(`Cache miss for key: ${cacheKey}. Calculating frequently used vehicles.`);

      const queryBuilder = this.vehicleRepository
        .createQueryBuilder('vehicle')
        .innerJoin('reservation', 'reservation', 'reservation.vehicleId = vehicle.id')
        .innerJoinAndSelect('vehicle.manager', 'manager')
        .innerJoinAndSelect('manager.company', 'managerCompany')
        .where('vehicle.deleted_at IS NULL')
        .andWhere('reservation.deleted_at IS NULL')
        .andWhere('reservation.totalSum IS NOT NULL');

      if (dispatcherCompanyId) {
        queryBuilder
          .innerJoin('reservation.dispatcher', 'dispatcher')
          .innerJoin('dispatcher.company', 'company')
          .andWhere('company.id = :dispatcherCompanyId', { dispatcherCompanyId });
      } else if (operatorCompanyId) {
        queryBuilder
          .innerJoin('reservation.operator', 'operator')
          .innerJoin('operator.company', 'company')
          .andWhere('company.id = :operatorCompanyId', { operatorCompanyId });
      }

      queryBuilder
        .andWhere('reservation.dateFrom BETWEEN :startDate AND :endDate', {
          startDate,
          endDate,
        })
        .setParameters({ startDate, endDate, dispatcherCompanyId, operatorCompanyId })
        .addSelect('COUNT(reservation.id)', 'reservationCount')
        .addSelect('SUM(reservation.totalSum)', 'totalSum')
        .groupBy('vehicle.id')
        .addGroupBy('manager.id')
        .addGroupBy('managerCompany.id')
        .orderBy('"reservationCount"', 'DESC');

      if (limit) {
        queryBuilder.limit(limit);
      }

      if (offset) {
        queryBuilder.offset(offset);
      }

      const { entities: vehicles, raw } = await queryBuilder.getRawAndEntities();

      const frequentlyUsedVehicles: FrequentlyUsedVehicle[] = vehicles.map((vehicle, index) => {
        const record = raw[index];

        return {
          vehicleId: vehicle.id,
          vehicleName: VehicleUtils.getVehicleUniqueId(vehicle),
          companyName: vehicle.manager?.company?.name || '',
          reservationCount: parseInt(record.reservationCount, 10) || 0,
          totalSum: parseFloat(record.totalSum) || 0,
        };
      });

      await this.redisClient.set(
        cacheKey,
        JSON.stringify(frequentlyUsedVehicles),
        'EX',
        CACHE_TTL_SECONDS,
      );

      return { frequentlyUsedVehicles, total: frequentlyUsedVehicles.length };
    } catch (error) {
      this.logger.error('Error calculating frequently used vehicles', error.stack);
      throw new BadRequestException('Failed to calculate frequently used vehicles.');
    }
  }

  async getMostActiveContractors(
    query: MostActiveContractorsDto,
  ): Promise<{ mostActiveContractors: MostActiveContractor[]; total: number }> {
    const { dispatcherCompanyId, operatorCompanyId, startDate, endDate, limit, offset } = query;
    const companyId = dispatcherCompanyId || operatorCompanyId;
    const companyType = dispatcherCompanyId ? 'dispatcher' : 'operator';
    const cacheKey = `most_active_contractors:${companyType}:${companyId}:${startDate}:${endDate}:${
      limit || 'all'
    }:${offset || 'all'}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return { mostActiveContractors: cachedResult, total: cachedResult.length };
      }

      this.logger.log(`Cache miss for key: ${cacheKey}. Calculating most active contractors.`);

      let queryBuilder;

      if (dispatcherCompanyId) {
        queryBuilder = this.reservationRepository
          .createQueryBuilder('reservation')
          .innerJoin('reservation.operator', 'operator')
          .innerJoin('operator.company', 'operatorCompany')
          .innerJoin('reservation.dispatcher', 'dispatcher')
          .innerJoin('dispatcher.company', 'dispatcherCompany')
          .select([
            'operatorCompany.id AS "operatorCompanyId"',
            'operatorCompany.name AS "operatorCompanyName"',
            'COUNT(reservation.id) AS "reservationCount"',
            'SUM(reservation.totalSum) AS "totalSum"',
          ])
          .where('dispatcherCompany.id = :dispatcherCompanyId', { dispatcherCompanyId })
          .andWhere('dispatcher.deleted_at IS NULL')
          .andWhere('dispatcherCompany.deleted_at IS NULL')
          .groupBy('operatorCompany.id, operatorCompany.name');
      } else if (operatorCompanyId) {
        queryBuilder = this.reservationRepository
          .createQueryBuilder('reservation')
          .innerJoin('reservation.dispatcher', 'dispatcher')
          .innerJoin('dispatcher.company', 'dispatcherCompany')
          .innerJoin('reservation.operator', 'operator')
          .innerJoin('operator.company', 'operatorCompany')
          .select([
            'dispatcherCompany.id AS "operatorCompanyId"',
            'dispatcherCompany.name AS "operatorCompanyName"',
            'COUNT(reservation.id) AS "reservationCount"',
            'SUM(reservation.totalSum) AS "totalSum"',
          ])
          .where('operatorCompany.id = :operatorCompanyId', { operatorCompanyId })
          .andWhere('operator.deleted_at IS NULL')
          .andWhere('operatorCompany.deleted_at IS NULL')
          .groupBy('dispatcherCompany.id, dispatcherCompany.name');
      }

      queryBuilder
        .andWhere('reservation.deleted_at IS NULL')
        .andWhere('reservation.dateFrom BETWEEN :startDate AND :endDate', { startDate, endDate })
        .orderBy('"reservationCount"', 'DESC');

      if (limit) {
        queryBuilder.limit(limit);
      }

      if (offset) {
        queryBuilder.offset(offset);
      }

      const result = await queryBuilder.getRawMany();

      const mostActiveContractors: MostActiveContractor[] = result.map(record => ({
        operatorCompanyId: parseInt(record.operatorCompanyId, 10),
        operatorCompanyName: record.operatorCompanyName,
        reservationCount: parseInt(record.reservationCount, 10),
        totalSum: parseInt(record.totalSum, 10),
      }));

      // Cache the result
      await this.redisClient.set(
        cacheKey,
        JSON.stringify(mostActiveContractors),
        'EX',
        CACHE_TTL_SECONDS,
      );

      return { mostActiveContractors, total: mostActiveContractors.length };
    } catch (error) {
      this.logger.error('Error calculating most active contractors', error.stack);
      throw new BadRequestException('Failed to calculate most active contractors.');
    }
  }

  async getHighestExpensesPerPumpingCompany(
    query: AnalyticsBaseDto,
  ): Promise<{ highestExpensesPerPumpingCompany: HighestExpensesPerPumpingCompany[] }> {
    const { dispatcherCompanyId, startDate, endDate } = query;
    const cacheKey = `highest_expenses_per_pumping_company:${dispatcherCompanyId}:${startDate}:${endDate}`;
    const CACHE_TTL_SECONDS = 3600; // 1 hour

    try {
      const cachedResult = await this.getCachedResult(this.redisClient, cacheKey, this.logger);
      if (cachedResult !== null) {
        return { highestExpensesPerPumpingCompany: cachedResult };
      }

      this.logger.log(
        `Cache miss for key: ${cacheKey}. Calculating highest expenses per pumping company.`,
      );

      const queryBuilder = this.reservationRepository
        .createQueryBuilder('reservation')
        .innerJoin('reservation.operator', 'operator')
        .innerJoin('operator.company', 'operatorCompany')
        .innerJoin('reservation.dispatcher', 'dispatcher')
        .innerJoin('dispatcher.company', 'dispatcherCompany')
        .select([
          'operatorCompany.id AS "operatorCompanyId"',
          'operatorCompany.name AS "operatorCompanyName"',
          'COUNT(reservation.id) AS "totalJobsHandled"',
          '(SUM(reservation.totalSum) / COUNT(reservation.id)) AS "averageExpensePerJob"',
        ])
        .where('dispatcherCompany.id = :dispatcherCompanyId', { dispatcherCompanyId })
        .andWhere('reservation.deleted_at IS NULL')
        .andWhere('operator.deleted_at IS NULL')
        .andWhere('operatorCompany.deleted_at IS NULL')
        .andWhere('dispatcher.deleted_at IS NULL')
        .andWhere('dispatcherCompany.deleted_at IS NULL')
        .andWhere('reservation.totalSum IS NOT NULL')
        .andWhere('reservation.dateFrom BETWEEN :startDate AND :endDate', { startDate, endDate })
        .groupBy('operatorCompany.id, operatorCompany.name')
        .orderBy('"averageExpensePerJob"', 'DESC');

      const result = await queryBuilder.getRawMany();

      const highestExpensesPerPumpingCompany: HighestExpensesPerPumpingCompany[] = result.map(
        record => ({
          operatorCompanyId: parseInt(record.operatorCompanyId, 10),
          operatorCompanyName: record.operatorCompanyName,
          totalJobsHandled: parseInt(record.totalJobsHandled, 10),
          averageExpensePerJob: parseFloat(record.averageExpensePerJob),
        }),
      );

      // Cache the result
      await this.redisClient.set(
        cacheKey,
        JSON.stringify(highestExpensesPerPumpingCompany),
        'EX',
        CACHE_TTL_SECONDS,
      );

      return { highestExpensesPerPumpingCompany };
    } catch (error) {
      this.logger.error('Error calculating highest expenses per pumping company', error.stack);
      throw new BadRequestException('Failed to calculate highest expenses per pumping company.');
    }
  }

  async getCachedResult(redisClient, cacheKey, logger) {
    const cachedResult = await redisClient.get(cacheKey);
    if (cachedResult !== null) {
      logger.log(`Cache hit for key: ${cacheKey}`);
      return JSON.parse(cachedResult);
    }
    return null;
  }
}
