import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ReservationService } from './v1/reservation.service';
import { JobService } from 'src/job/v1/job.service';

@Injectable()
export class ReservationCronService {
  constructor(
    private readonly reservationService: ReservationService,
    private jobService: JobService,
  ) {}

  @Cron(CronExpression.EVERY_12_HOURS)
  async handleCron() {
    const reservations = await this.reservationService.findExpiredPendingReservations();

    const jobIds = reservations.map(reservation => reservation.job.id);

    await this.jobService.cancelJobs(jobIds);
  }
}
