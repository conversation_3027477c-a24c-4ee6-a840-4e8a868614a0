import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1722534577775 implements MigrationInterface {
  name = 'Migrations1722534577775';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job" ADD "estimatedTotalCost" integer NOT NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "estimatedTotalCost"`);
  }
}
