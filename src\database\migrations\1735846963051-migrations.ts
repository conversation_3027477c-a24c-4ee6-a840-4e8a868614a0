import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1735846963051 implements MigrationInterface {
  name = 'Migrations1735846963051';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" ALTER COLUMN "vat" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "pricelist" ALTER COLUMN "vat" DROP DEFAULT`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" ALTER COLUMN "vat" SET DEFAULT '{"name": "Standard Pumping Services", "percentage": 21}'`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" ALTER COLUMN "vat" SET NOT NULL`);
  }
}
