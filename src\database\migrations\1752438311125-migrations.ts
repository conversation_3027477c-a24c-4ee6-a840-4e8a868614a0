import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1752438311125 implements MigrationInterface {
  name = 'Migrations1752438311125';

  public async up(queryRunner: QueryRunner): Promise<void> {
    const tableExists = await queryRunner.hasTable('bulk_invoice_log');

    if (tableExists) {
      await queryRunner.query(
        `CREATE TYPE "public"."invoice_log_type_enum" AS ENUM('single', 'bulk')`,
      );
      await queryRunner.query(
        `ALTER TABLE "bulk_invoice_log" ADD "type" "public"."invoice_log_type_enum" NOT NULL DEFAULT 'bulk'`,
      );
      await queryRunner.query(`ALTER TABLE "bulk_invoice_log" RENAME TO "invoice_log"`);
    } else {
      await queryRunner.query(
        `CREATE TYPE "public"."invoice_log_status_enum" AS ENUM('Pending', 'Canceled', 'Failed', 'Completed')`,
      );
      await queryRunner.query(
        `CREATE TYPE "public"."invoice_log_type_enum" AS ENUM('single', 'bulk')`,
      );
      await queryRunner.query(
        `CREATE TABLE "invoice_log" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "reservationIds" integer array NOT NULL, "operatorCompanyId" integer, "dispatcherCompanyId" integer, "invoiceNumber" character varying, "status" "public"."invoice_log_status_enum" NOT NULL DEFAULT 'Pending', "type" "public"."invoice_log_type_enum" NOT NULL DEFAULT 'bulk', "total" numeric(15,2) NOT NULL, CONSTRAINT "PK_1be79907ba240e3a3df70682dc8" PRIMARY KEY ("id"))`,
      );
      await queryRunner.query(
        `CREATE INDEX "IDX_b805b0aa9e4213618fe63dd7fd" ON "invoice_log" ("operatorCompanyId") `,
      );
      await queryRunner.query(
        `CREATE INDEX "IDX_7030151585db791b4f4b9d0eb1" ON "invoice_log" ("dispatcherCompanyId") `,
      );
      await queryRunner.query(
        `CREATE INDEX "IDX_fbafe2d150dadf9c95a574025e" ON "invoice_log" ("operatorCompanyId", "dispatcherCompanyId") `,
      );
      await queryRunner.query(
        `ALTER TABLE "invoice_log" ADD CONSTRAINT "FK_b805b0aa9e4213618fe63dd7fd1" FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
      );
      await queryRunner.query(
        `ALTER TABLE "invoice_log" ADD CONSTRAINT "FK_7030151585db791b4f4b9d0eb16" FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if invoice_log table exists
    const tableExists = await queryRunner.hasTable('invoice_log');

    if (tableExists) {
      await queryRunner.query(`ALTER TABLE "invoice_log" RENAME TO "bulk_invoice_log"`);
      await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "type"`);
      await queryRunner.query(`DROP TYPE "public"."invoice_log_type_enum"`);
    } else {
      await queryRunner.query(
        `ALTER TABLE "invoice_log" DROP CONSTRAINT "FK_7030151585db791b4f4b9d0eb16"`,
      );
      await queryRunner.query(
        `ALTER TABLE "invoice_log" DROP CONSTRAINT "FK_b805b0aa9e4213618fe63dd7fd1"`,
      );
      await queryRunner.query(`DROP INDEX "public"."IDX_fbafe2d150dadf9c95a574025e"`);
      await queryRunner.query(`DROP INDEX "public"."IDX_7030151585db791b4f4b9d0eb1"`);
      await queryRunner.query(`DROP INDEX "public"."IDX_b805b0aa9e4213618fe63dd7fd"`);
      await queryRunner.query(`DROP TABLE "invoice_log"`);
      await queryRunner.query(`DROP TYPE "public"."invoice_log_type_enum"`);
      await queryRunner.query(`DROP TYPE "public"."invoice_log_status_enum"`);
    }
  }
}
