import { Module } from '@nestjs/common';
import { TrackAndTraceController } from './track-and-trace.controller';
import { TrackAndTraceService } from './track-and-trace.service';
import { JobModule } from 'src/job/job.module';
import { ReservationModule } from 'src/reservation/reservation.module';

@Module({
  imports: [JobModule, ReservationModule],
  controllers: [TrackAndTraceController],
  providers: [TrackAndTraceService],
})
export class TrackAndTraceModule {}
