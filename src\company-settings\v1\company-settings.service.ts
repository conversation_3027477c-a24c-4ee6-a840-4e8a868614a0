import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { CompanyService } from 'src/company/v1/company.service';
import {
  Expression,
  QueryOperator,
  SortDirections,
  addExpressions,
  ConditionType,
  getWhereQuery,
  getQuerySort,
  getQueryLimit,
} from 'src/libs/helpers/CeQuery';
import { DataSource, Repository } from 'typeorm';
import { CancellationSettings } from '../entities/cancellation-settings.entity';
import { CompanySettings } from '../entities/company-settings.entity';
import { WorkScheduleSettings } from '../entities/work-schedule-settings.entity';
import { CompanyHoliday } from '../entities/company-holiday.entity';
import {
  getCompanySettingsCountQuery,
  getCompanySettingsQuery,
} from '../queries/get-company-settings-query';
import { CreateCompanySettingsDto } from './dto/create-company-settings.dto';
import {
  CompanySettingsCategoryTypes,
  GetCompanySettingsDto,
} from './dto/get-company-settings.dto';
import { UpdateCompanySettingsDto } from './dto/update-company-settings.dto';

@Injectable()
export class CompanySettingsService {
  private readonly logger = new Logger(CompanySettingsService.name);
  constructor(
    @InjectRepository(CompanySettings)
    private companySettingsRepository: Repository<CompanySettings>,
    @InjectRepository(WorkScheduleSettings)
    private workScheduleSettingsRepository: Repository<WorkScheduleSettings>,
    @InjectRepository(CancellationSettings)
    private cancellationSettingsRepository: Repository<CancellationSettings>,
    @InjectRepository(CompanyHoliday)
    private companyHolidayRepository: Repository<CompanyHoliday>,
    @Inject(forwardRef(() => CompanyService))
    private companyService: CompanyService,
    private dataSource: DataSource,
  ) {}

  async create(
    createCompanySettingsDto: CreateCompanySettingsDto,
    creatingUser: SerializedUser,
  ): Promise<CompanySettings> {
    this.logger.log({ method: 'create' });
    const { companyId, workSchedules, cancellation, holidays } = createCompanySettingsDto;

    // Fetch company to ensure it exists
    const company = await this.companyService.findOneById(companyId);
    if (!company) {
      throw new NotFoundException(
        `Company with ID ${createCompanySettingsDto.companyId} not found`,
      );
    }

    // Check if company settings exists for this company
    const companySettingsExists = await this.findMany(
      {
        expressions: [],
        limit: 1,
        offset: 0,
        sortModel: [],
      },
      creatingUser,
    );
    if (companySettingsExists.totalCount != 0) {
      throw new HttpException(
        'Company Settings exists for this Company!',
        HttpStatus.UNPROCESSABLE_ENTITY,
      );
    }

    // Create CompanySettings first
    const companySettings = this.companySettingsRepository.create({
      company: company,
      created_by: creatingUser.sub,
    });

    await this.companySettingsRepository.save(companySettings);

    // Process workSchedule if it exists
    if (workSchedules) {
      const createdWorkSchedules = workSchedules.map(schedule =>
        this.workScheduleSettingsRepository.create({
          ...schedule,
          companySettings: companySettings,
        }),
      );

      await this.workScheduleSettingsRepository.save(createdWorkSchedules);
    }

    if (holidays) {
      const createdHolidays = holidays.map(holiday =>
        this.companyHolidayRepository.create({
          ...holiday,
          companySettings: companySettings,
        }),
      );

      await this.companyHolidayRepository.save(createdHolidays);
    }

    // Process cancellation if it exists
    if (cancellation) {
      const cancellationSettings = this.cancellationSettingsRepository.create({
        ...cancellation,
        settingsId: companySettings.id,
      });

      await this.cancellationSettingsRepository.save(cancellationSettings);
      companySettings.cancellationId = cancellationSettings.id;
    }

    await this.companySettingsRepository.save(companySettings);

    this.logger.log({
      method: 'create',
      creatingUserId: creatingUser.sub,
      companySettingsId: companySettings.id,
    });

    return companySettings;
  }

  async createDefaultSettings(companyId: number): Promise<CompanySettings> {
    const companySettings = this.companySettingsRepository.create({
      companyId,
    });

    await this.companySettingsRepository.save(companySettings);

    const cancellationSettings = this.cancellationSettingsRepository.create({
      cancellationWindow: '12:00',
      reducedCancellationWindow: '12:00',
      minTimeBetweenJobs: '00:15',
      settingsId: companySettings.id,
    });

    await this.cancellationSettingsRepository.save(cancellationSettings);
    companySettings.cancellationId = cancellationSettings.id;

    await this.companySettingsRepository.save(companySettings);
    const defaultWorkSchedules = [
      { day: 'Monday', startTime: '06:00', endTime: '18:00' },
      { day: 'Tuesday', startTime: '06:00', endTime: '18:00' },
      { day: 'Wednesday', startTime: '06:00', endTime: '18:00' },
      { day: 'Thursday', startTime: '06:00', endTime: '18:00' },
      { day: 'Friday', startTime: '06:00', endTime: '18:00' },
    ];

    const createdWorkSchedules = defaultWorkSchedules.map(schedule =>
      this.workScheduleSettingsRepository.create({
        ...schedule,
        companySettings: companySettings,
      }),
    );

    await this.workScheduleSettingsRepository.save(createdWorkSchedules);

    companySettings.workSchedules = createdWorkSchedules;

    return companySettings;
  }

  async findMany(
    body: GetCompanySettingsDto,
    fetchingUser: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<CompanySettings>> {
    this.logger.log({ method: 'findMany' });
    const { limit, offset, sortModel = [], relations = [] } = body;
    const companyId = fetchingUser?.companyId;
    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    expressionsToAdd.push({
      category: CompanySettingsCategoryTypes.companyId,
      operator: '=' as QueryOperator,
      value: companyId,
      conditionType: 'AND',
    });

    if (!sortModel.length) {
      sortModel.push({ field: CompanySettingsCategoryTypes.id, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const companysettingsSQL = getCompanySettingsQuery(
      whereQuery.query,
      querySort,
      queryLimit,
      relations,
    );

    try {
      let totalCount = 0;

      const companysettings = await queryRunner.manager.query(
        companysettingsSQL,
        whereQuery.params,
      );

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getCompanySettingsCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({
        method: 'findMany',
        query: body,
        companysettingsLength: companysettings.length,
      });
      return new EntityWithCountDto<CompanySettings>(CompanySettings, companysettings, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser: SerializedUser): Promise<CompanySettings> {
    this.logger.log({ method: 'findOneById', id });

    const body: GetCompanySettingsDto = {
      expressions: [
        {
          category: CompanySettingsCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
      ],
      limit: 1,
      offset: 0,
      sortModel: [],
      relations: ['workSchedules', 'cancellation', 'holidays'],
    };

    const result = await this.findMany(body, fetchingUser, false);

    const [companysettings] = result.data;

    if (!companysettings) {
      this.logger.error({ method: 'findOneById', id, error: 'Company Settings was not found' });
      throw new HttpException('Company Settings was not found!', HttpStatus.NOT_FOUND);
    }
    this.logger.log({
      method: 'findOneById',
      id,
      companysettingsId: companysettings.id,
    });
    return companysettings;
  }

  async updateOneById(
    companySettingsId: number,
    updateCompanySettingsDto: UpdateCompanySettingsDto,
    updatingUser: SerializedUser,
  ): Promise<CompanySettings> {
    this.logger.log({
      method: 'updateOneById',
      companySettingsId,
      updatingUserId: updatingUser.sub,
    });

    const { workSchedules, cancellation, holidays } = updateCompanySettingsDto;

    // Fetch existing CompanySettings with related workSchedules and cancellation
    const companySettings = await this.findOneById(companySettingsId, updatingUser);

    if (!companySettings) {
      throw new NotFoundException(`CompanySettings with ID ${companySettingsId} not found`);
    }

    // Process workSchedules
    if (workSchedules) {
      // Remove all existing workSchedules
      if (companySettings.workSchedules.length > 0) {
        await this.workScheduleSettingsRepository.remove(companySettings.workSchedules);
      }

      // Create and assign new workSchedules
      const newWorkSchedules = workSchedules.map(schedule =>
        this.workScheduleSettingsRepository.create({
          ...schedule,
          companySettings,
        }),
      );

      companySettings.workSchedules =
        await this.workScheduleSettingsRepository.save(newWorkSchedules);
    }

    if (holidays) {
      if (companySettings.holidays && companySettings.holidays.length > 0) {
        await this.companyHolidayRepository.remove(companySettings.holidays);
      }

      const newHolidays = holidays.map(holiday =>
        this.companyHolidayRepository.create({
          ...holiday,
          companySettings,
        }),
      );

      companySettings.holidays = await this.companyHolidayRepository.save(newHolidays);
    }

    // Process cancellation settings
    if (cancellation) {
      companySettings.cancellation = Object.assign(
        companySettings.cancellation ||
          ({
            settingsId: companySettingsId,
          } as CancellationSettings),
        cancellation,
      );
      const savedCancellation = await this.cancellationSettingsRepository.save(
        companySettings.cancellation,
      );
      companySettings.cancellationId = savedCancellation.id;
    }

    await this.companySettingsRepository.save(companySettings);

    this.logger.log({
      method: 'updateCompanySettings',
      companySettingsId: companySettings.id,
      updatingUserId: updatingUser.sub,
    });

    return companySettings;
  }

  async removeOneById(id: number, removingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removeOneById', id });
    await this.findOneById(id, removingUser);
    await this.companySettingsRepository.delete(id);
  }
}
