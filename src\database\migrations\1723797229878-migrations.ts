import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1723797229878 implements MigrationInterface {
  name = 'Migrations1723797229878';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" ADD "invoiceNumber" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "invoiceNumber"`);
  }
}
