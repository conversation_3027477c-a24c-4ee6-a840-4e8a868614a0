import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNumber, IsO<PERSON>al, IsString, Max, Min } from 'class-validator';
import { Location } from '../../entities/vehicle.entity';
import { VehicleUtils } from '../../helpers/vehicle.utils';
import { Transform, Type } from 'class-transformer';
import { processNumber } from 'src/common/helpers/processNumbers';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';
import { processBoolean } from 'src/common/helpers/processBooleans';
import { VehicleType } from 'src/common/constants/vehicle-type.enum';
import { Status } from 'src/common/constants/status.enum';

export class CreateVehicleDto {
  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.boomSize.min)
  @Max(VehicleUtils.valueRanges.boomSize.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  boomSize: number;

  @ApiProperty({
    required: true,
    enum: VehicleType,
    isArray: true,
    description: 'Array of job statuses',
  })
  @IsEnum(VehicleType, { each: true })
  type: VehicleType;

  @ApiProperty()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  operatorId?: number;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'typeOfMotorization'))
  typeOfMotorization: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'licensePlateNumber'))
  licensePlateNumber: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'vehicleBrand'))
  vehicleBrand: string;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'brandModel'))
  brandModel: string;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.weight.min)
  @Max(VehicleUtils.valueRanges.weight.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  weight: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.height.min)
  @Max(VehicleUtils.valueRanges.height.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  height: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.length.min)
  @Max(VehicleUtils.valueRanges.length.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  length: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.width.min)
  @Max(VehicleUtils.valueRanges.width.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  width?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.maxVerticalReach.min)
  @Max(VehicleUtils.valueRanges.maxVerticalReach.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  maxVerticalReach: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.maxHorizontalReach.min)
  @Max(VehicleUtils.valueRanges.maxHorizontalReach.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  maxHorizontalReach: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.endHoseLength.min)
  @Max(VehicleUtils.valueRanges.endHoseLength.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  endHoseLength?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.maxFlowRate.min)
  @Max(VehicleUtils.valueRanges.maxFlowRate.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  maxFlowRate: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.maxConcretePressure.min)
  @Max(VehicleUtils.valueRanges.maxConcretePressure.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  maxConcretePressure?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.availableFlexiblePipeLength80Mm.min)
  @Max(VehicleUtils.valueRanges.availableFlexiblePipeLength80Mm.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  availableFlexiblePipeLength80Mm: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.availableFlexiblePipeLength90Mm.min)
  @Max(VehicleUtils.valueRanges.availableFlexiblePipeLength90Mm.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  availableFlexiblePipeLength90Mm: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.availableFlexiblePipeLength100Mm.min)
  @Max(VehicleUtils.valueRanges.availableFlexiblePipeLength100Mm.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  availableFlexiblePipeLength100Mm: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.availableFlexiblePipeLength120Mm.min)
  @Max(VehicleUtils.valueRanges.availableFlexiblePipeLength120Mm.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  availableFlexiblePipeLength120Mm: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.maxDownwardReach.min)
  @Max(VehicleUtils.valueRanges.maxDownwardReach.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  maxDownwardReach: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.numberOfBoomSections.min)
  @Max(VehicleUtils.valueRanges.numberOfBoomSections.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  numberOfBoomSections?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.minUnfoldingHeight.min)
  @Max(VehicleUtils.valueRanges.minUnfoldingHeight.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  minUnfoldingHeight?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.boomRotation.min)
  @Max(VehicleUtils.valueRanges.boomRotation.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  boomRotation?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.frontOutriggerSpan.min)
  @Max(VehicleUtils.valueRanges.frontOutriggerSpan.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  frontOutriggerSpan?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.rearOutriggerSpan.min)
  @Max(VehicleUtils.valueRanges.rearOutriggerSpan.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  rearOutriggerSpan?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.frontPressureOnOutrigger.min)
  @Max(VehicleUtils.valueRanges.frontPressureOnOutrigger.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  frontPressureOnOutrigger?: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.rearPressureOnOutrigger.min)
  @Max(VehicleUtils.valueRanges.rearPressureOnOutrigger.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  rearPressureOnOutrigger?: number;

  @ApiProperty()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'boomUnfoldingSystem'))
  boomUnfoldingSystem: string;

  @ApiProperty()
  @IsBoolean()
  @Transform(({ value }) => processBoolean(value))
  bacExit: boolean;

  @ApiProperty()
  @IsBoolean()
  @Transform(({ value }) => processBoolean(value))
  bacExitReverse: boolean;

  @ApiProperty()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'siteAddress'))
  siteAddress: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'city'))
  city: string;

  @ApiProperty({ required: false, type: Number, description: 'Postcode' })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  plz: number;

  @ApiProperty()
  location: Location;

  @ApiProperty()
  @IsString()
  uniqueIdentificationNumber: string;

  @ApiProperty()
  @IsBoolean()
  @Transform(({ value }) => processBoolean(value))
  hasStrangler: boolean;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  frontSideOpening: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  rearSideOpening: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  invoicingPipesFrom: number;

  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  pipeLengthForSecondTechnician: number;

  @ApiProperty()
  @IsNumber()
  @Min(VehicleUtils.valueRanges.completedJobs.min)
  @Max(VehicleUtils.valueRanges.completedJobs.max)
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  completedJobs: number;

  @ApiProperty({ enum: Status, enumName: 'Status', default: Status.ACTIVE })
  @IsEnum(Status)
  @IsOptional()
  status?: Status = Status.ACTIVE;
}
