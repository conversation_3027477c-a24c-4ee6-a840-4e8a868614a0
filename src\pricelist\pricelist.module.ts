import { forwardRef, Module } from '@nestjs/common';
import { PricelistController } from './v1/pricelist.controller';
import { PricelistService } from './v1/pricelist.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Pricelist } from './entities/pricelist.entity';
import { UsersModule } from 'src/users/users.module';
import { VehicleModule } from 'src/vehicle/vehicle.module';
import { ContainerPricelistsService } from './v1/container-pricelists.service';
import { ContainerPricelists } from './entities/container-pricelists.entity';
import { PartnerModule } from 'src/partner/partner.module';
import { ContractModule } from 'src/contract/contract.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Pricelist, ContainerPricelists]),
    forwardRef(() => UsersModule),
    forwardRef(() => VehicleModule),
    forwardRef(() => PartnerModule),
    forwardRef(() => ContractModule),
  ],
  controllers: [PricelistController],
  providers: [PricelistService, ContainerPricelistsService],
  exports: [PricelistService, ContainerPricelistsService],
})
export class PricelistModule {}
