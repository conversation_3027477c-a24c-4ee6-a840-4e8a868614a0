import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1726517514270 implements MigrationInterface {
  name = 'Migrations1726517514270';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "UQ_7cafb96380bf55d72ec621d3f7c" UNIQUE ("vehicleSizeId", "parentId")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "UQ_7cafb96380bf55d72ec621d3f7c"`,
    );
  }
}
