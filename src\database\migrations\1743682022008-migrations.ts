import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1743682022008 implements MigrationInterface {
  name = 'Migrations1743682022008';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "reasonForDelay" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "reasonForDelay"`);
  }
}
