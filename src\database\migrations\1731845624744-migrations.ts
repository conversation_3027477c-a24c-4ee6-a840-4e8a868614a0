import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1731845624744 implements MigrationInterface {
  name = 'Migrations1731845624744';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "task" ADD "labelColor" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "labelColor"`);
  }
}
