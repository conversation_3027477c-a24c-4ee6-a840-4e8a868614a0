import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1726756578743 implements MigrationInterface {
  name = 'Migrations1726756578743';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_e5c16596d14b9cb2527e8e4633"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "vehicleTypeId" integer`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "UQ_00e5ddee29dd30d0433ff418418" UNIQUE ("vehicleTypeId")`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "type"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "vehicleTypeId" integer`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" DROP CONSTRAINT "FK_691cfe437aaf4c9ecb3f67cb2cc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" ADD CONSTRAINT "UQ_691cfe437aaf4c9ecb3f67cb2cc" UNIQUE ("pricelistId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f2787f31f717e0a77fdb16e5ea" ON "vehicle" ("vehicleTypeId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_f2787f31f717e0a77fdb16e5ea0" FOREIGN KEY ("vehicleTypeId") REFERENCES "vehicle_type"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_00e5ddee29dd30d0433ff418418" FOREIGN KEY ("vehicleTypeId") REFERENCES "vehicle_type"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" ADD CONSTRAINT "FK_691cfe437aaf4c9ecb3f67cb2cc" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" DROP CONSTRAINT "FK_691cfe437aaf4c9ecb3f67cb2cc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_00e5ddee29dd30d0433ff418418"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_f2787f31f717e0a77fdb16e5ea0"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_f2787f31f717e0a77fdb16e5ea"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" DROP CONSTRAINT "UQ_691cfe437aaf4c9ecb3f67cb2cc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" ADD CONSTRAINT "FK_691cfe437aaf4c9ecb3f67cb2cc" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "vehicleTypeId"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "type" character varying`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "UQ_00e5ddee29dd30d0433ff418418"`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "vehicleTypeId"`);
    await queryRunner.query(`CREATE INDEX "IDX_e5c16596d14b9cb2527e8e4633" ON "vehicle" ("type") `);
  }
}
