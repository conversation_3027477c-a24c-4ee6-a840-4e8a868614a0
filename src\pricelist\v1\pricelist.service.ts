import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreatePricelistDto } from './dto/create-pricelist.dto';
import { UpdatePricelistDto } from './dto/update-pricelist.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Pricelist } from '../entities/pricelist.entity';
import { DataSource, Repository } from 'typeorm';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { ContractCategoryTypes, GetContractDto } from 'src/contract/v1/dto/get-contract.dto';
import { ContractedStatus } from 'src/common/constants/user.enum';
import { ContractService } from 'src/contract/v1/contract.service';
import { GetPricelistsDto, PricelistCategoryTypes } from './dto/get-pricelists.dto';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { VehicleService } from 'src/vehicle/v1/vehicle.service';
import {
  Expression,
  QueryOperator,
  SortDirections,
  addExpressions,
  ConditionType,
  getWhereQuery,
  getQuerySort,
  getQueryLimit,
} from 'src/libs/helpers/CeQuery';
import { getPricelistsQuery, getPricelistsCountQuery } from '../queries/get-pricelist-query';
import { CreateManyPricelistsDto } from './dto/create-many-pricelists.dto';

@Injectable()
export class PricelistService {
  private readonly logger = new Logger(PricelistService.name);

  constructor(
    @InjectRepository(Pricelist)
    private pricelistRepository: Repository<Pricelist>,
    @Inject(forwardRef(() => VehicleService))
    private vehicleService: VehicleService,
    @Inject(forwardRef(() => ContractService))
    private contractService: ContractService,
    private dataSource: DataSource,
  ) {}

  async create(createPricelistDto: CreatePricelistDto, creatingUserId: number): Promise<Pricelist> {
    this.logger.log({
      method: 'createPricelist',
      creatingUser: creatingUserId,
    });
    const { vehicleId, ...options } = createPricelistDto;
    this.validatePricingOptions(options);

    const pricelist = this.pricelistRepository.create({
      ...options,
      created_by: creatingUserId,
    });

    if (vehicleId) {
      // checks if vehicle exists
      const vehicle = await this.vehicleService.findOne(vehicleId);
      pricelist.vehicleId = vehicle.id;
    }

    if (!createPricelistDto.vat) {
      pricelist.vat = {
        name: 'Standard Pumping Services',
        percentage: 21,
      };
    }

    const savedPricelist = await this.pricelistRepository.save(pricelist);
    this.logger.log({
      method: 'createPricelist',
      pricelistId: savedPricelist.id,
      creatingUser: creatingUserId,
    });
    return savedPricelist;
  }

  async createMany(
    createManyPricelistsDto: CreateManyPricelistsDto,
    creatingUser: SerializedUser,
  ): Promise<Pricelist[]> {
    const { pricelistsDto, containerId } = createManyPricelistsDto;

    this.logger.log({
      method: 'createMany',
      pricelists: pricelistsDto.length,
      creatingUser: creatingUser.sub,
    });

    const newPricelists = pricelistsDto.map(pricelist => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { id, containerId: pricelistContainerId, ...newPricelist } = pricelist;
      return this.pricelistRepository.create({
        ...newPricelist,
        containerId: containerId ?? pricelistContainerId,
        created_by: creatingUser.sub,
      });
    });

    const savedPricelists = await this.pricelistRepository.save(newPricelists);

    this.logger.log({
      method: 'createMany',
      createdCount: savedPricelists.length,
    });

    return savedPricelists;
  }

  async findMany(
    body: GetPricelistsDto,
    fetchingUser?: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<Pricelist>> {
    this.logger.log({ method: 'findMany' });
    const { limit, offset, sortModel = [], relations = ['user', 'vehicle', 'variants'] } = body;
    const companyId = fetchingUser?.companyId;
    const queryRunner = this.dataSource.createQueryRunner('slave');
    const expressionsToAdd: Expression[] = [];
    if (companyId) {
      expressionsToAdd.push({
        category: PricelistCategoryTypes.userCompanyid,
        operator: '=' as QueryOperator,
        value: companyId,
        conditionType: 'AND',
      });
    }

    if (!sortModel.length) {
      sortModel.push({ field: PricelistCategoryTypes.id, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const pricelistsSQL = getPricelistsQuery(whereQuery.query, querySort, queryLimit, relations);

    try {
      let totalCount = 0;

      const pricelists = await queryRunner.manager.query(pricelistsSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getPricelistsCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({ method: 'findMany', body, pricelistsLength: pricelists.length });

      return new EntityWithCountDto<Pricelist>(Pricelist, pricelists, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneGlobally(id: number): Promise<Pricelist | null> {
    this.logger.log({ method: 'findOneGlobally', id });

    const body: GetPricelistsDto = {
      expressions: [
        {
          category: PricelistCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
      ],
      relations: [],
      limit: 1,
      offset: 0,
      sortModel: [],
    };

    const result = await this.findMany(body, null, false);

    const [pricelist] = result.data;

    if (!pricelist) {
      throw new NotFoundException('Pricelist was not found!');
    }

    this.logger.log({ method: 'findOneGlobally', id, pricelistId: pricelist.id });
    return pricelist;
  }

  async findOneById(id: number, fetchingUser: SerializedUser): Promise<Pricelist | null> {
    this.logger.log({ method: 'findOneById', id });

    const body: GetPricelistsDto = {
      expressions: [
        {
          category: PricelistCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
      ],
      relations: ['user', 'variants'],
      limit: 1,
      offset: 0,
      sortModel: [],
    };

    const result = await this.findMany(body, fetchingUser, false);

    const [pricelist] = result.data;

    if (!pricelist) {
      throw new NotFoundException('Pricelist was not found!');
    }

    this.logger.log({ method: 'findOneById', id, pricelistId: pricelist.id });
    return pricelist;
  }

  async update(
    id: number,
    updatePricelistDto: UpdatePricelistDto,
    updatingUser: SerializedUser,
  ): Promise<Pricelist> {
    this.logger.log({
      method: 'updatePricelist',
      pricelistId: id,
      updatingUser: updatingUser.sub,
    });
    const { vehicleId, ...options } = updatePricelistDto;
    const pricelist = await this.findOneById(id, updatingUser);
    this.validatePricingOptions(options);
    if (vehicleId && pricelist.vehicleId != vehicleId) {
      // checks if vehicle exists
      const vehicle = await this.vehicleService.findOne(vehicleId);
      if (!pricelist.isContract) {
        pricelist.vehicleId = vehicle.id;
      }
    }

    Object.assign(pricelist, options);

    const savedPricelist = await this.pricelistRepository.save(pricelist);

    this.logger.log({
      method: 'updatePricelist',
      pricelistId: pricelist.id,
      updatingUser: updatingUser.sub,
    });
    return savedPricelist;
  }

  async remove(id: number, removingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removePricelist', id, removingUser });

    const pricelist = await this.findOneById(id, removingUser);

    if (!pricelist) {
      throw new BadRequestException('Pricelist not found');
    }

    const query: GetContractDto = {
      expressions: [
        {
          category: ContractCategoryTypes.pricelistId,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
        {
          category: ContractCategoryTypes.status,
          operator: 'in' as QueryOperator,
          value: [ContractedStatus.PENDING, ContractedStatus.APPROVED],
          conditionType: 'AND',
        },
      ],
      limit: 1,
      offset: 0,
      sortModel: [],
      relations: [],
    };

    const { totalCount } = await this.contractService.findMany(query, removingUser);

    if (totalCount > 0) {
      throw new BadRequestException(
        'Ensure that you cancel the existing contracts associated with this pricelist before proceeding',
      );
    }
    await this.pricelistRepository.delete(id);
  }

  validatePricingOptions(options: Partial<CreatePricelistDto>) {
    const hasPricePerMeter = options.pricePerMeterPumped != null;
    const hasPumpTiers = Array.isArray(options.pumpTiers) && options.pumpTiers.length > 0;

    if (!hasPricePerMeter && !hasPumpTiers) {
      throw new BadRequestException(
        "Either 'pricePerMeterPumped' or 'pumpTiers' must be provided!",
      );
    }

    if (hasPricePerMeter && hasPumpTiers) {
      throw new BadRequestException(
        "'pricePerMeterPumped' and 'pumpTiers' cannot both be provided at the same time!",
      );
    }
  }
}
