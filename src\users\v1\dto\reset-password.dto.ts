import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, MinLength } from 'class-validator';
import { IsPasswordMatching } from 'src/common/validators/password-match-validator';

export class ResetPasswordDto {
  @ApiProperty()
  @IsNotEmpty()
  token: string;

  @ApiProperty()
  @IsNotEmpty()
  @MinLength(6)
  newPassword: string;

  @ApiProperty()
  @IsNotEmpty()
  @MinLength(6)
  @IsPasswordMatching('newPassword', {
    message: 'Password confirmation does not match the new password',
  })
  newPasswordConfirmation: string;
}
