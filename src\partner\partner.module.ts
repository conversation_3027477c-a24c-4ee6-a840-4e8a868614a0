import { forwardRef, Module } from '@nestjs/common';
import { PartnerService } from './v1/partner.service';
import { PartnerController } from './v1/partner.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Partner } from './entities/partner.entity';
import { PricelistModule } from 'src/pricelist/pricelist.module';

@Module({
  imports: [TypeOrmModule.forFeature([Partner]), forwardRef(() => PricelistModule)],
  controllers: [PartnerController],
  providers: [PartnerService],
  exports: [PartnerService],
})
export class PartnerModule {}
