import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706742259707 implements MigrationInterface {
  name = 'Migrations1706742259707';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "managerId" integer`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_55b219065150443755f93d2671f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "UQ_55b219065150443755f93d2671f" UNIQUE ("operatorId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_55b219065150443755f93d2671f" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_55b219065150443755f93d2671f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "UQ_55b219065150443755f93d2671f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_55b219065150443755f93d2671f" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "managerId"`);
  }
}
