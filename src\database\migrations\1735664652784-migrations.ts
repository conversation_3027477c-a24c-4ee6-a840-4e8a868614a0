import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1735664652784 implements MigrationInterface {
  name = 'Migrations1735664652784';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."bulk_invoice_log_status_enum" AS ENUM('Pending', 'Canceled', 'Failed', 'Completed')`,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD "status" "public"."bulk_invoice_log_status_enum" NOT NULL DEFAULT 'Pending'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."bulk_invoice_log_status_enum"`);
  }
}
