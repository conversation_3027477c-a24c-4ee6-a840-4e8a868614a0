import { CompanyHoliday } from 'src/company-settings/entities/company-holiday.entity';

/**
 * Helper function to check if a date falls on a company holiday
 * @param date - The date to check
 * @param holidays - Array of company holidays
 * @returns Object with isHoliday boolean and holiday reason if applicable
 */
export const isDateOnHoliday = (
  date: Date,
  holidays: CompanyHoliday[],
): { isHoliday: boolean; reason?: string } => {
  if (!holidays || holidays.length === 0) {
    return { isHoliday: false };
  }

  const dateString = date.toISOString().split('T')[0];

  const matchingHoliday = holidays.find(holiday => {
    const holidayDateString = new Date(holiday.date).toISOString().split('T')[0];
    return holidayDateString === dateString;
  });

  if (matchingHoliday) {
    return {
      isHoliday: true,
      reason: matchingHoliday.reason,
    };
  }

  return { isHoliday: false };
};

/**
 * Helper function to check if any date in a date range falls on company holidays
 * @param dateFrom - Start date of the range
 * @param dateTo - End date of the range
 * @param holidays - Array of company holidays
 * @returns Object with hasHolidays boolean and array of conflicting holidays
 */
export const checkDateRangeForHolidays = (
  dateFrom: Date,
  dateTo: Date,
  holidays: CompanyHoliday[],
): { hasHolidays: boolean; conflictingHolidays: Array<{ date: Date; reason: string }> } => {
  if (!holidays || holidays.length === 0) {
    return { hasHolidays: false, conflictingHolidays: [] };
  }

  const conflictingHolidays: Array<{ date: Date; reason: string }> = [];
  const startDate = new Date(dateFrom);
  const endDate = new Date(dateTo);

  holidays.forEach(holiday => {
    const holidayDate = new Date(holiday.date);

    const holidayDateString = holidayDate.toISOString().split('T')[0];
    const startDateString = startDate.toISOString().split('T')[0];
    const endDateString = endDate.toISOString().split('T')[0];

    if (holidayDateString >= startDateString && holidayDateString <= endDateString) {
      conflictingHolidays.push({
        date: holidayDate,
        reason: holiday.reason,
      });
    }
  });

  return {
    hasHolidays: conflictingHolidays.length > 0,
    conflictingHolidays,
  };
};

/**
 * Helper function to format holiday conflict error message
 * @param conflictingHolidays - Array of conflicting holidays
 * @returns Formatted error message
 */
export const formatHolidayErrorMessage = (
  conflictingHolidays: Array<{ date: Date; reason: string }>,
): string => {
  if (conflictingHolidays.length === 0) {
    return '';
  }

  if (conflictingHolidays.length === 1) {
    const holiday = conflictingHolidays[0];
    const formattedDate = holiday.date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    return `Reservation cannot be created on ${formattedDate} due to company holiday: ${holiday.reason}`;
  }

  const sortedHolidays = [...conflictingHolidays].sort((a, b) => a.date.getTime() - b.date.getTime());

  const maxDisplay = 3;
  const holidaysToShow = sortedHolidays.slice(0, maxDisplay);
  const remainingCount = sortedHolidays.length - maxDisplay;

  const holidayList = holidaysToShow
    .map(holiday => {
      const formattedDate = holiday.date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
      });
      return `${formattedDate} (${holiday.reason})`;
    })
    .join(', ');

  let message = `Reservation cannot be created during the selected period due to company holidays: ${holidayList}`;

  if (remainingCount > 0) {
    message += ` and ${remainingCount} more`;
  }

  return message;
};
