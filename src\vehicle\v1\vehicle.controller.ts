import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Query,
  VERSION_NEUTRAL,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { VehicleService } from './vehicle.service';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { ApiTags, ApiConsumes } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { GetVehiclesDto } from 'src/vehicle/v1/dto/get-vehicles.dto';
import { CheckVehicleSlotAvailabilityDto } from './dto/check-vehicle-slot-availability.dto';
import { GetVehiclesSearchDto } from './dto/get-vehicles-search.dto';

@ApiTags('Vehicles')
@Controller({ path: 'vehicle', version: VERSION_NEUTRAL })
export class VehicleController {
  constructor(private readonly vehicleService: VehicleService) {}

  @Post()
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  create(@Body() createVehicleDto: CreateVehicleDto, @Req() req) {
    return this.vehicleService.create(createVehicleDto, req?.user?.sub);
  }

  @Get()
  @Roles([
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.DISPATCHER_MANAGER },
  ])
  findAll(@Query() query: GetVehiclesDto, @Req() req) {
    return this.vehicleService.findMany(query, req?.user);
  }

  @Get('search')
  @Roles([
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
  ])
  findAllSearch(@Query() query: GetVehiclesSearchDto, @Req() req) {
    return this.vehicleService.findManySearch(query, req?.user);
  }

  @Get('many-with-reservations')
  @Roles([
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
  ])
  findManyWithReservations(@Query() query: GetVehiclesSearchDto, @Req() req) {
    return this.vehicleService.findManyWithReservations(query, req?.user);
  }

  @Post('checkVehicleSlotAvailability')
  @Roles([
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
  ])
  async checkVehicleSlotAvailability(
    @Body() checkVehicleSlotAvailabilityDto: CheckVehicleSlotAvailabilityDto,
  ): Promise<boolean> {
    return this.vehicleService.checkVehicleAvailability(checkVehicleSlotAvailabilityDto);
  }

  @Get(':id')
  @Roles([
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR },
  ])
  findOne(@Param('id') id: string, @Req() req) {
    return this.vehicleService.findOne(+id, req?.user);
  }

  @Patch(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  update(@Param('id') id: string, @Body() updateVehicleDto: UpdateVehicleDto, @Req() req) {
    return this.vehicleService.update(+id, updateVehicleDto, req?.user?.sub);
  }

  @Delete(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  remove(@Param('id') id: string) {
    return this.vehicleService.remove(+id);
  }

  @Post(':id/upload-image')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  @UseInterceptors(FileInterceptor('image'))
  @ApiConsumes('multipart/form-data')
  async uploadVehicleImage(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Req() req
  ) {
    if (!file) {
      throw new BadRequestException('No image file provided');
    }

    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException('Only image files are allowed (JPEG, JPG, PNG, GIF)');
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size must be less than 5MB');
    }

    return this.vehicleService.uploadImage(+id, file, req?.user?.sub);
  }

  @Delete(':id/image')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  async removeVehicleImage(@Param('id') id: string, @Req() req) {
    return this.vehicleService.removeImage(+id, req?.user?.sub);
  }

  @Get(':id/image-url')
  @Roles([
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR },
  ])
  async getVehicleImageUrl(@Param('id') id: string) {
    return this.vehicleService.getImageUrl(+id);
  }
}
