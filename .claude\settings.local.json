{"permissions": {"allow": ["Bash(npm run typeorm:show-migrations:*)", "Read(/C:\\Users\\<USER>\\Desktop\\Projects\\ConcretEasy\\backend\\src\\reservation\\v1/**)", "Read(/C:\\Users\\<USER>\\Desktop\\Projects\\ConcretEasy\\web\\src\\common\\api/**)", "Read(/C:\\Users\\<USER>\\Desktop\\Projects\\ConcretEasy\\web\\src\\common\\types/**)", "Read(/C:\\Users\\<USER>\\Desktop\\Projects\\ConcretEasy\\web\\src\\common\\types/**)", "Read(/C:\\Users\\<USER>\\Desktop\\Projects\\ConcretEasy\\web\\src\\common\\types/**)", "Read(/C:\\Users\\<USER>\\Desktop\\Projects\\ConcretEasy\\web\\src\\locales\\en/**)", "Read(//c/Users/<USER>/Desktop/Projects/ConcretEasy/web/**)", "Bash(npm run:*)", "Bash(psql:*)", "<PERSON><PERSON>(mkdir:*)"], "deny": []}}