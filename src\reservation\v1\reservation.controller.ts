import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  VERSION_NEUTRAL,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { ReservationService } from './reservation.service';
import { CreateReservationDto } from './dto/create-reservation.dto';
import { UpdateReservationDto } from './dto/update-reservation.dto';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { GetReservationDto } from './dto/get-reservation.dto';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { FileValidationPipe } from 'src/job/pipes/file-validation.pipe';
import { InvoiceReservationDto } from './dto/invoice-reservation.dto';
import { SingleInvoiceDto } from './dto/single-invoice.dto';
import { GetInvoiceDto } from './dto/get-invoice.dto';
import { DuplicateReservationDto } from './dto/duplicate-reservation.dto';

@ApiTags('Reservations')
@Controller({ path: 'reservation', version: VERSION_NEUTRAL })
export class ReservationController {
  constructor(private readonly reservationService: ReservationService) {}

  @Post()
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'parkingPermitAcquiredFile', maxCount: 1 },
      { name: 'localAdministrationAuthorizationFile', maxCount: 1 },
      { name: 'trafficPlanFile', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  async create(
    @Body() createReservationDto: CreateReservationDto,
    @Req() req,
    @UploadedFiles(new FileValidationPipe())
    files: Record<string, Express.Multer.File[]>,
  ) {
    await this.handleFiles(createReservationDto, files);

    return this.reservationService.create(createReservationDto, req?.user);
  }

  @Post('get')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  findMany(@Body() body: GetReservationDto, @Req() req) {
    return this.reservationService.findMany(body, true, req?.user);
  }

  @Get(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  findOne(@Param('id') id: string, @Req() req) {
    return this.reservationService.findOneById(+id, req?.user);
  }

  @Get(':id/job-progress')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  getJobProgress(@Param('id') id: string, @Req() req) {
    return this.reservationService.getJobProgress(+id, req?.user);
  }

  @Patch(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'parkingPermitAcquiredFile', maxCount: 1 },
      { name: 'localAdministrationAuthorizationFile', maxCount: 1 },
      { name: 'trafficPlanFile', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  async update(
    @Param('id') id: string,
    @Body() updateReservationDto: UpdateReservationDto,
    @Req() req,
    @UploadedFiles(new FileValidationPipe())
    files: Record<string, Express.Multer.File[]>,
  ) {
    await this.handleFiles(updateReservationDto, files);

    return this.reservationService.update(+id, updateReservationDto, req?.user);
  }

  @Delete(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  remove(@Param('id') id: string, @Req() req) {
    return this.reservationService.remove(+id, req?.user);
  }

  @Get('nextUpcomingReservation/:operatorId')
  @Roles([{ role: Role.OPERATOR }])
  getUpcomingReservation(@Param('operatorId') operatorId: string, @Req() req) {
    return this.reservationService.getUpcomingReservation(+operatorId, req?.user);
  }

  @Get('pendingReservation/:operatorId')
  @Roles([{ role: Role.OPERATOR }])
  getPendingReservation(@Param('operatorId') operatorId: string, @Req() req) {
    return this.reservationService.getPendingReservation(+operatorId, req?.user);
  }

  @Get('invoice/:reservationId')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  getInvoice(@Param() getInvoiceDto: GetInvoiceDto) {
    return this.reservationService.generatePdfForReservation(getInvoiceDto.reservationId);
  }

  @Post('invoice')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  enqueueInvoiceForSingleReservation(@Body() singleInvoiceDto: SingleInvoiceDto, @Req() req) {
    return this.reservationService.enqueueInvoiceForSingleReservation(
      singleInvoiceDto,
      req?.user.sub,
    );
  }

  @Post('bulk-invoice')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  enqueueInvoiceForManyReservations(
    @Body() invoiceReservationDto: InvoiceReservationDto,
    @Req() req,
  ) {
    return this.reservationService.enqueueInvoiceForManyReservations(
      invoiceReservationDto,
      req?.user.sub,
    );
  }

  @Post('contract-invoice/:id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  enqueueInvoiceForContract(@Param('id') contractId: number, @Req() req) {
    return this.reservationService.enqueueInvoiceForContract(contractId, req?.user);
  }

  @Post('/:id/markAsRead')
  @Roles([{ role: Role.OPERATOR }])
  async markAsRead(@Param('id') id: string) {
    return this.reservationService.markAsRead(+id);
  }

  @Get(':id/cost')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  async reservationCost(@Param('id') id: string) {
    return this.reservationService.reservationCost(+id);
  }

  @Post(':id/duplicate')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  async duplicateReservation(
    @Param('id') id: string,
    @Body() duplicateReservationDto: DuplicateReservationDto,
    @Req() req,
  ) {
    return this.reservationService.duplicateReservation(+id, duplicateReservationDto, req?.user);
  }

  private async handleFiles<T extends CreateReservationDto | UpdateReservationDto>(
    reservationDto: T,
    files?: Record<string, Express.Multer.File[]>,
  ) {
    files = files || {};

    const fileFields = {
      parkingPermitAcquiredFile: 'parkingPermitAcquiredFile',
      localAdministrationAuthorizationFile: 'localAdministrationAuthorizationFile',
      trafficPlanFile: 'trafficPlanFile',
    };

    for (const [key, field] of Object.entries(fileFields)) {
      if (files[key] && files[key].length) {
        reservationDto[field] = files[key][0];
      }
    }
  }
}
