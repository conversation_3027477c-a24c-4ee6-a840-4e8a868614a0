import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QUEUES } from 'src/libs/bullmq/constants';
import { ReservationService } from '../v1/reservation.service';
import { MailerService } from 'src/mailer/mailer.service';
import { UsersService } from 'src/users/v1/users.service';
import { GenerateInvoiceForManyReservation } from '../v1/interface/reservation-invoice.interface';

@Processor(QUEUES.RESERVATION_BULK_INVOICE, {
  concurrency: 1,
  limiter: {
    max: 1, // Maximum job count
    duration: 15000, // per 15 seconds
  },
})
export class ProcessReservationBulkInvoiceConsumer extends WorkerHost {
  private readonly logger: Logger;

  constructor(
    private reservationService: ReservationService,
    private mailerService: MailerService,
    private userService: UsersService,
  ) {
    super();
    this.logger = new Logger(ProcessReservationBulkInvoiceConsumer.name);
  }

  async process(job: Job<any>): Promise<any> {
    this.logger.log(`Job id:${job.id} is being processed`);
    const { reservationIds, dispatcherCompanyId, operatorManagerId, invoiceLog } = job.data;

    const invoiceForManyReservation: GenerateInvoiceForManyReservation = {
      reservationIds,
      dispatcherCompanyId,
      operatorManagerId,
      invoiceLog,
    };

    return this.reservationService.generateInvoiceForManyReservations(invoiceForManyReservation);
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job<unknown>) {
    this.logger.log(`Job id:${job.id} completed`);
  }

  @OnWorkerEvent('failed')
  async onFailed(job: Job<any>, error: Error) {
    this.logger.log(`Job id:${job.id} has failed`, error.stack);

    const operatorManagerId = job.data.operatorManagerId;
    if (operatorManagerId) {
      const operatorManager = await this.userService.findOneById(operatorManagerId);
      if (operatorManager) {
        const emailContext = {
          name: operatorManager.firstName,
          surname: operatorManager.lastName,
          errorMessage: error.message,
          documentType: 'Invoice',
        };

        await this.mailerService.sendMail({
          to: operatorManager.email,
          subject: 'Bulk Invoice Processing Error',
          template: 'document-error-notification',
          context: emailContext,
        });
      }
    }
  }

  @OnWorkerEvent('error')
  onError(job: Job<unknown>) {
    this.logger.log(`Job id:${job.id} encountered an error`);
  }
}
