import { <PERSON>, Post, Body, Param, Req, VERSION_NEUTRAL, Delete } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Role } from 'src/common/constants/roles.enum';
import { Roles } from 'src/common/decorators/roles.decorator';
import { PartnerService } from './partner.service';
import { CreatePartnerDto } from './dto/create-partner.dto';
import { GetPartnerDto } from './dto/get-partner.dto';
import { AssignContainerPricelistsDto } from './dto/assign-container-pricelists-partner.dto';
import { RemoveContainerPricelistsDto } from './dto/remove-container-pricelists-partner.dto';
import { CreateManyPartnersDto } from './dto/create-multiple-partners.dto';

@ApiTags('Partners')
@Controller({ path: 'partners', version: VERSION_NEUTRAL })
export class PartnerController {
  constructor(private readonly partnerService: PartnerService) {}

  @Post('get')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER },
  ])
  findMany(@Body() body: GetPartnerDto, @Req() req) {
    return this.partnerService.findMany(body, req?.user);
  }

  @Post()
  @Roles([{ role: Role.DISPATCHER_MANAGER }])
  create(@Body() createPartnerDto: CreatePartnerDto, @Req() req) {
    return this.partnerService.create(createPartnerDto, req?.user);
  }

  @Post('create-many')
  @Roles([{ role: Role.DISPATCHER_MANAGER }])
  createMany(@Body() createPartnerDto: CreateManyPartnersDto, @Req() req) {
    return this.partnerService.createMany(createPartnerDto, req?.user);
  }

  @Post(':id/assign-containers')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  assignContainersById(
    @Param('id') id: number,
    @Body() assignPricelistsDto: AssignContainerPricelistsDto,
  ) {
    return this.partnerService.assignContainersById(id, assignPricelistsDto);
  }

  @Post(':id/remove-containers')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  removeContainersById(
    @Param('id') id: number,
    @Body() removePricelistsDto: RemoveContainerPricelistsDto,
  ) {
    return this.partnerService.removeContainersById(id, removePricelistsDto);
  }

  @Post(':id/accept')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  acceptOneById(@Param('id') id: number, @Req() req) {
    return this.partnerService.acceptOneById(id, req?.user);
  }

  @Post(':id/reject')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  rejectOneById(@Param('id') id: number, @Req() req) {
    return this.partnerService.rejectOneById(id, req?.user);
  }

  @Delete(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  removeOneById(@Param('id') id: number, @Req() req) {
    return this.partnerService.removeOneById(id, req?.user);
  }
}
