import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708550074611 implements MigrationInterface {
  name = 'Migrations1708550074611';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_b639b0e8a6d8095d93f7397adfb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_b639b0e8a6d8095d93f7397adfb" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_b639b0e8a6d8095d93f7397adfb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_b639b0e8a6d8095d93f7397adfb" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
