import { BadRequestException, HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Task } from '../entities/task.entity';
import { TaskComment } from '../entities/comment.entity';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { UsersService } from 'src/users/v1/users.service';
import { GetTaskDto, TaskCategoryTypes } from './dto/get-task.dto';
import { ActionType } from 'src/common/constants/task.enum';
import { TaskActivity } from '../entities/task-activity.entity';
import { TaskLabelService } from './task-label.service';
import {
  QueryOperator,
  SortDirections,
  Expression,
  addExpressions,
  ConditionType,
  getWhereQuery,
  getQuerySort,
  getQueryLimit,
} from 'src/libs/helpers/CeQuery';
import { getTasksQuery, getTasksCountQuery } from '../queries/get-task-query';

@Injectable()
export class TaskService {
  private readonly logger = new Logger(TaskService.name);

  constructor(
    @InjectRepository(Task)
    private taskRepository: Repository<Task>,
    @InjectRepository(TaskComment)
    private commentRepository: Repository<TaskComment>,
    @InjectRepository(TaskActivity)
    private activityRepository: Repository<TaskActivity>,
    private labelService: TaskLabelService,
    private userService: UsersService,
    private dataSource: DataSource,
  ) {}

  async create(createTaskDto: CreateTaskDto, creatingUser: SerializedUser): Promise<Task> {
    const creatingUserId = creatingUser.sub;
    const creatingCompanyId = creatingUser.companyId;
    this.logger.log({ method: 'createTask', creatingUserId });
    const {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      startDate,
      assigneeId,
      labelId,
      ...createTask
    } = createTaskDto;
    const assignee = await this.userService.findOneById(assigneeId);

    let taskLabel = null;
    if (labelId) {
      taskLabel = await this.labelService.findOneById(labelId, creatingUser);
    }

    const task = this.taskRepository.create({
      ...createTask,
      assignee: assignee,
      companyId: creatingCompanyId,
      label: taskLabel,
      created_by: creatingUserId,
    });

    await this.taskRepository.save(task);

    this.logger.log({ method: 'createTask', creatingUserId, taskId: task.id });
    return task;
  }

  async findMany(
    body: GetTaskDto,
    fetchingUser: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<Task>> {
    this.logger.log({ method: 'findMany' });
    const { limit, offset, relations, sortModel = [] } = body;
    const queryRelations = relations ?? ['assignee', 'company', 'activities', 'label', 'comments'];
    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    expressionsToAdd.push({
      category: TaskCategoryTypes.companyId,
      operator: '=' as QueryOperator,
      value: fetchingUser.companyId,
      conditionType: 'AND',
    });

    if (!sortModel.length) {
      sortModel.push({ field: TaskCategoryTypes.id, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const tasksSQL = getTasksQuery(whereQuery.query, querySort, queryLimit, queryRelations);

    try {
      let totalCount = 0;

      const tasks = await queryRunner.manager.query(tasksSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getTasksCountQuery(whereQuery.query, queryRelations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({ method: 'findMany', tasksLength: tasks.length });
      return new EntityWithCountDto<Task>(Task, tasks, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser: SerializedUser): Promise<Task> {
    this.logger.log({ method: 'findOneById', id });
    const result = await this.findMany(
      {
        expressions: [
          {
            category: TaskCategoryTypes.id,
            operator: '=' as QueryOperator,
            value: id,
            conditionType: 'AND',
          },
          {
            category: TaskCategoryTypes.companyId,
            operator: '=' as QueryOperator,
            value: fetchingUser.companyId,
            conditionType: 'AND',
          },
        ],
        sortModel: [],
        limit: 1,
        offset: 0,
        relations: ['assignee', 'company', 'comments', 'activities', 'label'],
      },
      fetchingUser,
      false,
    );
    const [task] = result.data;
    if (!task) {
      throw new HttpException('Task was not found!', HttpStatus.NOT_FOUND);
    }
    this.logger.log({ method: 'findOneById', id, taskId: task.id });
    return task;
  }

  async updateOneById(
    taskId: number,
    updateTaskDto: UpdateTaskDto,
    updatingUser: SerializedUser,
  ): Promise<Task> {
    const updatingUserId = updatingUser.sub;
    this.logger.log({ method: 'updateOneById', taskId, updatingUserId: updatingUser.sub });
    const { labelId, assigneeId, ...updateTask } = updateTaskDto;
    const { comments, ...task } = await this.findOneById(taskId, updatingUser);
    const originalStatus = task.status;
    const originalAssigneeId = task.assigneeId;
    const originalPriority = task.priority;
    const originalLabel = task.label;

    Object.assign(task, updateTask);

    const activityLogs = [];

    if (labelId != null && originalLabel?.id !== labelId) {
      const taskLabel = await this.labelService.findOneById(labelId, updatingUser);
      task.label = taskLabel;
      activityLogs.push(
        this.activityRepository.create({
          taskId,
          actionType: ActionType.LABEL_CHANGE,
          oldValue: originalLabel?.name ?? '',
          created_by: updatingUserId,
        }),
      );
    }

    if (updateTask.status != null && originalStatus !== updateTask.status) {
      activityLogs.push(
        this.activityRepository.create({
          taskId,
          actionType: ActionType.STATUS_UPDATE,
          oldValue: originalStatus,
          created_by: updatingUserId,
        }),
      );
    }

    if (assigneeId != null && originalAssigneeId !== assigneeId) {
      const assignee = await this.userService.findOneById(assigneeId);
      task.assignee = assignee;
      activityLogs.push(
        this.activityRepository.create({
          taskId,
          actionType: ActionType.ASSIGNEE_CHANGE,
          oldValue: originalAssigneeId.toString(),
          created_by: updatingUserId,
        }),
      );
    }

    if (updateTask.priority != null && originalPriority !== updateTask.priority) {
      activityLogs.push(
        this.activityRepository.create({
          taskId,
          actionType: ActionType.PRIORITY_UPDATE,
          oldValue: originalPriority,
          created_by: updatingUserId,
        }),
      );
    }

    await this.taskRepository.save({
      ...task,
    });
    this.logActivity(activityLogs);

    this.logger.log({ method: 'updateOneById', taskId, updatedTask: task });
    return { comments, ...task };
  }

  async removeOneById(id: number, removingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removeOneById', id });
    await this.findOneById(id, removingUser);
    await this.taskRepository.delete(id);
  }

  async addComment(
    taskId: number,
    content: string,
    creatingUser: SerializedUser,
  ): Promise<TaskComment> {
    const creatingUserId = creatingUser.sub;
    this.logger.log({ method: 'addComment', taskId, authorId: creatingUserId });

    await this.findOneById(taskId, creatingUser);

    const comment = this.commentRepository.create({
      content,
      taskId: taskId,
      created_by: creatingUserId,
    });
    await this.commentRepository.save(comment);

    this.logger.log({ method: 'addComment', taskId, commentId: comment.id });
    return comment;
  }

  private async logActivity(logs: TaskActivity[]): Promise<void> {
    if (!logs.length) return;
    this.activityRepository.save(logs);
    this.logger.log(`Saved ${logs.length} activity logs.`);
  }

  async findComments(taskId: number, searchingUser: SerializedUser): Promise<TaskComment[]> {
    this.logger.log({ method: 'findComments', taskId });

    await this.findOneById(taskId, searchingUser);

    const comments = await this.commentRepository
      .createQueryBuilder('comment')
      .leftJoin('user', 'user.id = comment.created_by')
      .where('comment.taskId = :taskId', { taskId })
      .select(['comment', 'user'])
      .getMany();

    return comments;
  }

  async findActivities(taskId: number, searchingUser: SerializedUser): Promise<TaskActivity[]> {
    this.logger.log({ method: 'findActivities', taskId });

    await this.findOneById(taskId, searchingUser);

    const activities = await this.activityRepository
      .createQueryBuilder('activity')
      .leftJoin('user', 'user.id = activity.created_by')
      .where('activity.taskId = :taskId', { taskId })
      .select(['activity', 'user'])
      .getMany();

    return activities;
  }
}
