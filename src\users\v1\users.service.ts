import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';
import { DataSource, Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { GetUsersQueryParam, UserCategoryTypes } from './dto/get-users.dto';
import { Role } from 'src/common/constants/roles.enum';
import { Status } from 'src/common/constants/status.enum';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { MailerService } from 'src/mailer/mailer.service';
import { randomBytes } from 'crypto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { VerificationEmail } from './interfaces/verification-email';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { CompanyService } from 'src/company/v1/company.service';
import { CreateCompanyDto } from 'src/company/v1/dto/create-company.dto';
import { Company } from 'src/company/entities/company.entity';
import { CompanyUtils } from 'src/company/company.utils';
import {
  ConditionType,
  Expression,
  QueryOperator,
  SortDirections,
  addExpressions,
  getQueryLimit,
  getQuerySort,
  getWhereQuery,
} from 'src/libs/helpers/CeQuery';
import { getUsersQuery, getUserCountQuery } from '../queries/get-user-query';
import { UnavailablePeriodService } from 'src/unavailable-period/v1/unavailable-period.service';
import { CreateUnavailablePeriodDto } from 'src/unavailable-period/v1/dto/create-unavailable-period.dto';
import { ReservationService } from 'src/reservation/v1/reservation.service';

enum TokenType {
  NewUser = 'new-user',
  ForgotPassword = 'forgot-password',
}

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private mailerService: MailerService,
    private dataSource: DataSource,
    @Inject(forwardRef(() => CompanyService))
    private companyService: CompanyService,
    @Inject(forwardRef(() => UnavailablePeriodService))
    private unavailablePeriodService: UnavailablePeriodService,
    @Inject(forwardRef(() => ReservationService))
    private reservationService: ReservationService,
  ) {}

  validateUserRole(creatingUserRole: number, createUserRole: number): void {
    if (creatingUserRole === Role.MANAGER) {
      if (createUserRole === Role.MANAGER) {
        throw new UnauthorizedException(
          `Unable to create Manager. Please contact your support administrator.`,
        );
      }

      if (createUserRole === Role.DISPATCHER || createUserRole === Role.OPERATOR) {
        throw new UnauthorizedException(
          `Unable to create Dispatcher or Operator. Please contact your support administrator.`,
        );
      }
    }

    if (
      creatingUserRole === Role.DISPATCHER_MANAGER ||
      creatingUserRole === Role.OPERATOR_MANAGER
    ) {
      if (createUserRole === Role.MANAGER) {
        throw new UnauthorizedException(
          `Unable to create Manager. Please contact your support administrator.`,
        );
      }

      if (createUserRole === Role.DISPATCHER_MANAGER || createUserRole === Role.OPERATOR_MANAGER) {
        throw new UnauthorizedException(
          `Unable to create Dispatcher Manager or Operator Manager. Please contact your support administrator.`,
        );
      }
    }
  }

  async create(createUserDto: CreateUserDto, creatingUser: SerializedUser): Promise<User> {
    this.logger.log({ method: 'createUser' });
    const emailUser = await this.usersRepository.findOne({
      where: {
        email: createUserDto.email,
      },
      relations: {},
    });

    if (emailUser) {
      throw new HttpException('Email already in use!', HttpStatus.UNPROCESSABLE_ENTITY);
    }

    this.validateUserRole(creatingUser.roleId, createUserDto.role);

    if (createUserDto.companyName && createUserDto.email) {
      const companyDto: CreateCompanyDto = {
        name: createUserDto.companyName,
        contactEmail: createUserDto.email,
        type: createUserDto.companyType,
        address: createUserDto.companyAddress,
        category: CompanyUtils.getCompanyCategoryByRole(createUserDto.role),
      };
      const company = await this.companyService.create(companyDto, creatingUser.sub);
      createUserDto.companyId = company.id;
    }

    if (!createUserDto.companyId || createUserDto.companyId === null) {
      createUserDto.companyId = creatingUser.companyId;
    }

    const user = this.usersRepository.create({
      ...createUserDto,
      created_by: creatingUser.sub,
    });

    const updatedUser = await this.regenerateUserToken(user, TokenType.NewUser);
    await this.sendVerificationEmail(updatedUser, TokenType.NewUser);

    await this.usersRepository.save(updatedUser);

    this.logger.log({ method: 'createUser', updatedUserId: updatedUser.id, userId: user.id });
    return updatedUser;
  }

  async findMany(
    body: GetUsersQueryParam,
    fetchingUser?: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<User>> {
    this.logger.log({ method: 'findMany' });

    const { limit, offset, sortModel = [] } = body;
    const userRoleId = fetchingUser?.roleId;
    const userId = fetchingUser?.sub;
    const companyId = fetchingUser?.companyId;

    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    switch (userRoleId) {
      case Role.MANAGER:
        expressionsToAdd.push({
          category: UserCategoryTypes.role,
          operator: 'in' as QueryOperator,
          value: [Role.DISPATCHER_MANAGER, Role.OPERATOR_MANAGER],
          conditionType: 'AND',
        });
        break;

      case Role.OPERATOR_MANAGER:
        expressionsToAdd.push({
          category: UserCategoryTypes.role,
          operator: 'in' as QueryOperator,
          value: [Role.OPERATOR, Role.OPERATOR_MANAGER],
          conditionType: 'AND',
        });
        break;

      case Role.DISPATCHER_MANAGER:
        expressionsToAdd.push({
          category: UserCategoryTypes.role,
          operator: 'in' as QueryOperator,
          value: [Role.DISPATCHER_MANAGER, Role.DISPATCHER],
          conditionType: 'AND',
        });
        break;

      default:
        break;
    }

    if (!sortModel.length) {
      sortModel.push({ field: UserCategoryTypes.firstName, sort: SortDirections.ASC });
    }

    // Company and User come from the SerializedUser object.
    // Because of its reusability, some part of our code that
    // use this function don't need to supply the SerializedUser
    const companyAndUserExpressionToAdd: Expression[] = [];
    if (companyId) {
      companyAndUserExpressionToAdd.push({
        category: UserCategoryTypes.companyId,
        operator: '=' as QueryOperator,
        value: companyId,
        conditionType: 'OR',
      });
    }
    if (userId) {
      companyAndUserExpressionToAdd.push({
        category: UserCategoryTypes.createdBy,
        operator: '=' as QueryOperator,
        value: userId,
        conditionType: 'OR',
      });
    }
    const expressionsWithOtherData: Expression[] = [];

    if (companyAndUserExpressionToAdd.length) {
      const companyAndUserExpressions = addExpressions(
        companyAndUserExpressionToAdd,
        body.expressions,
        'AND' as ConditionType,
      );
      expressionsWithOtherData.push(
        ...addExpressions(expressionsToAdd, companyAndUserExpressions, 'AND' as ConditionType),
      );
    } else {
      expressionsWithOtherData.push(
        ...addExpressions(expressionsToAdd, body.expressions, 'AND' as ConditionType),
      );
    }

    // Build the where query dynamically based on the expressions
    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;

    // Use the raw query for users
    const usersSQL = getUsersQuery(whereQuery.query, querySort, queryLimit);

    try {
      let totalCount = 0;

      // Fetch the users based on the constructed SQL query
      const users = await queryRunner.manager.query(usersSQL, whereQuery.params);

      // Optionally fetch the total count if needed
      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getUserCountQuery(whereQuery.query),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      // Return the result wrapped in EntityWithCountDto
      this.logger.log({ method: 'findMany', query: body, usersLength: users.length });
      return new EntityWithCountDto<User>(User, users, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser?: SerializedUser): Promise<User> {
    const userId = fetchingUser?.sub;

    this.logger.log({ method: 'findOneById', id, userId });

    const { data: users } = await this.findMany(
      {
        expressions: [{ category: UserCategoryTypes.id, operator: '=', value: id }],
        limit: 1,
        sortModel: [],
      },
      fetchingUser,
      false,
    );

    if (!users.length) {
      throw new HttpException('User was not found!', HttpStatus.NOT_FOUND);
    }
    this.logger.log({ method: 'findOneById', id, userId: users[0].id });
    return users[0];
  }

  async findManyByIds(ids: number[], fetchingUser: SerializedUser): Promise<User[]> {
    const userId = fetchingUser?.sub;

    this.logger.log({ method: 'findManyByIds', ids, userId });

    const { data: users } = await this.findMany(
      {
        expressions: [{ category: UserCategoryTypes.id, operator: 'in', value: ids }],
        sortModel: [],
        limit: 1000,
      },
      fetchingUser,
      false,
    );

    this.logger.log({ method: 'findManyByIds', ids, usersFound: users.length });
    return users;
  }

  async findByEmail(email: string): Promise<User | null> {
    this.logger.log({ method: 'findByEmail', email });
    const { data: users } = await this.findMany(
      {
        expressions: [{ category: UserCategoryTypes.email, operator: '=', value: email }],
        limit: 1,
        sortModel: [],
      },
      null,
      false,
    );

    if (!users.length) {
      throw new HttpException('User was not found!', HttpStatus.NOT_FOUND);
    }
    return users[0];
  }

  async updateOneById(
    id: number,
    updateUserDto: UpdateUserDto,
    updatingUser?: SerializedUser,
  ): Promise<User> {
    const userId = updatingUser?.sub;
    const companyId = updatingUser?.companyId;
    this.logger.log({
      method: 'updateOneById',
      userId: id,
      updatingUser: userId,
      companyId,
    });
    const user = await this.findOneById(id, updatingUser);
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const { data: emailUsers } = await this.findMany(
        {
          expressions: [{ category: UserCategoryTypes.email, operator: '=', value: id }],
          limit: 1,
          sortModel: [],
        },
        updatingUser,
        false,
      );

      if (emailUsers.length) {
        throw new HttpException('Email already in use!', HttpStatus.UNPROCESSABLE_ENTITY);
      }
    }

    if (
      updateUserDto.status === Status.INACTIVE &&
      user.role === Role.OPERATOR
    ) {
      const futureReservations = await this.reservationService.countFutureReservationsByOperator(
        user.id,
      );

      if (futureReservations > 0) {
        throw new BadRequestException(
          `Cannot set operator to inactive. This operator has ${futureReservations} future reservation(s).`,
        );
      }
    }

    if (updateUserDto.status === Status.UNAVAILABLE) {
      const payload: CreateUnavailablePeriodDto = {
        from: updateUserDto.from,
        to: updateUserDto.to,
        operatorId: user.id,
      };
      await this.unavailablePeriodService.create(payload, updatingUser);
    }

    if (updateUserDto.status === Status.ACTIVE) {
      const vehicles = user.operatorVehicles || [];
      const vehicleIds = vehicles.map(vehicle => vehicle.id);
      await this.unavailablePeriodService.removeVehiclesFromUnavailablePeriods(vehicleIds, user.id);
    }

    Object.assign(user, updateUserDto);
    const updatedUser = await this.usersRepository.save(user);

    this.logger.log({
      method: 'updateOneById',
      id,
      userId: user.id,
      updatingUser: userId,
    });
    return updatedUser;
  }

  async removeOneById(id: number, removingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removeOneById', id });
    const user = await this.findOneById(id, removingUser);
    await this.usersRepository.delete(user.id);
    if (user.role == Role.DISPATCHER_MANAGER || user.role == Role.OPERATOR_MANAGER) {
      await this.companyService.removeOneById(user.companyId);
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    this.logger.log({
      method: 'resetPassword',
      resetPasswordDto,
    });

    const { newPassword, newPasswordConfirmation, token } = resetPasswordDto;

    if (newPassword !== newPasswordConfirmation) {
      throw new BadRequestException('Passwords do not match');
    }

    const user = await this.usersRepository.findOne({
      where: { resetPasswordToken: token },
    });

    if (!user) {
      throw new NotFoundException('Reset password token is invalid');
    }

    if (new Date() > user.resetPasswordTokenExpiresAt) {
      await this.regenerateUserToken(user, TokenType.NewUser);
      await this.sendVerificationEmail(user, TokenType.NewUser);

      throw new UnauthorizedException(
        'Reset password token has expired, we sent you another email with the new token.',
      );
    }

    const hashedPassword = await bcrypt.hash(newPassword, await bcrypt.genSalt());

    await this.usersRepository.update(user.id, {
      password: hashedPassword,
      resetPasswordToken: null,
      resetPasswordTokenExpiresAt: null,
    });
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    this.logger.log({
      method: 'forgotPassword',
      forgotPasswordDto,
    });

    const { email } = forgotPasswordDto;

    const user = await this.findByEmail(email);

    const updatedUser = await this.regenerateUserToken(user, TokenType.ForgotPassword);

    await this.sendVerificationEmail(updatedUser, TokenType.ForgotPassword);

    await this.usersRepository.save(updatedUser);
  }

  async seed(): Promise<User> {
    this.logger.log({ method: 'seed' });
    const createUserDto: CreateUserDto = {
      firstName: 'admin',
      lastName: 'admin',
      email: '<EMAIL>',
      phoneNumber: '+123456789',
      status: Status.ACTIVE,
      role: Role.MANAGER,
    };
    const hashedPassword = await bcrypt.hash('password', await bcrypt.genSalt());

    const user = this.usersRepository.create({
      ...createUserDto,
    });
    user.password = hashedPassword;
    await this.usersRepository.save(user);
    this.logger.log({ method: 'seed', user });
    return user;
  }

  async sendVerificationEmail(user: User, tokenType: TokenType): Promise<void> {
    const resetPasswordLink = `${process.env.CONCRETE_EASY_APP_UI_URL}/auth/password/reset?token=${user.resetPasswordToken}`;
    let emailSubject: string;
    let emailTemplate: string;

    if (tokenType === TokenType.NewUser) {
      emailSubject = 'Welcome to Concreteasy';
      emailTemplate = 'welcome-email';
    } else if (tokenType === TokenType.ForgotPassword) {
      emailSubject = 'Reset Your Concreteasy Password';
      emailTemplate = 'forgot-password-email';
    } else {
      throw new Error(`Unsupported token type: ${tokenType}`);
    }

    const emailContext: VerificationEmail = {
      firstname: user.firstName,
      lastname: user.lastName,
      email: user.email,
      phone: user.phoneNumber,
      country: user.country,
      role: Role[user.role],
      passwordResetLink: resetPasswordLink,
    };

    await this.mailerService.sendMail({
      to: user.email,
      subject: emailSubject,
      template: emailTemplate,
      context: emailContext,
    });
  }

  async regenerateUserToken(user: User, type: TokenType): Promise<User> {
    user.resetPasswordToken = randomBytes(20).toString('hex');
    const expiresAt = new Date();
    if (type == TokenType.NewUser) {
      expiresAt.setDate(expiresAt.getDate() + 7);
    } else if (type == TokenType.ForgotPassword) {
      expiresAt.setTime(expiresAt.getTime() + 3600000);
    }
    user.resetPasswordTokenExpiresAt = expiresAt;
    return user;
  }

  async getFavoritedByCompanies(userId: number): Promise<Company[]> {
    this.logger.log({ method: 'getFavoritedByCompanies', userId });
    const user = await this.usersRepository.findOne({
      where: { id: userId },
      relations: ['company.favoritedBy.dispatcherCompany'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    const favoritedBy = user.company.favoritedBy.map(favorite => favorite.dispatcherCompany);
    return favoritedBy;
  }

  async findOneWithVehicles(userId: number): Promise<User> {
    this.logger.log({
      method: 'findOneWithVehicles',
      userId,
    });

    const user = this.usersRepository.findOne({
      where: {
        id: userId,
      },
      relations: ['operatorVehicles'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async updateOperatorStatus(operatorIds: number[], status: Status): Promise<void> {
    if (operatorIds.length === 0) return;

    await this.usersRepository.update(operatorIds, { status });
    this.logger.log({
      method: 'updateOperatorStatus',
      message: `Updated operators (${operatorIds.join(', ')}) to status: ${status}`,
    });
  }
}
