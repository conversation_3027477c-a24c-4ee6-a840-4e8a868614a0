import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { OperatorClientService } from './operator-client.service';
import { CreateOperatorClientDto } from './dto/create-operator-client.dto';
import { UpdateOperatorClientDto } from './dto/update-operator-client.dto';
import { GetOperatorClientDto } from './dto/get-operator-client.dto';

@ApiTags('OperatorClient')
@Controller({ path: 'operator-client', version: VERSION_NEUTRAL })
export class OperatorClientController {
  constructor(private readonly operatorClientService: OperatorClientService) {}

  @Post()
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  create(@Body() createOperatorClientDto: CreateOperatorClientDto, @Req() req) {
    return this.operatorClientService.create(createOperatorClientDto, req?.user?.sub);
  }

  @Post('get')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  findMany(@Body() body: GetOperatorClientDto, @Req() req) {
    return this.operatorClientService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.operatorClientService.findOneById(+id, req.user);
  }

  @Patch(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  updateOneById(
    @Param('id') id: string,
    @Body() updateOperatorClientDto: UpdateOperatorClientDto,
    @Req() req,
  ) {
    return this.operatorClientService.updateOneById(+id, updateOperatorClientDto, req?.user?.sub);
  }

  @Delete(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  removeOneById(@Param('id') id: string) {
    return this.operatorClientService.removeOneById(+id);
  }
}
