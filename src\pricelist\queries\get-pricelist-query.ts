const groupBy = `
GROUP BY
  "pricelist"."id"
`;

export const getPricelistsQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT 
      "pricelist".*
      ${selects ? `, ${selects}` : ''}
    FROM "pricelist"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getPricelistsCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "pricelist"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  user: {
    join: `LEFT JOIN "user" AS "user" ON "pricelist"."created_by" = "user"."id"`,
    select: `jsonb_build_object(
      'id', "user"."id",
      'companyId', "user"."companyId"
    ) AS "user"`,
    groupBy: `"user"."id"`,
  },
  vehicle: {
    join: `LEFT JOIN "vehicle" AS "vehicle" ON "pricelist"."vehicleId" = "vehicle"."id"`,
    select: `jsonb_build_object(
      'id', "vehicle"."id",
      'boomSize', "vehicle"."boomSize",
      'uniqueIdentificationNumber', "vehicle"."uniqueIdentificationNumber"
    ) AS "vehicle"`,
    groupBy: `"vehicle"."id"`,
  },
  container: {
    join: `LEFT JOIN "container_pricelists" AS "container" ON "pricelist"."containerId" = "container"."id"`,
    select: `jsonb_build_object(
      'id', "container"."id",
      'title', "container"."title",
      'isPublic', "container"."isPublic",
      'isDefault', "container"."isDefault"
    ) AS "container"`,
    groupBy: `"container"."id"`,
  },
  containerPricelists: {
    join: `LEFT JOIN "container_pricelists" AS "container" ON "pricelist"."containerId" = "container"."id"`,
    select: `jsonb_build_object(
      'id', "container"."id",
      'title', "container"."title",
      'isPublic', "container"."isPublic",
      'isDefault', "container"."isDefault"
    ) AS "container"`,
    groupBy: `"container"."id"`,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
