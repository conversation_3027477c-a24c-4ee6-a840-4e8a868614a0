import { ApiProperty } from '@nestjs/swagger';
import { ArrayMaxSize, IsArray, IsInt, IsNumber } from 'class-validator';

export class SingleInvoiceDto {
  @ApiProperty({ type: Number, isArray: true })
  @IsArray()
  @IsInt({ each: true })
  @ArrayMaxSize(10, { message: 'The number of reservationIds should not exceed 10.' })
  reservationIds: number[];

  @ApiProperty({
    description: 'Dispatcher Company ID',
    required: true,
  })
  @IsNumber()
  dispatcherCompanyId: number;
}
