import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1707944622525 implements MigrationInterface {
  name = 'Migrations1707944622525';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "price_list_settings" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "packageFlatFeeDuration" integer NOT NULL, "pipeInvoicingStartsFrom" integer NOT NULL, "pumpTiers" jsonb NOT NULL, "transportRates" jsonb NOT NULL, "pricelistId" integer, CONSTRAINT "REL_eef017adcf2398ee6b6d992deb" UNIQUE ("pricelistId"), CONSTRAINT "PK_445ba10015f9b26fb03af86601e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "isDefault" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier1" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier2" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier3" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier4" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier5" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier6" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "supplyOfTheChemicalSlushie" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "barbotine" integer`);
    await queryRunner.query(
      `ALTER TABLE "price_list_settings" ADD CONSTRAINT "FK_eef017adcf2398ee6b6d992deb2" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "price_list_settings" DROP CONSTRAINT "FK_eef017adcf2398ee6b6d992deb2"`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "barbotine"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "supplyOfTheChemicalSlushie"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier6"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier5"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier4"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier3"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier2"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier1"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "isDefault"`);
    await queryRunner.query(`DROP TABLE "price_list_settings"`);
  }
}
