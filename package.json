{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./src/database/typeorm.ts", "migration:generate": "npm run typeorm -- -d ./src/database/typeorm.ts migration:generate ./src/database/migrations/%npm_config_name%", "migration:create": "npm run typeorm -- migration:create ./src/database/migrations/%npm_config_name%", "migration:revert": "npm run typeorm -- -d ./src/database/typeorm.ts migration:revert", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.549.0", "@aws-sdk/lib-storage": "^3.549.0", "@aws-sdk/s3-request-presigner": "^3.549.0", "@nestjs-modules/mailer": "^1.11.2", "@nestjs/bullmq": "^10.1.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.1", "@nestjs/swagger": "^7.1.17", "@nestjs/typeorm": "^10.0.1", "@types/nodemailer": "^6.4.14", "@willsoto/nestjs-prometheus": "^6.0.0", "bcrypt": "^5.1.1", "bullmq": "^5.8.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "csurf": "^1.11.0", "file-type": "^16.5.3", "fs-extra": "^11.2.0", "googleapis": "^134.0.0", "handlebars": "^4.7.8", "helmet": "^7.1.0", "ioredis": "^5.4.1", "moment": "^2.30.1", "pdfmake": "^0.2.10", "pg": "^8.11.3", "prom-client": "^15.1.1", "reflect-metadata": "^0.1.13", "retry": "^0.13.1", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.19", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}