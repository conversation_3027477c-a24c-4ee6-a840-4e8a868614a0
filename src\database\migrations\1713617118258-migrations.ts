import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1721287318526 implements MigrationInterface {
  name = 'Migrations1721287318526';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_4748f570a7192613ab3606d86cd"`,
    );
    await queryRunner.query(
      `CREATE TABLE "vehicle_group" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "name" character varying, "type" character varying, "managerId" integer, CONSTRAINT "PK_87b1f6177ef86cc7ba8b65a455e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "vehicle_pricelist" ("pricelistId" integer NOT NULL, "vehicleId" integer NOT NULL, CONSTRAINT "PK_f7d0a48ac57bd79434c149e671c" PRIMARY KEY ("pricelistId", "vehicleId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_64b73df56f913ad34a97120579" ON "vehicle_pricelist" ("pricelistId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5f96dddd394b4048c2c7bdf3eb" ON "vehicle_pricelist" ("vehicleId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "vehicle_group_pricelist" ("pricelistId" integer NOT NULL, "vehicleGroupId" integer NOT NULL, CONSTRAINT "PK_d923ef217d7513519b98f211af2" PRIMARY KEY ("pricelistId", "vehicleGroupId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d30768d4be1e7ceb0b703c765b" ON "vehicle_group_pricelist" ("pricelistId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_46eaef43de8d2780611749fcc1" ON "vehicle_group_pricelist" ("vehicleGroupId") `,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "settingsId"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "packageFlatFeeDuration" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "pipeInvoicingStartsFrom" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "parentId" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "pumpTiers" jsonb`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "transportRates" jsonb`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "secondTechnicianFee" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "backupPumpPackage" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "vehicleGroupId" integer`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_group" ADD CONSTRAINT "FK_3e3031b3194b0ba5f823d60d0fe" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_a3a88ad51cf1cc45a92bec25d26" FOREIGN KEY ("parentId") REFERENCES "pricelist"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_f3b488966ddab258a03dbcaab40" FOREIGN KEY ("vehicleGroupId") REFERENCES "vehicle_group"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_pricelist" ADD CONSTRAINT "FK_64b73df56f913ad34a97120579b" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_pricelist" ADD CONSTRAINT "FK_5f96dddd394b4048c2c7bdf3ebf" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_group_pricelist" ADD CONSTRAINT "FK_d30768d4be1e7ceb0b703c765b3" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_group_pricelist" ADD CONSTRAINT "FK_46eaef43de8d2780611749fcc1a" FOREIGN KEY ("vehicleGroupId") REFERENCES "vehicle_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`DROP TABLE IF EXISTS price_list_settings`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "price_list_settings" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "packageFlatFeeDuration" integer NOT NULL, "pipeInvoicingStartsFrom" integer NOT NULL, "pumpTiers" jsonb NOT NULL, "transportRates" jsonb NOT NULL, "pricelistId" integer, CONSTRAINT "REL_eef017adcf2398ee6b6d992deb" UNIQUE ("pricelistId"), CONSTRAINT "PK_445ba10015f9b26fb03af86601e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_group_pricelist" DROP CONSTRAINT "FK_46eaef43de8d2780611749fcc1a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_group_pricelist" DROP CONSTRAINT "FK_d30768d4be1e7ceb0b703c765b3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_pricelist" DROP CONSTRAINT "FK_5f96dddd394b4048c2c7bdf3ebf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_pricelist" DROP CONSTRAINT "FK_64b73df56f913ad34a97120579b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_f3b488966ddab258a03dbcaab40"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_a3a88ad51cf1cc45a92bec25d26"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_group" DROP CONSTRAINT "FK_3e3031b3194b0ba5f823d60d0fe"`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "vehicleGroupId"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "backupPumpPackage"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "secondTechnicianFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "transportRates"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "pumpTiers"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "parentId"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "pipeInvoicingStartsFrom"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "packageFlatFeeDuration"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "settingsId" integer`);
    await queryRunner.query(`DROP INDEX "public"."IDX_46eaef43de8d2780611749fcc1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d30768d4be1e7ceb0b703c765b"`);
    await queryRunner.query(`DROP TABLE "vehicle_group_pricelist"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5f96dddd394b4048c2c7bdf3eb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_64b73df56f913ad34a97120579"`);
    await queryRunner.query(`DROP TABLE "vehicle_pricelist"`);
    await queryRunner.query(`DROP TABLE "vehicle_group"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_4748f570a7192613ab3606d86cd" FOREIGN KEY ("settingsId") REFERENCES "price_list_settings"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
