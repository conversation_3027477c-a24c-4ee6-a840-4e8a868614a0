import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1724502992006 implements MigrationInterface {
  name = 'Migrations1724502992006';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "vehicle_size" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "type" character varying, "boomSize" integer, "weight" double precision, "height" double precision, "length" double precision, "width" double precision, "maxVerticalReach" double precision, "maxHorizontalReach" double precision, "endHoseLength" double precision, "maxDownwardReach" double precision, "minUnfoldingHeight" double precision, "boomRotation" double precision, "rubberEndHoseLength" double precision, "frontOutriggerSpan" double precision, "rearOutriggerSpan" double precision, "frontPressureOnOutrigger" double precision, "rearPressureOnOutrigger" double precision, "frontSideOpening" double precision, "rearSideOpening" double precision, "pipeLengthForSecondTechnician" double precision, "operatorManagerId" integer, CONSTRAINT "UQ_2e5c0c48cc2271c11addc6902aa" UNIQUE ("operatorManagerId", "boomSize"), CONSTRAINT "PK_15cab1165a9c35a1360013d41ca" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1db0695b35006be185320812b9" ON "vehicle_size" ("type") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d26f6b06bec69a341b98d924c4" ON "vehicle_size" ("boomSize") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c2758f74fecacf8c04ad234fe1" ON "vehicle_size" ("operatorManagerId") `,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "vehicleSizeId" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "vehicleSizeId" integer`);
    await queryRunner.query(
      `CREATE INDEX "IDX_6138d71264f56b4ff2355bc455" ON "pricelist" ("vehicleSizeId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_be2f02be96355e6412f5efd738" ON "vehicle" ("vehicleSizeId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" ADD CONSTRAINT "FK_c2758f74fecacf8c04ad234fe14" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_6138d71264f56b4ff2355bc455c" FOREIGN KEY ("vehicleSizeId") REFERENCES "vehicle_size"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_be2f02be96355e6412f5efd7380" FOREIGN KEY ("vehicleSizeId") REFERENCES "vehicle_size"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_be2f02be96355e6412f5efd7380"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_6138d71264f56b4ff2355bc455c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" DROP CONSTRAINT "FK_c2758f74fecacf8c04ad234fe14"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_be2f02be96355e6412f5efd738"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6138d71264f56b4ff2355bc455"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "vehicleSizeId"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "vehicleSizeId"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c2758f74fecacf8c04ad234fe1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d26f6b06bec69a341b98d924c4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1db0695b35006be185320812b9"`);
    await queryRunner.query(`DROP TABLE "vehicle_size"`);
  }
}
