import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1719502237620 implements MigrationInterface {
  name = 'Migrations1719502237620';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job_event" ADD "currentStatus" character varying NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job_event" DROP COLUMN "currentStatus"`);
  }
}
