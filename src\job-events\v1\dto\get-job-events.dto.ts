import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';
import { transformFriendlyCategory } from 'src/common/utility/transform-category.util';

export enum JobEventCategoryTypes {
  jobEventId = '"job_event"."id"',
  statusDescription = '"job_event"."statusDescription"',
  currentStatus = '"job_event"."currentStatus"',
  jobId = '"job_event"."jobId"',
}

export class JobEventSortItem extends SortItem {
  @IsEnum(JobEventCategoryTypes, { message: 'invalid sort field for JobEvent' })
  @Transform(({ value }) => transformFriendlyCategory(value, JobEventCategoryTypes))
  field: string;
}

export class JobEventExpression extends Expression {
  @IsOptional()
  @Transform(({ value }) => transformFriendlyCategory(value, JobEventCategoryTypes))
  @IsEnum(JobEventCategoryTypes, { message: 'invalid filter category for JobEvent' })
  category?: string | null | undefined;
}

export class GetJobEventsDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: JobEventSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JobEventSortItem)
  readonly sortModel: JobEventSortItem[] = [];

  @ApiProperty({ required: true, isArray: true, type: JobEventExpression })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JobEventExpression)
  readonly expressions: JobEventExpression[] = [];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
