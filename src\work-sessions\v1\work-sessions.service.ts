import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { WorkSession } from '../entities/work-session.entity';
import { WorkSessionStatus } from '../enums/work-session-status.enum';
import { StartWorkSessionDto } from './dto/start-work-session.dto';
import { EndWorkSessionDto } from './dto/end-work-session.dto';
import { StartBreakDto } from './dto/start-break.dto';
import { EndBreakDto } from './dto/end-break.dto';
import { GetWorkSessionsDto } from './dto/get-work-sessions.dto';

@Injectable()
export class WorkSessionsService {
  constructor(
    @InjectRepository(WorkSession)
    private workSessionRepository: Repository<WorkSession>,
  ) {}

  async startWorkSession(dto: StartWorkSessionDto): Promise<WorkSession> {
    const existingSession = await this.workSessionRepository.findOne({
      where: [
        {
          operatorId: dto.operatorId,
          status: WorkSessionStatus.ACTIVE,
        },
        {
          operatorId: dto.operatorId,
          status: WorkSessionStatus.ON_BREAK,
        },
      ],
    });

    if (existingSession) {
      throw new BadRequestException(
        'Operator already has an active work session',
      );
    }

    const session = this.workSessionRepository.create({
      operatorId: dto.operatorId,
      sessionStart: new Date(),
      status: WorkSessionStatus.ACTIVE,
      notes: dto.notes,
      breaks: [],
      jobIds: [],
    });

    return await this.workSessionRepository.save(session);
  }

  async startBreak(dto: StartBreakDto): Promise<WorkSession> {
    const session = await this.workSessionRepository.findOne({
      where: { id: dto.sessionId },
    });

    if (!session) {
      throw new NotFoundException('Work session not found');
    }

    if (session.status !== WorkSessionStatus.ACTIVE) {
      throw new BadRequestException('Work session is not active');
    }

    const hasOngoingBreak = session.breaks?.some(b => !b.breakEnd);
    if (hasOngoingBreak) {
      throw new BadRequestException('There is already an ongoing break');
    }

    const breaks = session.breaks || [];
    breaks.push({
      breakStart: new Date(),
      reason: dto.reason,
    });

    session.breaks = breaks;
    session.status = WorkSessionStatus.ON_BREAK;

    return await this.workSessionRepository.save(session);
  }

  async endBreak(dto: EndBreakDto): Promise<WorkSession> {
    const session = await this.workSessionRepository.findOne({
      where: { id: dto.sessionId },
    });

    if (!session) {
      throw new NotFoundException('Work session not found');
    }

    if (session.status !== WorkSessionStatus.ON_BREAK) {
      throw new BadRequestException('Work session is not on break');
    }

    const breaks = session.breaks || [];
    const ongoingBreak = breaks.find(b => !b.breakEnd);

    if (!ongoingBreak) {
      throw new BadRequestException('No ongoing break found');
    }

    ongoingBreak.breakEnd = new Date();
    session.breaks = breaks;
    session.status = WorkSessionStatus.ACTIVE;

    return await this.workSessionRepository.save(session);
  }

  async endWorkSession(dto: EndWorkSessionDto): Promise<WorkSession> {
    const session = await this.workSessionRepository.findOne({
      where: { id: dto.sessionId },
    });

    if (!session) {
      throw new NotFoundException('Work session not found');
    }

    if (session.status === WorkSessionStatus.ENDED) {
      throw new BadRequestException('Work session is already ended');
    }

    if (session.status === WorkSessionStatus.ON_BREAK) {
      const breaks = session.breaks || [];
      const ongoingBreak = breaks.find(b => !b.breakEnd);
      if (ongoingBreak) {
        ongoingBreak.breakEnd = new Date();
      }
    }

    const now = new Date();
    session.sessionEnd = now;
    session.status = WorkSessionStatus.ENDED;

    if (dto.notes) {
      session.notes = session.notes
        ? `${session.notes}\n${dto.notes}`
        : dto.notes;
    }

    const totalSessionTime =
      (now.getTime() - session.sessionStart.getTime()) / (1000 * 60); // minutes

    let totalBreakTime = 0;
    if (session.breaks && session.breaks.length > 0) {
      totalBreakTime = session.breaks.reduce((sum, breakItem) => {
        if (breakItem.breakEnd) {
          const duration =
            (new Date(breakItem.breakEnd).getTime() -
              new Date(breakItem.breakStart).getTime()) /
            (1000 * 60);
          return sum + duration;
        }
        return sum;
      }, 0);
    }

    session.totalBreakTime = Math.round(totalBreakTime);
    session.totalWorkTime = Math.round(totalSessionTime - totalBreakTime);

    return await this.workSessionRepository.save(session);
  }

  async getWorkSessions(dto: GetWorkSessionsDto) {
    const { operatorId, status, dateFrom, dateTo, page = 1, limit = 10 } = dto;

    const where: any = {};

    if (operatorId) {
      where.operatorId = operatorId;
    }

    if (status) {
      where.status = status;
    }

    if (dateFrom && dateTo) {
      where.sessionStart = Between(new Date(dateFrom), new Date(dateTo));
    } else if (dateFrom) {
      where.sessionStart = MoreThanOrEqual(new Date(dateFrom));
    } else if (dateTo) {
      where.sessionStart = LessThanOrEqual(new Date(dateTo));
    }

    const [data, total] = await this.workSessionRepository.findAndCount({
      where,
      relations: ['operator'],
      order: { sessionStart: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getActiveSession(operatorId: number): Promise<WorkSession | null> {
    return await this.workSessionRepository.findOne({
      where: [
        {
          operatorId,
          status: WorkSessionStatus.ACTIVE,
        },
        {
          operatorId,
          status: WorkSessionStatus.ON_BREAK,
        },
      ],
      relations: ['operator'],
    });
  }

  async addJobToSession(sessionId: number, jobId: number): Promise<WorkSession> {
    const session = await this.workSessionRepository.findOne({
      where: { id: sessionId },
    });

    if (!session) {
      throw new NotFoundException('Work session not found');
    }

    const jobIds = (session.jobIds || []).map(id => Number(id));
    const normalizedJobId = Number(jobId);

    if (!jobIds.includes(normalizedJobId)) {
      jobIds.push(normalizedJobId);
      session.jobIds = jobIds;
      return await this.workSessionRepository.save(session);
    }

    return session;
  }
}
