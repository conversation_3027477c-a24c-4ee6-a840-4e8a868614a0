import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1715698982035 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('reservation');

    if (table) {
      const columnExists = table.findColumnByName('orderNumber');

      if (!columnExists) {
        await queryRunner.query(`
                    ALTER TABLE "reservation" 
                    ADD "orderNumber" character varying(12) NOT NULL UNIQUE
                `);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('reservation');

    if (table) {
      const columnExists = table.findColumnByName('orderNumber');

      if (columnExists) {
        await queryRunner.query(`
                    ALTER TABLE "reservation" 
                    DROP CONSTRAINT IF EXISTS "UQ_reservation_orderNumber",
                    DROP COLUMN "orderNumber"
                `);
      }
    }
  }
}
