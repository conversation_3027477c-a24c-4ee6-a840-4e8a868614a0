const groupBy = `
GROUP BY
  "partner"."id"
`;

export const getPartnerQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT 
      "partner".*
      ${selects ? `, ${selects}` : ''}
    FROM "partner"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getPartnerCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "partner"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  operatorCompany: {
    join: `LEFT JOIN "company" AS "operatorCompany" ON "partner"."operatorCompanyId" = "operatorCompany"."id"`,
    select: `jsonb_build_object(
      'id', "operatorCompany"."id",
      'name', "operatorCompany"."name",
      'address', "operatorCompany"."address",
      'vatNumber', "operatorCompany"."vatNumber",
      'contactEmail', "operatorCompany"."contactEmail",
      'phoneNumber', "operatorCompany"."phoneNumber"
    ) AS "operatorCompany"`,
    groupBy: `"operatorCompany"."id"`,
  },
  dispatcherCompany: {
    join: `LEFT JOIN "company" AS "dispatcherCompany" ON "partner"."dispatcherCompanyId" = "dispatcherCompany"."id"`,
    select: `jsonb_build_object(
      'id', "dispatcherCompany"."id",
      'name', "dispatcherCompany"."name",
      'address', "dispatcherCompany"."address",
      'vatNumber', "dispatcherCompany"."vatNumber",
      'contactEmail', "dispatcherCompany"."contactEmail",
      'phoneNumber', "dispatcherCompany"."phoneNumber"
    ) AS "dispatcherCompany"`,
    groupBy: `"dispatcherCompany"."id"`,
  },
  containerPricelists: {
    join: ``,
    select: `COALESCE(
      (
        SELECT json_agg(
          jsonb_build_object(
            'id', "container"."id",
            'title', "container"."title",
            'isPublic', "container"."isPublic",
            'isDefault', "container"."isDefault",
            'pricelists', COALESCE(
              (
                SELECT json_agg(
                  to_jsonb(pl) || jsonb_build_object(
                    'vehicle', CASE
                      WHEN v."id" IS NOT NULL THEN jsonb_build_object(
                        'id', v."id",
                        'boomSize', v."boomSize",
                        'uniqueIdentificationNumber', v."uniqueIdentificationNumber"
                      )
                      ELSE NULL
                    END
                  )
                  ORDER BY v."boomSize" ASC
                )
                FROM "pricelist" pl
                LEFT JOIN "vehicle" v ON pl."vehicleId" = v."id"
                WHERE pl."containerId" = "container"."id"
              ), '[]'
            )
          )
        )
        FROM "partner_container_pricelists" AS "pcp"
        LEFT JOIN "container_pricelists" AS "container" ON "pcp"."containerPricelistsId" = "container"."id"
        WHERE "pcp"."partnerId" = "partner"."id"
      ), '[]'
    ) AS "containerPricelists"`,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
