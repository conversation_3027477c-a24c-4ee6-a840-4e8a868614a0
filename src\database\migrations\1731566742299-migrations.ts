import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1731566742299 implements MigrationInterface {
  name = 'Migrations1731566742299';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company_settings" DROP CONSTRAINT "FK_e00b096906dd073f5341bbd73c2"`,
    );
    await queryRunner.query(`ALTER TABLE "cancellation_settings" ADD "settingsId" integer`);
    await queryRunner.query(`
      UPDATE "cancellation_settings" cs
      SET "settingsId" = (
        SELECT s."id"
        FROM "company_settings" s
        WHERE s."cancellationId" = cs."id"
      )
      WHERE EXISTS (
        SELECT 1
        FROM "company_settings" s
        WHERE s."cancellationId" = cs."id"
      );
    `);
    await queryRunner.query(`
      UPDATE "company_settings"
      SET "cancellationId" = NULL
      WHERE "cancellationId" IS NOT NULL
      AND NOT EXISTS (
        SELECT 1 FROM "cancellation_settings" WHERE "id" = "company_settings"."cancellationId"
      );
    `);
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" ALTER COLUMN "settingsId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" ADD CONSTRAINT "UQ_dbff45b84db5bb034b9e5ba2712" UNIQUE ("settingsId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dbff45b84db5bb034b9e5ba271" ON "cancellation_settings" ("settingsId")`,
    );
    await queryRunner.query(`
      ALTER TABLE "cancellation_settings"
      ADD CONSTRAINT "FK_dbff45b84db5bb034b9e5ba2712"
      FOREIGN KEY ("settingsId") REFERENCES "company_settings"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
    `);
    await queryRunner.query(`
      ALTER TABLE "company_settings"
      ADD CONSTRAINT "FK_e00b096906dd073f5341bbd73c2"
      FOREIGN KEY ("cancellationId") REFERENCES "cancellation_settings"("id") ON DELETE SET NULL ON UPDATE NO ACTION;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company_settings" DROP CONSTRAINT "FK_e00b096906dd073f5341bbd73c2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" DROP CONSTRAINT "FK_dbff45b84db5bb034b9e5ba2712"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_dbff45b84db5bb034b9e5ba271"`);
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" DROP CONSTRAINT "UQ_dbff45b84db5bb034b9e5ba2712"`,
    );
    await queryRunner.query(`ALTER TABLE "cancellation_settings" DROP COLUMN "settingsId"`);
    await queryRunner.query(`
      ALTER TABLE "company_settings"
      ADD CONSTRAINT "FK_e00b096906dd073f5341bbd73c2"
      FOREIGN KEY ("cancellationId") REFERENCES "cancellation_settings"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
    `);
  }
}
