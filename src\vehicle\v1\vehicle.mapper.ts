import { Reservation } from 'src/reservation/entities/reservation.entity';
import { VehicleReservationDetails } from './interfaces/get-vehicles-reservations.interface';

export class VehicleMapper {
  static mapReservationToVehicleReservationResponse(
    reservation: Reservation,
    dispatcherId: number,
    userCompanyId: number,
  ): VehicleReservationDetails {
    const vehicleReservation: VehicleReservationDetails = {
      id: reservation.id,
      dispatcherId: reservation.dispatcher ? reservation.dispatcher.id : null,
      dateFrom: reservation.dateFrom,
      dateTo: reservation.dateTo,
      operatorName: reservation.operator?.firstName || '',
      siteAddress: reservation.siteAddress,
      amount: reservation.job?.amountOfConcrete || null,
      flowRate: reservation.job?.flowRate || null,
      jobStatus: reservation.job?.status || null,
      hasBeenRead: reservation.hasBeenRead,
      city: reservation.city,
      pricelistId: reservation.pricelistId,
      clientDetails: reservation.clientDetails,
      ownReservation: reservation.dispatcher
        ? reservation.dispatcher.id === dispatcherId ||
          reservation.dispatcher.company.id === userCompanyId ||
          reservation.operator.companyId === userCompanyId
        : reservation.operator.companyId === userCompanyId ?? false,
    };
    return vehicleReservation;
  }
}
