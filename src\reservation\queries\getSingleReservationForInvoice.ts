import { JobStatus } from 'src/common/constants/job.enum';

export const getSingleReservationForInvoiceQuery = (
  reservationId: number,
  checkInvoiced: boolean = true,
) => {
  const invoicedCondition = checkInvoiced ? `AND "reservation"."invoiced" = FALSE` : '';

  return `
  SELECT 
    "reservation"."id",
    "reservation"."dateFrom",
    "reservation"."dispatcherId",
    "reservation"."jobId",
    "reservation"."vehicleId",
    ("reservation"."clientDetails")::jsonb,
    ("reservation"."location")::jsonb,
    "vehicle"."operatorId",
    to_jsonb(pricelist) AS pricelist,
    to_jsonb("job") || jsonb_build_object('report', ("job"."report")::jsonb) AS job
  FROM reservation AS reservation
  LEFT JOIN "job" AS "job" ON "reservation"."jobId" = "job"."id"
  LEFT JOIN "vehicle" AS "vehicle" ON "reservation"."vehicleId" = "vehicle"."id"
  LEFT JOIN "pricelist" AS "pricelist" ON "reservation"."pricelistId" = "pricelist"."id"
  WHERE "reservation"."id" = ${reservationId} 
    AND "job"."status" = '${JobStatus.COMPLETE}'
    ${invoicedCondition}
    AND "pricelist"."isContract" = FALSE;
`;
};
