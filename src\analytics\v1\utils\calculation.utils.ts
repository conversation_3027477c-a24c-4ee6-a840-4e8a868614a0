import { InjectRepository } from '@nestjs/typeorm';
import { Reservation } from 'src/reservation/entities/reservation.entity';
import { Repository } from 'typeorm';

export class CalculationUtils {
  constructor(
    @InjectRepository(Reservation)
    private reservationRepository: Repository<Reservation>,
  ) {}

  async calculateAverageCompletionTime(
    startDate: string,
    endDate: string,
    dispatcherCompanyId?: number,
    operatorCompanyId?: number,
  ): Promise<number | null> {
    const queryBuilder = this.reservationRepository
      .createQueryBuilder('reservation')
      .innerJoin('reservation.job', 'job');

    if (dispatcherCompanyId) {
      queryBuilder
        .innerJoin('reservation.dispatcher', 'dispatcher')
        .innerJoin('dispatcher.company', 'company')
        .where('company.id = :dispatcherCompanyId', { dispatcherCompanyId })
        .andWhere('dispatcher.deleted_at IS NULL')
        .andWhere('company.deleted_at IS NULL');
    } else if (operatorCompanyId) {
      queryBuilder
        .innerJoin('reservation.operator', 'operator')
        .innerJoin('operator.company', 'company')
        .where('company.id = :operatorCompanyId', { operatorCompanyId })
        .andWhere('operator.deleted_at IS NULL')
        .andWhere('company.deleted_at IS NULL');
    }

    const data = await queryBuilder
      .select(['job.start', 'job.end'])
      .andWhere('job.start BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('job.start IS NOT NULL')
      .andWhere('job.end IS NOT NULL')
      .andWhere('reservation.deleted_at IS NULL')
      .getRawMany();

    if (data.length === 0) {
      return null;
    }

    const completionTimesInHours = data.map(item => {
      const startTime = new Date(item.job_start);
      const endTime = new Date(item.job_end);
      const durationInHours = (endTime.getTime() - startTime.getTime()) / 3600000; // Convert milliseconds to hours
      return durationInHours > 0 ? durationInHours : 0;
    });

    const totalCompletionTime = completionTimesInHours.reduce((sum, value) => sum + value, 0);
    const averageCompletionTimeInHours = parseFloat(
      (totalCompletionTime / completionTimesInHours.length).toFixed(2),
    );

    return averageCompletionTimeInHours;
  }

  async calculateAverageM3PerJob(
    startDate: string,
    endDate: string,
    dispatcherCompanyId?: number,
    operatorCompanyId?: number,
  ): Promise<number | null> {
    const queryBuilder = this.reservationRepository
      .createQueryBuilder('reservation')
      .innerJoin('reservation.job', 'job');

    if (dispatcherCompanyId) {
      queryBuilder
        .innerJoin('reservation.dispatcher', 'dispatcher')
        .innerJoin('dispatcher.company', 'company')
        .where('company.id = :dispatcherCompanyId', { dispatcherCompanyId })
        .andWhere('reservation.dispatcherId IS NOT NULL')
        .andWhere('dispatcher.deleted_at IS NULL')
        .andWhere('company.deleted_at IS NULL');
    } else if (operatorCompanyId) {
      queryBuilder
        .innerJoin('reservation.operator', 'operator')
        .innerJoin('operator.company', 'company')
        .where('company.id = :operatorCompanyId', { operatorCompanyId })
        .andWhere('reservation.operatorId IS NOT NULL')
        .andWhere('operator.deleted_at IS NULL')
        .andWhere('company.deleted_at IS NULL');
    }

    const result = await queryBuilder
      .select('SUM(job.amountOfConcrete)', 'totalVolume')
      .addSelect('COUNT(job.id)', 'jobCount')
      .andWhere('reservation.dateFrom BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('reservation.dateFrom IS NOT NULL')
      .andWhere('reservation.dateTo IS NOT NULL')
      .andWhere('company.id IS NOT NULL')
      .andWhere('job.start BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('job.amountOfConcrete IS NOT NULL')
      .andWhere('reservation.deleted_at IS NULL')
      .getRawOne();

    const totalVolumeNumber = parseFloat(result.totalVolume) || 0;
    const jobCountNumber = parseInt(result.jobCount, 10) || 0;

    if (jobCountNumber === 0) {
      return null;
    }

    const averageM3PerJob = parseFloat((totalVolumeNumber / jobCountNumber).toFixed(2));
    return averageM3PerJob;
  }

  async calculateAverageM3PerCompany(
    dispatcherCompanyId: number,
    operatorCompanyId: number,
    startDate: string,
    endDate: string,
  ): Promise<number | null> {
    const data = await this.reservationRepository
      .createQueryBuilder('reservation')
      .innerJoin('reservation.job', 'job')
      .innerJoin('reservation.operator', 'operator')
      .innerJoin('reservation.dispatcher', 'dispatcher')
      .innerJoin('dispatcher.company', 'dispatcherCompany')
      .innerJoin('operator.company', 'company')
      .select(['job.amountOfConcrete', 'job.start', 'job.end'])
      .where('job.start BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('dispatcherCompany.id = :dispatcherCompanyId', { dispatcherCompanyId })
      .andWhere('company.id = :operatorCompanyId', { operatorCompanyId })
      .andWhere('job.amountOfConcrete IS NOT NULL')
      .andWhere('job.start IS NOT NULL')
      .andWhere('job.end IS NOT NULL')
      .getRawMany();

    if (data.length === 0) {
      return null;
    }

    const m3PerHourArray = data.map(item => {
      const amountOfConcrete = parseFloat(item.job_amountOfConcrete);
      const start = new Date(item.job_start);
      const end = new Date(item.job_end);
      return this.calculateM3PerHour(amountOfConcrete, start, end);
    });

    const totalM3PerHour = m3PerHourArray.reduce((sum, value) => sum + value, 0);
    const averageM3PerCompany = parseFloat((totalM3PerHour / m3PerHourArray.length).toFixed(2));

    return averageM3PerCompany;
  }

  async calculateAverageM3PerHour(
    startDate: string,
    endDate: string,
    dispatcherCompanyId?: number,
    operatorCompanyId?: number,
  ): Promise<number | null> {
    const queryBuilder = this.reservationRepository
      .createQueryBuilder('reservation')
      .innerJoin('reservation.job', 'job');

    if (dispatcherCompanyId) {
      queryBuilder
        .innerJoin('reservation.dispatcher', 'dispatcher')
        .innerJoin('dispatcher.company', 'company')
        .where('company.id = :dispatcherCompanyId', { dispatcherCompanyId })
        .andWhere('dispatcher.deleted_at IS NULL')
        .andWhere('company.deleted_at IS NULL');
    } else if (operatorCompanyId) {
      queryBuilder
        .innerJoin('reservation.operator', 'operator')
        .innerJoin('operator.company', 'company')
        .where('company.id = :operatorCompanyId', { operatorCompanyId })
        .andWhere('operator.deleted_at IS NULL')
        .andWhere('company.deleted_at IS NULL');
    }

    const data = await queryBuilder
      .select(['job.amountOfConcrete', 'job.start', 'job.end'])
      .andWhere('job.start BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('job.amountOfConcrete IS NOT NULL')
      .andWhere('job.start IS NOT NULL')
      .andWhere('job.end IS NOT NULL')
      .andWhere('reservation.deleted_at IS NULL')
      .getRawMany();

    if (data.length === 0) {
      return null;
    }

    const m3PerHourArray = data.map(item => {
      const amountOfConcrete = parseFloat(item.job_amountOfConcrete);
      const start = new Date(item.job_start);
      const end = new Date(item.job_end);
      return this.calculateM3PerHour(amountOfConcrete, start, end);
    });

    const totalM3PerHour = m3PerHourArray.reduce((sum, value) => sum + value, 0);
    const averageM3PerHour = parseFloat((totalM3PerHour / m3PerHourArray.length).toFixed(2));

    return averageM3PerHour;
  }

  calculateM3PerHour(amountOfConcrete: number, startTime: Date, endTime: Date): number {
    const durationInHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
    if (durationInHours <= 0) {
      return 0;
    }
    return amountOfConcrete / durationInHours;
  }

  getMonthsBetweenDates(startDateStr: string, endDateStr: string): string[] {
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);
    const months = [];
    startDate.setUTCDate(1);
    endDate.setUTCDate(1);

    while (startDate <= endDate) {
      const year = startDate.getUTCFullYear();
      const month = (startDate.getUTCMonth() + 1).toString().padStart(2, '0');
      months.push(`${year}-${month}`);

      // Move to the next month
      startDate.setUTCMonth(startDate.getUTCMonth() + 1);
    }

    return months;
  }
}
