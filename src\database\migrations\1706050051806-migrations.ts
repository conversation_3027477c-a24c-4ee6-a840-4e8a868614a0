import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706050051806 implements MigrationInterface {
  name = 'Migrations1706050051806';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "isManager" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `CREATE INDEX "IDX_9e3cb0ae2b24c39a55e84eed69" ON "user" ("isManager") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_9e3cb0ae2b24c39a55e84eed69"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "isManager"`);
  }
}
