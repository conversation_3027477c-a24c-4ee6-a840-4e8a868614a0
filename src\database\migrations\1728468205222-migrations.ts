import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1728468205222 implements MigrationInterface {
  name = 'Migrations1728468205222';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "dayContractFee" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "dayContractDuration" integer`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "dayContractOvertimeRate" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "packageFlatFeeWeekend" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "additionalHourWeekend" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "packageFlatFeeNight" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "additionalHourNight" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "minimumChargeFlatFee" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "minimumM3Charged" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "dayCancellationFee" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "cancellationFee" double precision`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "secondTechnicianFeeWeekend" double precision`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "secondTechnicianFeeWeekend"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "cancellationFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "dayCancellationFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "minimumM3Charged"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "minimumChargeFlatFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "additionalHourNight"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "packageFlatFeeNight"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "additionalHourWeekend"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "packageFlatFeeWeekend"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "dayContractOvertimeRate"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "dayContractDuration"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "dayContractFee"`);
  }
}
