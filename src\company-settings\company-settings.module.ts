import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompanyModule } from 'src/company/company.module';
import { CancellationSettings } from './entities/cancellation-settings.entity';
import { CompanySettings } from './entities/company-settings.entity';
import { WorkScheduleSettings } from './entities/work-schedule-settings.entity';
import { CompanyHoliday } from './entities/company-holiday.entity';
import { CompanySettingsController } from './v1/company-settings.controller';
import { CompanySettingsService } from './v1/company-settings.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([CompanySettings, WorkScheduleSettings, CancellationSettings, CompanyHoliday]),
    forwardRef(() => CompanyModule),
  ],
  controllers: [CompanySettingsController],
  providers: [CompanySettingsService],
  exports: [CompanySettingsService],
})
export class CompanySettingsModule {}
