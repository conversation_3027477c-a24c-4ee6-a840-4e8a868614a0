import { Company } from 'src/company/entities/company.entity';

export function generateInvoiceNumber(dispatcherCompany: Partial<Company>): string {
  const companyName = dispatcherCompany.name
    ? dispatcherCompany.name.toUpperCase().substring(0, 3)
    : 'unknown';

  const countryCode = dispatcherCompany.country
    ? dispatcherCompany.country.toUpperCase()
    : 'unknown';
  const currentYear = new Date().getFullYear();
  const timestamp = Date.now().toString().slice(0, -4);

  return `${companyName}-${countryCode}-${currentYear}-${timestamp}`;
}
