import { BaseEntity } from '../../common/entities/base.entity';
import {
  Entity,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Company } from 'src/company/entities/company.entity';
import { PartnerStatus } from 'src/common/constants/user.enum';
import { ContainerPricelists } from 'src/pricelist/entities/container-pricelists.entity';

@Entity()
@Unique(['dispatcherCompanyId', 'operatorCompanyId'])
export class Partner extends BaseEntity {
  @Column()
  @Index()
  dispatcherCompanyId: number;

  @Column()
  @Index()
  operatorCompanyId: number;

  @ManyToOne(() => Company, company => company.dispatcherPartners, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'dispatcherCompanyId' })
  dispatcherCompany: Company;

  @ManyToOne(() => Company, company => company.operatorPartners, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'operatorCompanyId' })
  operatorCompany: Company;

  @ManyToMany(() => ContainerPricelists, container => container.partners, {
    cascade: false,
  })
  @JoinTable({
    name: 'partner_container_pricelists',
    joinColumn: {
      name: 'partnerId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'containerPricelistsId',
      referencedColumnName: 'id',
    },
  })
  containerPricelists: ContainerPricelists[];

  @Column({
    type: 'enum',
    enum: PartnerStatus,
    default: PartnerStatus.ACTIVE,
  })
  status: PartnerStatus;
}
