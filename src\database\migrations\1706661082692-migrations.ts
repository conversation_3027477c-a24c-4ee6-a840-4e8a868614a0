import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706661082692 implements MigrationInterface {
  name = 'Migrations1706661082692';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "role" SET DEFAULT '5'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "role" SET DEFAULT '1'`);
  }
}
