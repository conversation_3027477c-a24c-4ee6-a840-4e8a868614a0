import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1709680281831 implements MigrationInterface {
  name = 'Migrations1709680281831';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "packageFlatFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "packageFlatFee" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterPumped"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "pricePerMeterPumped" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier1"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier1" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier2"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier2" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier3"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier3" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier4"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier4" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier5"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier5" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier6"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier6" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "additionalHour"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "additionalHour" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "cleaningFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "cleaningFee" double precision`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterOfFlexiblePipeLength80Mm"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "pricePerMeterOfFlexiblePipeLength80Mm" double precision`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterOfFlexiblePipeLength90Mm"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "pricePerMeterOfFlexiblePipeLength90Mm" double precision`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterOfFlexiblePipeLength100Mm"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "pricePerMeterOfFlexiblePipeLength100Mm" double precision`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterOfRigidPipeLength120Mm"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "pricePerMeterOfRigidPipeLength120Mm" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "supplyOfTheChemicalSlushie"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "supplyOfTheChemicalSlushie" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "barbotine"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "barbotine" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "extraCementBagPrice"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "extraCementBagPrice" double precision`);
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "amountOfConcrete" TYPE double precision`,
    );
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "flexiblePipeLength80Mm"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "flexiblePipeLength80Mm" double precision`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "flexiblePipeLength90Mm"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "flexiblePipeLength90Mm" double precision`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "frontOutriggersSpan"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "frontOutriggersSpan" double precision`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "rearOutriggersSpan"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "rearOutriggersSpan" double precision`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "rigidPipeLength"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "rigidPipeLength" double precision`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "voltage"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "voltage" double precision`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "authorizedWeight"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "authorizedWeight" double precision`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "heightLimit"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "heightLimit" double precision`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "heightLimit"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "heightLimit" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "authorizedWeight"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "authorizedWeight" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "voltage"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "voltage" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "rigidPipeLength"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "rigidPipeLength" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "rearOutriggersSpan"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "rearOutriggersSpan" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "frontOutriggersSpan"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "frontOutriggersSpan" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "flexiblePipeLength90Mm"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "flexiblePipeLength90Mm" integer`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "flexiblePipeLength80Mm"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "flexiblePipeLength80Mm" integer`);
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "amountOfConcrete" TYPE integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "extraCementBagPrice"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "extraCementBagPrice" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "barbotine"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "barbotine" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "supplyOfTheChemicalSlushie"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "supplyOfTheChemicalSlushie" integer`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterOfRigidPipeLength120Mm"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "pricePerMeterOfRigidPipeLength120Mm" integer`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterOfFlexiblePipeLength100Mm"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "pricePerMeterOfFlexiblePipeLength100Mm" integer`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterOfFlexiblePipeLength90Mm"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "pricePerMeterOfFlexiblePipeLength90Mm" integer`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterOfFlexiblePipeLength80Mm"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "pricePerMeterOfFlexiblePipeLength80Mm" integer`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "cleaningFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "cleaningFee" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "additionalHour"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "additionalHour" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier6"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier6" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier5"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier5" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier4"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier4" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier3"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier3" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier2"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier2" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier1"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier1" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "pricePerMeterPumped"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "pricePerMeterPumped" integer`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "packageFlatFee"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "packageFlatFee" integer`);
  }
}
