import { CompanyCategory } from 'src/common/constants/company.enum';
import { Role } from 'src/common/constants/roles.enum';

export class CompanyUtils {
  static getCompanyCategoryByRole = (roleId: Role): CompanyCategory | null => {
    switch (roleId) {
      case Role.OPERATOR_MANAGER:
        return CompanyCategory.OPERATOR;
      case Role.DISPATCHER_MANAGER:
        return CompanyCategory.DISPATCHER;
      default:
        return null;
    }
  };
}
