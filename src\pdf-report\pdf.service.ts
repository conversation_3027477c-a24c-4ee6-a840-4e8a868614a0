import { Injectable, OnModuleInit } from '@nestjs/common';
import * as pdfMake from 'pdfmake/build/pdfmake';
import * as pdfFonts from 'pdfmake/build/vfs_fonts';
import { FilesService } from 'src/s3/files.service';
import { GenerateJobReport } from './interface/generate-job-report.interface';
import {
  GenerateBulkReservationReport,
  GenerateSingleReservationReport,
} from './interface/generate-reservation-report.interface';
import { Cleaning } from 'src/common/constants/job.enum';
import { VehicleUtils } from 'src/vehicle/helpers/vehicle.utils';
import { JobEventEnum } from 'src/job-events/enums/job-event.enum';
import { formatNumber } from 'src/common/helpers/number-processing';
import { CancellationType } from 'src/common/constants/cancellation.enum';
pdfMake.vfs = pdfFonts.pdfMake.vfs;

@Injectable()
export class PdfService implements OnModuleInit {
  private logoBase64: string;
  private formattedDate: string;
  checkmark: string =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAdNJREFUaEPtV6tOA1EQPacYECgSJI6XIPADhA8AicEhMTheCY8EBJIERQJUQTUOxZsvwPEbCAQJmaE32U1KSdmdvftqcms7Z+Y8ZrZdos8/7HP+CAKqTjAkEBLwdCCskKeB3vCQgLeFng1CAp4GesNDAt4W/m0w02g0VkRkF4Am9a9bAlMkH0m2RGQjibz7vk4CzOTrJCAmfy0im2mcj2vqkIAj/xCtjYl8HRLwIl+1gMnoYN3abFnWprO2qhXKhXxVCeRGvgoBuZK3CBgEMADgM+uuApiOnjZNEdnz6PMLmvYGDkjOq+oSgK8Mw+Pn/KWI7GfA94SkFTBE8hbAh6ouA/g2kJjoeNpsG3CpStMKcM2G2ynck3wXkVUAkmJCTP5KRHZS1JtLLAJc8xGSz85REVlPmFY4ecsRd3IdJfmqqi0ARz1ElEI+qwCHG4tEnAI46RJRGnkfAQ7riL6oqnvxaEYixkk+qep5+6/6oXmhMwCsN9A9Yo7knaquAXhzt6GqF2WR900gFrNA8sb9Pqjq2T93kcHfZIhvAvGERQCzAI6TR+ZbkZeAfFkZugUBBrMKKQ0JFGKroWlIwGBWIaUhgUJsNTQNCRjMKqQ0JFCIrYamPzPRkTH9lvxuAAAAAElFTkSuQmCC';

  constructor(private readonly filesService: FilesService) {
    this.formatDate();
  }

  private formatDate(dateToFormat?: Date, showTime: boolean = true): string {
    const date = dateToFormat || new Date();

    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      ...(showTime && {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true, // Use 12-hour format with AM/PM
      }),
    };

    const formattedDate = date.toLocaleString('en-GB', options);

    // If no date is provided, set this.formattedDate to the current date
    if (!dateToFormat) {
      this.formattedDate = formattedDate;
    }

    // Return the formatted date string in both cases
    return formattedDate;
  }

  async onModuleInit() {
    const bucketName = process.env.S3_ASSETS_BUCKET_NAME ?? 'concreteasy-assets';
    const key = process.env.S3_LOGO ?? 'ConcretEasy.png';
    // this.logoBase64 = await this.filesService.getImageBase64(bucketName, key);
        this.logoBase64 =
      'data:image/png;base64,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';
  }

  async generateJobReport(jobReport: GenerateJobReport): Promise<Buffer> {
    const { job, vehicle, reservation, company, jobEventTimes } = jobReport;

    const completeEvents = job.jobEvents.filter(
      event => event.currentStatus === JobEventEnum.COMPLETE,
    );
    const sortedCompleteEvents = completeEvents.sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    );

    const endDate: Date =
      sortedCompleteEvents.length > 0 ? new Date(sortedCompleteEvents[0].created_at) : new Date();

    const addRow = (description, planned, actual) => {
      return [
        { text: description, margin: [10, 5, 10, 5] },
        { text: planned, alignment: 'center', margin: [10, 5, 10, 5] },
        { text: actual, alignment: 'center', margin: [10, 5, 10, 5] },
      ];
    };

    const packageFlatFeeRows = [];
    if (
      job.amountOfConcrete < reservation.pricelist.minimumM3Charged &&
      reservation.pricelist.minimumChargeFlatFee
    ) {
      const estimateBaseFeeDuration = Math.min(
        job.estimateHourDuration,
        reservation.pricelist.packageFlatFeeDuration,
      );
      const baseFeeDuration = Math.min(
        job.hourDuration,
        reservation.pricelist.packageFlatFeeDuration,
      );
      packageFlatFeeRows.push(
        addRow(
          'Package fee (Minimum)',
          (estimateBaseFeeDuration / reservation.pricelist.packageFlatFeeDuration) *
            reservation.pricelist.minimumChargeFlatFee,
          job.report.amountOfConcrete < reservation.pricelist.minimumM3Charged
            ? (baseFeeDuration / reservation.pricelist.packageFlatFeeDuration) *
                reservation.pricelist.minimumChargeFlatFee
            : '-',
        ),
      );
    } else {
      const packageFlatFee = reservation.pricelist.packageFlatFee || 0;
      if (
        (reservation.pricelist.packageFlatFee && job.baseHourBreakdown?.work) ||
        job.estimateBaseHourBreakdown?.work
      ) {
        packageFlatFeeRows.push(
          addRow(
            'Package fee',
            formatNumber(
              (job.estimateBaseHourBreakdown.work / reservation.pricelist.packageFlatFeeDuration) *
                packageFlatFee,
            ),
            formatNumber(
              (job.baseHourBreakdown.work / reservation.pricelist.packageFlatFeeDuration) *
                packageFlatFee,
            ),
          ),
        );
      }
      if (job.baseHourBreakdown?.night || job.estimateBaseHourBreakdown?.night) {
        packageFlatFeeRows.push(
          addRow(
            'Package fee (Night)',
            formatNumber(
              (job.estimateBaseHourBreakdown.night / reservation.pricelist.packageFlatFeeDuration) *
                reservation.pricelist.packageFlatFeeNight ?? packageFlatFee,
            ),
            formatNumber(
              (job.baseHourBreakdown.night / reservation.pricelist.packageFlatFeeDuration) *
                reservation.pricelist.packageFlatFeeNight ?? packageFlatFee,
            ),
          ),
        );
      }
      if (job.baseHourBreakdown?.weekend || job.estimateBaseHourBreakdown?.weekend) {
        packageFlatFeeRows.push(
          addRow(
            'Package fee (Weekend)',
            formatNumber(
              (job.estimateBaseHourBreakdown.weekend /
                reservation.pricelist.packageFlatFeeDuration) *
                reservation.pricelist.packageFlatFeeWeekend ?? packageFlatFee,
            ),
            formatNumber(
              (job.baseHourBreakdown.weekend / reservation.pricelist.packageFlatFeeDuration) *
                reservation.pricelist.packageFlatFeeWeekend ?? packageFlatFee,
            ),
          ),
        );
      }
    }
    const additionalHours = Math.max(
      job.hourDuration - reservation.pricelist.packageFlatFeeDuration,
      0,
    );
    const additionalHourRows = [];
    if (additionalHours > 0) {
      if (reservation.pricelist.additionalHour && job.additionalHourBreakdown?.work) {
        additionalHourRows.push(
          addRow(
            'Additional hour',
            formatNumber(
              job.estimateAdditionalHourBreakdown.work * reservation.pricelist.additionalHour,
            ),
            formatNumber(job.additionalHourBreakdown.work * reservation.pricelist.additionalHour),
          ),
        );
      }
      if (job.additionalHourBreakdown?.night) {
        additionalHourRows.push(
          addRow(
            'Additional hour (Night)',
            formatNumber(
              job.estimateAdditionalHourBreakdown.night *
                reservation.pricelist.additionalHourNight ??
                (reservation.pricelist.additionalHour || 0),
            ),
            formatNumber(
              job.additionalHourBreakdown.night * reservation.pricelist.additionalHourNight ??
                (reservation.pricelist.additionalHour || 0),
            ),
          ),
        );
      }
      if (job.additionalHourBreakdown?.weekend) {
        additionalHourRows.push(
          addRow(
            'Additional hour (Weekend)',
            formatNumber(
              job.estimateAdditionalHourBreakdown.weekend *
                reservation.pricelist.additionalHourWeekend ??
                (reservation.pricelist.additionalHour || 0),
            ),
            formatNumber(
              job.additionalHourBreakdown.weekend * reservation.pricelist.additionalHourWeekend ??
                (reservation.pricelist.additionalHour || 0),
            ),
          ),
        );
      }
    }
    const secondTechnicianRows = [];
    if (job.enlistSecondTechnician && job.report.secondTechnicianMinutes) {
      if (
        reservation.pricelist.secondTechnicianHourFee &&
        job.secondTechnicianHourBreakdown?.work
      ) {
        secondTechnicianRows.push(
          addRow(
            '2nd technician fee',
            formatNumber(
              job.estimateBaseHourBreakdown.work * reservation.pricelist.secondTechnicianHourFee,
            ),
            formatNumber(
              job.secondTechnicianHourBreakdown.work *
                reservation.pricelist.secondTechnicianHourFee,
            ),
          ),
        );
      }
      if (job.secondTechnicianHourBreakdown?.night) {
        secondTechnicianRows.push(
          addRow(
            '2nd technician fee (Night)',
            formatNumber(
              job.estimateBaseHourBreakdown.night *
                reservation.pricelist.secondTechnicianHourFeeNight ??
                (reservation.pricelist.secondTechnicianHourFee || 0),
            ),
            formatNumber(
              job.secondTechnicianHourBreakdown.night *
                reservation.pricelist.secondTechnicianHourFeeNight ??
                (reservation.pricelist.secondTechnicianHourFee || 0),
            ),
          ),
        );
      }
      if (job.secondTechnicianHourBreakdown?.weekend) {
        secondTechnicianRows.push(
          addRow(
            '2nd technician fee (Weekend)',
            formatNumber(
              job.estimateBaseHourBreakdown.weekend *
                reservation.pricelist.secondTechnicianHourFeeWeekend ??
                (reservation.pricelist.secondTechnicianHourFee || 0),
            ),
            formatNumber(
              job.secondTechnicianHourBreakdown.weekend *
                reservation.pricelist.secondTechnicianHourFeeWeekend ??
                (reservation.pricelist.secondTechnicianHourFee || 0),
            ),
          ),
        );
      }
    }
    const jobDetailsTableBody = [
      [
        { text: 'Description', fillColor: '#F4F4F4', margin: [10, 5, 10, 5] },
        { text: 'Planned', fillColor: '#F4F4F4', alignment: 'center', margin: [10, 5, 10, 5] },
        { text: 'Actual', fillColor: '#F4F4F4', alignment: 'center', margin: [10, 5, 10, 5] },
      ],
      ...packageFlatFeeRows,
      addRow(
        'Pipe 80 mm (Flexible)',
        job.flexiblePipeLength80Mm ? job.flexiblePipeLength80Mm : '-',
        job.report.flexiblePipeLength80Mm ? job.report.flexiblePipeLength80Mm : '-',
      ),
      addRow(
        'Pipe 90 mm (Flexible)',
        job.flexiblePipeLength90Mm ? job.flexiblePipeLength90Mm : '-',
        job.report.flexiblePipeLength90Mm ? job.report.flexiblePipeLength90Mm : '-',
      ),
      addRow(
        'Pipe 100 mm (Rigid)',
        job.rigidPipeLength100Mm ? job.rigidPipeLength100Mm : '-',
        job.report.rigidPipeLength100Mm ? job.report.rigidPipeLength100Mm : '-',
      ),
      addRow(
        'Pipe 120 mm (Rigid)',
        job.rigidPipeLength120Mm ? job.rigidPipeLength120Mm : '-',
        job.report.rigidPipeLength120Mm ? job.report.rigidPipeLength120Mm : '-',
      ),
      ...additionalHourRows,
      ...secondTechnicianRows,
      addRow(
        'Barbotine',
        job.barbotine ? '1' : '-',
        reservation.pricelist.barbotine ? reservation.pricelist.barbotine : '-',
      ),
      addRow(
        'Chemical Slushie',
        job.supplyOfTheChemicalSlushieCost ? '1' : '-',
        job.supplyOfTheChemicalSlushieCost ? reservation.pricelist.supplyOfTheChemicalSlushie : '-',
      ),
      addRow(
        'Extra cement bag',
        job.extraCementBag ? job.units : '-',
        job.report.extraCementBags ? job.report.cementBags : '-',
      ),
      addRow(
        'Cleaning fee',
        job.cleaning === 'Central' ? reservation.pricelist.cleaningFee : '-',
        job.report.cleaningTime ? reservation.pricelist.cleaningFee : '-',
      ),
      addRow(
        'Transport Rate',
        job.chargedTransportRate ? job.chargedTransportRate.tariff : '-',
        job.chargedTransportRate ? job.chargedTransportRate.tariff : '-',
      ),
    ];

    const documentDefinition = {
      pageSize: 'A4',
      defaultStyle: {
        fontSize: 8,
      },
      pageMargins: [50, 210, 50, 60],
      header: () => {
        return {
          stack: [
            {
              columns: [
                {
                  stack: [
                    { text: `${company.name}`, marginBottom: 2 },
                    { text: `${company.address || ''}`, marginBottom: 2 },
                    { text: `${company.vatNumber || ''}`, marginBottom: 2 },
                    { text: `${company.phoneNumber || ''}`, marginBottom: 2 },
                    { text: `${company.billingEmail}` },
                  ],
                  width: '*',
                },
                {
                  image: this.logoBase64,
                  width: 200,
                  height: 80,
                  alignment: 'right',
                  margin: [0, 0, 0, 0],
                },
              ],
              margin: [50, 50, 50, 10],
            },
            {
              columns: [
                {
                  stack: [
                    {
                      text: `Order #${reservation.orderNumber}`,
                      fontSize: 12,
                      bold: true,
                      margin: [0, -20, 10, 0],
                    },
                    {
                      text: this.formattedDate,
                      style: 'defaultText',
                      margin: [0, 5, 0, 0],
                    },
                  ],
                  width: 'auto',
                },
                {
                  stack: [
                    {
                      canvas: [
                        {
                          type: 'rect',
                          x: 0,
                          y: 0,
                          w: 60,
                          h: 15,
                          r: 7,
                          color: '#e8faea',
                        },
                      ],
                      margin: [0, -20, 0, 0],
                    },
                    {
                      text: 'Completed',
                      color: '#629442',
                      fontSize: 10,
                      bold: true,
                      alignment: 'left',
                      margin: [5, -15, 0, 0],
                    },
                  ],
                  width: 'auto',
                },
              ],
              margin: [50, 10, 50, 30],
            },
          ],
        };
      },
      content: [
        {
          text: 'Order information',
          bold: true,
          fontSize: 12,
          border: [false, false, false, true],
          margin: [0, -20, 0, 10],
        },
        {
          columns: [
            {
              stack: [
                { text: 'Order time', style: 'defaultText', opacity: 0.4, marginBottom: 5 },
                { text: job.start ? this.formatDate(job.start, true) : '-', style: 'defaultText' },
              ],
              width: 'auto',
            },
            {
              stack: [
                { text: 'Invoiced time', style: 'defaultText', opacity: 0.4, marginBottom: 5 },
                { text: endDate ? this.formatDate(endDate, true) : '-', style: 'defaultText' },
              ],
              width: 'auto',
            },
            {
              stack: [
                { text: 'Completion time', style: 'defaultText', opacity: 0.4, marginBottom: 5 },
                { text: endDate ? this.formatDate(endDate, true) : '-', style: 'defaultText' },
              ],
              width: 'auto',
            },
          ],
          columnGap: 50,
        },
        {
          canvas: [
            {
              type: 'line',
              x1: 0,
              y1: 0,
              x2: 515,
              y2: 0,
              lineWidth: 1,
              lineColor: '#F4F4F4',
            },
          ],
          margin: [0, 10, 0, 15],
        },
        {
          columns: [
            {
              width: '25%',
              stack: [
                { text: 'Job Site Address', fontSize: 12, bold: true, marginBottom: 10 },
                { text: reservation.siteAddress || '-', style: 'defaultText', marginBottom: 3 },
                {
                  text: `${reservation.plz ? reservation.plz + ' ' : ''}${reservation.city || '-'}`,
                  style: 'defaultText',
                },
              ],
            },
            {
              width: '75%',
              stack: [
                { text: 'Client Details', fontSize: 12, bold: true, marginBottom: 10 },
                {
                  columns: [
                    {
                      width: '35%',
                      columns: [
                        { text: 'Name:', style: 'defaultText', width: 38, opacity: 0.6 },
                        {
                          text: `${reservation.clientDetails.name || ''}${
                            reservation.clientDetails.lastName
                              ? ' ' + reservation.clientDetails.lastName
                              : ''
                          }`,
                          style: 'defaultText',
                          width: '*',
                        },
                      ],
                      columnGap: 3,
                    },
                    {
                      width: '35%',
                      columns: [
                        { text: 'Company:', style: 'defaultText', width: 58, opacity: 0.6 },
                        {
                          text: reservation.clientDetails.companyName || '-',
                          style: 'defaultText',
                          width: '*',
                        },
                      ],
                      columnGap: 3,
                    },
                    {
                      width: '30%',
                      stack: [],
                    },
                  ],
                  columnGap: 10,
                  marginBottom: 5,
                },
                {
                  columns: [
                    {
                      width: '35%',
                      columns: [
                        { text: 'Email:', style: 'defaultText', width: 38, opacity: 0.6 },
                        {
                          text: reservation.clientDetails.email || '-',
                          style: 'defaultText',
                          width: '*',
                        },
                      ],
                      columnGap: 3,
                    },
                    {
                      width: '35%',
                      columns: [
                        { text: 'Phone:', style: 'defaultText', width: 58, opacity: 0.6 },
                        {
                          text: reservation.clientDetails.phoneNumber || '-',
                          style: 'defaultText',
                          width: '*',
                          noWrap: true,
                        },
                      ],
                      columnGap: 3,
                    },
                    {
                      width: '30%',
                      columns: [
                        { text: 'VAT:', style: 'defaultText', width: 30, opacity: 0.6 },
                        {
                          text: reservation.clientDetails.companyVatNumber || '-',
                          style: 'defaultText',
                          width: '*',
                        },
                      ],
                      columnGap: 3,
                    },
                  ],
                  columnGap: 10,
                },
              ],
            },
          ],
          columnGap: 10,
        },
        {
          canvas: [
            {
              type: 'line',
              x1: 0,
              y1: 0,
              x2: 515,
              y2: 0,
              lineWidth: 1,
              lineColor: '#F4F4F4',
            },
          ],
          margin: [0, 10, 0, 15],
        },
        {
          columns: [
            {
              width: '25%',
              stack: [
                { text: 'Timetable', fontSize: 12, bold: true, margin: [0, 0, 0, 10] },
                {
                  table: {
                    widths: ['*'],
                    body: [
                      [
                        {
                          stack: [
                            { text: 'Driving to site' },
                            { text: jobEventTimes[JobEventEnum.DRIVING_TO_SITE], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'Site arrival' },
                            { text: jobEventTimes[JobEventEnum.SITE_ARRIVAL], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'Security validation' },
                            { text: jobEventTimes[JobEventEnum.SECURITY_VALIDATION], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'Start of setup' },
                            { text: jobEventTimes[JobEventEnum.START_SETUP], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'End of setup' },
                            { text: jobEventTimes[JobEventEnum.END_SETUP], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'Start of pumping' },
                            { text: jobEventTimes[JobEventEnum.START_PUMPING], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'End of pumping' },
                            { text: jobEventTimes[JobEventEnum.END_PUMPING], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'Report' },
                            { text: jobEventTimes[JobEventEnum.REPORT], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'Client signature' },
                            { text: jobEventTimes[JobEventEnum.SIGNATURE], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'Start of clean-up' },
                            { text: jobEventTimes[JobEventEnum.START_CLEANUP], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'End of clean-up' },
                            { text: jobEventTimes[JobEventEnum.END_CLEANUP], opacity: 0.4 },
                          ],
                        },
                      ],
                      [
                        {
                          stack: [
                            { text: 'Leaving site' },
                            { text: jobEventTimes[JobEventEnum.LEAVE_SITE], opacity: 0.4 },
                          ],
                        },
                      ],
                    ],
                  },
                  layout: 'noBorders',
                  margin: [0, 0, 0, 30],
                },
              ],
            },
            {
              width: 'auto',
              canvas: [
                {
                  type: 'line',
                  x1: 0,
                  y1: 0,
                  x2: 0,
                  y2: 400,
                  lineWidth: 1,
                  lineColor: '#F4F4F4',
                },
              ],
            },
            {
              width: '50%',
              stack: [
                { text: 'Worksite', fontSize: 12, bold: true, margin: [10, 0, 0, 10] },
                {
                  table: {
                    widths: ['*', '*'],
                    body: [
                      [
                        { text: 'Terrain', style: 'defaultText', opacity: 0.4 },
                        {
                          text: job.terrainStability ? job.terrainStability : 'N/A',
                          style: 'defaultText',
                        },
                      ],
                      [
                        { text: 'Weight restriction', style: 'defaultText', opacity: 0.4 },
                        { text: job.authorizedWeight || 0, style: 'defaultText' },
                      ],
                      [
                        { text: 'Height restriction', style: 'defaultText', opacity: 0.4 },
                        { text: job.heightLimit || 0, style: 'defaultText' },
                      ],
                      [
                        { text: 'Power line', style: 'defaultText', opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 27, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${job.presenceOfPowerLines ? 'Yes' : 'No'}`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [9, -10, 0, 0],
                            },
                          ],
                        },
                      ],
                      [
                        { text: 'Local admin authorization', style: 'defaultText', opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 27, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${
                                reservation.localAdministrationAuthorizationKey ? 'Yes' : 'No'
                              }`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [9, -10, 0, 0],
                            },
                          ],
                        },
                      ],
                      [
                        { text: 'Traffic plan', style: 'defaultText', opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 27, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${reservation.trafficPlanKey ? 'Yes' : 'No'}`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [9, -10, 0, 0],
                            },
                          ],
                        },
                      ],
                      [
                        { text: 'Parking Permit', style: 'defaultText', opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 27, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${reservation.parkingPermitAcquiredKey ? 'Yes' : 'No'}`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [9, -10, 0, 0],
                            },
                          ],
                        },
                      ],
                    ],
                  },
                  layout: 'noBorders',
                  margin: [10, 0, 10, 10],
                },
                {
                  canvas: [
                    {
                      type: 'line',
                      x1: 0,
                      y1: 0,
                      x2: 515,
                      y2: 0,
                      lineWidth: 1,
                      lineColor: '#F4F4F4',
                    },
                  ],
                  margin: [0, 5, 0, 15],
                },
                {
                  width: '250',
                  stack: [
                    { text: 'Job', fontSize: 12, bold: true, margin: [10, 0, 0, 10] },
                    {
                      columns: [
                        { text: 'Start date', fontSize: 8, opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 62, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: reservation.dateFrom
                                ? this.formatDate(reservation.dateFrom, false)
                                : '-',
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 62, h: 12, r: 6, color: '#80BF62' },
                              ],
                            },
                            {
                              text: job.start ? this.formatDate(job.start, false) : '-',
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                      ],
                      columnGap: 5,
                      margin: [10, 0, 0, 10],
                    },
                    {
                      columns: [
                        { text: 'End date', fontSize: 8, opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 62, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: reservation.dateTo
                                ? this.formatDate(reservation.dateTo, false)
                                : '-',
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 62, h: 12, r: 6, color: '#80BF62' },
                              ],
                            },
                            {
                              text: endDate ? this.formatDate(endDate, false) : '-',
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                      ],
                      columnGap: 5,
                      margin: [10, 0, 0, 10],
                    },
                    {
                      columns: [
                        { text: 'Pumped volume', fontSize: 8, opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 43, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${job.amountOfConcrete ? job.amountOfConcrete : 0} m³`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                        {
                          stack: [
                            {
                              canvas: [
                                {
                                  type: 'rect',
                                  x: -20,
                                  y: 0,
                                  w: 43,
                                  h: 12,
                                  r: 6,
                                  color: '#80BF62',
                                },
                              ],
                            },
                            {
                              text: `${
                                job.report.amountOfConcrete ? job.report.amountOfConcrete : 0
                              } m³`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [-10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                      ],
                      columnGap: 5,
                      margin: [10, 0, 0, 10],
                    },
                    {
                      columns: [
                        { text: 'Pipe 80 mm (Flexible)', fontSize: 8, opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 43, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${
                                job.flexiblePipeLength80Mm ? job.flexiblePipeLength80Mm : 0
                              } m`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                        {
                          stack: [
                            {
                              canvas: [
                                {
                                  type: 'rect',
                                  x: -20,
                                  y: 0,
                                  w: 43,
                                  h: 12,
                                  r: 6,
                                  color: '#80BF62',
                                },
                              ],
                            },
                            {
                              text: `${
                                job.report.flexiblePipeLength80Mm
                                  ? job.report.flexiblePipeLength80Mm
                                  : 0
                              } m`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [-10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                      ],
                      columnGap: 5,
                      margin: [10, 0, 0, 10],
                    },
                    {
                      columns: [
                        { text: 'Pipe 90 mm (Flexible)', fontSize: 8, opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 43, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${
                                job.flexiblePipeLength90Mm ? job.flexiblePipeLength90Mm : 0
                              } m`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                        {
                          stack: [
                            {
                              canvas: [
                                {
                                  type: 'rect',
                                  x: -20,
                                  y: 0,
                                  w: 43,
                                  h: 12,
                                  r: 6,
                                  color: '#80BF62',
                                },
                              ],
                            },
                            {
                              text: `${
                                job.report.flexiblePipeLength90Mm
                                  ? job.report.flexiblePipeLength90Mm
                                  : 0
                              } m`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [-10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                      ],
                      columnGap: 5,
                      margin: [10, 0, 0, 10],
                    },
                    {
                      columns: [
                        { text: 'Pipe 100 mm (Rigid)', fontSize: 8, opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 43, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${
                                job.rigidPipeLength100Mm ? job.rigidPipeLength100Mm : '-'
                              } m`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                        {
                          stack: [
                            {
                              canvas: [
                                {
                                  type: 'rect',
                                  x: -20,
                                  y: 0,
                                  w: 43,
                                  h: 12,
                                  r: 6,
                                  color: '#80BF62',
                                },
                              ],
                            },
                            {
                              text: `${
                                job.report.rigidPipeLength100Mm
                                  ? job.report.rigidPipeLength100Mm
                                  : '-'
                              } m`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [-10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                      ],
                      columnGap: 5,
                      margin: [10, 0, 0, 10],
                    },
                    {
                      columns: [
                        { text: 'Pipe 120 mm (Rigid)', fontSize: 8, opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                { type: 'rect', x: 0, y: 0, w: 43, h: 12, r: 6, color: '#1565C0' },
                              ],
                            },
                            {
                              text: `${
                                job.rigidPipeLength120Mm ? job.rigidPipeLength120Mm : '-'
                              } m`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                        {
                          stack: [
                            {
                              canvas: [
                                {
                                  type: 'rect',
                                  x: -20,
                                  y: 0,
                                  w: 43,
                                  h: 12,
                                  r: 6,
                                  color: '#80BF62',
                                },
                              ],
                            },
                            {
                              text: `${
                                job.report.rigidPipeLength120Mm
                                  ? job.report.rigidPipeLength120Mm
                                  : '-'
                              } m`,
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [-10, -11, 0, 0],
                            },
                          ],
                          width: 65,
                        },
                      ],
                      columnGap: 5,
                      margin: [10, 0, 0, 10],
                    },
                    {
                      columns: [
                        { text: 'Order Number', fontSize: 8, opacity: 0.4 },
                        {
                          text: reservation.orderNumber,
                          margin: [-10, 0, 0, 0],
                        },
                      ],
                      margin: [10, 0, 0, 10],
                    },
                    {
                      columns: [
                        { text: 'Cleanup in concrete plant', fontSize: 8, opacity: 0.4 },
                        {
                          stack: [
                            {
                              canvas: [
                                {
                                  type: 'rect',
                                  x: -10,
                                  y: 0,
                                  w: 32,
                                  h: 12,
                                  r: 6,
                                  color: job.cleaning ? '#80BF62' : '#d32f2f',
                                },
                              ],
                            },
                            {
                              text: job.cleaning ? 'Yes' : 'No',
                              fontSize: 8,
                              bold: true,
                              color: '#fff',
                              margin: [0, -10, 0, 0],
                            },
                          ],
                        },
                      ],
                      margin: [10, 0, 0, 10],
                    },
                  ],
                },
                {
                  canvas: [
                    {
                      type: 'line',
                      x1: 0,
                      y1: 0,
                      x2: 515,
                      y2: 0,
                      lineWidth: 1,
                      lineColor: '#F4F4F4',
                    },
                  ],
                  margin: [0, 5, 0, 15],
                },
              ],
            },
            {
              width: '50%',
              stack: [
                { text: 'Chartered equipment', fontSize: 12, bold: true, margin: [-50, 0, 10, 10] },
                {
                  table: {
                    widths: ['*', '*'],
                    body: [
                      [
                        { text: 'Vehicle name', style: 'defaultText', opacity: 0.4 },
                        {
                          text: reservation.vehicle
                            ? VehicleUtils.getVehicleUniqueId(vehicle)
                            : '-',
                          style: 'defaultText',
                          marginLeft: -30,
                        },
                      ],
                      [
                        { text: 'License plate number', style: 'defaultText', opacity: 0.4 },
                        {
                          text: vehicle.licensePlateNumber ? vehicle.licensePlateNumber : '-',
                          style: 'defaultText',
                          marginLeft: -30,
                        },
                      ],
                      [
                        { text: 'Type', style: 'defaultText', opacity: 0.4 },
                        {
                          text: vehicle.type ? vehicle.type : '-',
                          style: 'defaultText',
                          marginLeft: -30,
                        },
                      ],
                      [
                        { text: 'Max flow rate', style: 'defaultText', opacity: 0.4 },
                        {
                          text: vehicle.maxFlowRate ? vehicle.maxFlowRate : '-',
                          style: 'defaultText',
                          marginLeft: -30,
                        },
                      ],
                      [
                        { text: 'Delivery pressure (bars)', style: 'defaultText', opacity: 0.4 },
                        {
                          text: vehicle.maxConcretePressure ? vehicle.maxConcretePressure : '-',
                          style: 'defaultText',
                          marginLeft: -30,
                        },
                      ],
                    ],
                  },
                  layout: 'noBorders',
                  margin: [-50, 0, 0, 20],
                },
              ],
            },
          ],
        },
        {
          stack: [
            {
              text: 'Security Validation',
              fontSize: 12,
              bold: true,
              margin: [135, -20, 0, 10],
            },
            {
              columns: [
                { text: 'Electrical risk', fontSize: 8, opacity: 0.4, width: '20%' },
                {
                  stack: [
                    job.isElectricalRisk
                      ? {
                          image: this.checkmark,
                          width: 12,
                          height: 12,
                          marginLeft: -2,
                          alignment: 'left',
                        }
                      : { text: 'x', fontSize: 12, color: '#F44336', alignment: 'left' },
                    {
                      text: this.limitText(job.electricalRiskComment || ''),
                      fontSize: 8,
                      alignment: 'left',
                      margin: [0, 2, 0, 0],
                      width: '100%',
                    },
                  ],
                  width: '90%',
                  marginLeft: 10,
                },
              ],
              columnGap: 10,
              margin: [135, 0, 0, 10],
            },
            {
              columns: [
                { text: 'Access compliance', fontSize: 8, opacity: 0.4, width: '20%' },
                {
                  stack: [
                    job.isAccessCompliance
                      ? {
                          image: this.checkmark,
                          width: 12,
                          height: 12,
                          marginLeft: -2,
                          alignment: 'left',
                        }
                      : { text: 'x', fontSize: 12, color: '#F44336', alignment: 'left' },
                    {
                      text: this.limitText(job.accessComplianceComment || ''),
                      fontSize: 8,
                      alignment: 'left',
                      margin: [0, 2, 0, 0],
                      width: '100%',
                    },
                  ],
                  width: '80%',
                  marginLeft: 10,
                },
              ],
              columnGap: 10,
              margin: [135, 0, 0, 10],
            },
          ],
        },
        {
          unbreakable: true,
          stack: [
            {
              text: 'Security Validation',
              fontSize: 12,
              bold: true,
              margin: [0, -10, 0, 15],
            },
            {
              columns: [
                { text: 'Parking compliance', fontSize: 8, opacity: 0.4, width: '20%' },
                {
                  stack: [
                    job.isParkingCompliance
                      ? {
                          image: this.checkmark,
                          width: 12,
                          height: 12,
                          marginLeft: -2,
                          alignment: 'left',
                        }
                      : { text: 'x', fontSize: 12, color: '#F44336', alignment: 'left' },
                    {
                      text: this.limitText(job.parkingComplianceComment || ''),
                      fontSize: 8,
                      alignment: 'left',
                      margin: [0, 2, 0, 0],
                      width: '100%',
                    },
                  ],
                  width: '80%',
                  marginLeft: 10,
                },
              ],
              columnGap: 10,
              margin: [0, 0, 0, 10],
            },
            {
              columns: [
                { text: 'Terrain stability', fontSize: 8, opacity: 0.4, width: '20%' },
                {
                  stack: [
                    job.isTerrainStability
                      ? {
                          image: this.checkmark,
                          width: 12,
                          height: 12,
                          marginLeft: -2,
                          alignment: 'left',
                        }
                      : { text: 'x', fontSize: 12, color: '#F44336', alignment: 'left' },
                    {
                      text: this.limitText(job.terrainStabilityComment || ''),
                      fontSize: 8,
                      alignment: 'left',
                      margin: [0, 2, 0, 0],
                      width: '100%',
                    },
                  ],
                  width: '80%',
                  marginLeft: 10,
                },
              ],
              columnGap: 10,
              margin: [0, 0, 0, 10],
            },
          ],
        },
        {
          unbreakable: true,
          stack: [
            {
              text: 'Job details',
              fontSize: 12,
              bold: true,
              margin: [0, 20, 0, 10],
            },
            {
              table: {
                widths: ['*', 'auto', 'auto'],
                body: jobDetailsTableBody,
              },
              layout: 'noBorders',
              margin: [0, 0, 0, 10],
            },
          ],
        },
        {
          text: 'Signature',
          fontSize: 12,
          bold: true,
          absolutePosition: { x: 50, y: 700 },
        },
        {
          text: reservation.clientDetails.name,
          fontSize: 8,
          absolutePosition: { x: 50, y: 720 },
        },
        job.signature || job.report.signature
          ? {
              image: job.signature || job.report.signature,
              width: 80,
              height: 80,
              absolutePosition: { x: 50, y: 735 },
            }
          : {
              text: '_______________',
              absolutePosition: { x: 50, y: 735 },
            },
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
        },
        subheader: {
          fontSize: 16,
          bold: true,
          margin: [0, 20, 0, 5],
        },
        itemLabel: {
          bold: true,
        },
        itemData: {
          italics: true,
        },
        tableHeader: {
          bold: true,
        },
      },
    };

    const pdfDoc = pdfMake.createPdf(documentDefinition);
    return new Promise<Buffer>((resolve, reject) => {
      if (!this.logoBase64) {
        return reject(new Error('Logo is missing. Cannot generate PDF.'));
      }
      pdfDoc.getBuffer(buffer => {
        resolve(Buffer.from(buffer));
      });
    });
  }

  async generateSingleReservationReport(singleReservationReport: GenerateSingleReservationReport) {
    const {
      reservation,
      dispatcherCompany,
      operatorCompany,
      invoiceNumber,
      totalExclVat,
      vatRate,
      vatAmount,
      totalSum,
      job,
    } = singleReservationReport;

    const concreteCost = job.concreteCost || 0;
    const additionalHourQty = job.additionalHours || 0;
    const content: any[] = [
      {
        columns: [
          [
            { text: `${operatorCompany.name}`, style: 'defaultText', marginBottom: 3 },
            { text: `${operatorCompany.address || ''}`, style: 'defaultText', marginBottom: 3 },
            { text: `${operatorCompany.vatNumber || ''}`, style: 'defaultText', marginBottom: 3 },
            { text: `${operatorCompany.phoneNumber || ''}`, style: 'defaultText', marginBottom: 3 },
            { text: `${operatorCompany.billingEmail}`, style: 'defaultText' },
            {
              text: 'Billed to',
              style: 'defaultText',
              marginTop: 10,
              marginBottom: 10,
              bold: true,
            },
            {
              text: `${dispatcherCompany?.name || reservation.clientDetails.name || ''}`,
              style: 'defaultText',
              marginBottom: 3,
            },
            { text: `${dispatcherCompany?.address || ''}`, style: 'defaultText', marginBottom: 3 },
            {
              text: `${
                dispatcherCompany?.vatNumber || reservation.clientDetails.companyVatNumber || ''
              }`,
              style: 'defaultText',
              marginBottom: 3,
            },
            {
              text: `${
                dispatcherCompany?.phoneNumber || reservation.clientDetails.phoneNumber || ''
              }`,
              style: 'defaultText',
              marginBottom: 3,
            },
            {
              text: `${dispatcherCompany?.billingEmail || reservation.clientDetails.email || ''}`,
              style: 'defaultText',
            },
          ],
          [
            {
              image: this.logoBase64,
              width: 200,
              height: 80,
              alignment: 'right',
              marginBottom: 15,
            },
            {
              columns: [
                { text: 'Invoice number: ', alignment: 'right', style: 'defaultText' },
                {
                  text: `${invoiceNumber}`,
                  alignment: 'right',
                  noWrap: true,
                  style: 'defaultText',
                  marginBottom: 4,
                },
              ],
            },
            {
              columns: [
                { text: 'Invoice date: ', alignment: 'right', style: 'defaultText' },
                { text: `${this.formattedDate}`, alignment: 'right', style: 'defaultText' },
              ],
            },
          ],
        ],
        margin: [10, 0, 10, 30],
      },
    ];

    const tableBody = [
      [
        { text: 'Description', style: ['columnMargin', 'defaultText'] },
        { text: 'Qty/Hr', style: ['rightAlignment', 'defaultText'] },
        { text: 'Price per unit', style: ['rightAlignment', 'defaultText'] },
        { text: 'Price', style: ['rightAlignment', 'defaultText'] },
      ],
    ];

    if (job.report.amountOfConcrete < reservation.pricelist.minimumM3Charged) {
      const baseFeeDuration = Math.min(
        job.hourDuration,
        reservation.pricelist.packageFlatFeeDuration,
      );
      tableBody.push([
        { text: 'Package fee (Minimum)', style: ['columnMargin', 'defaultText'] },
        {
          text: `${baseFeeDuration}`,
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(reservation.pricelist.minimumChargeFlatFee),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(job.baseFee),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    } else {
      const packageFlatFee = reservation.pricelist.packageFlatFee || 0;
      if (job.baseHourBreakdown?.work) {
        tableBody.push([
          { text: 'Package fee', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.baseHourBreakdown?.work}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(packageFlatFee),
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              (job.baseHourBreakdown.work / reservation.pricelist.packageFlatFeeDuration) *
                packageFlatFee,
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
      if (job.baseHourBreakdown?.night) {
        tableBody.push([
          { text: 'Package fee (Night)', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.baseHourBreakdown?.night}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(reservation.pricelist.packageFlatFeeNight ?? packageFlatFee),
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              (job.baseHourBreakdown.night / reservation.pricelist.packageFlatFeeDuration) *
                (reservation.pricelist.packageFlatFeeNight ?? packageFlatFee),
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
      if (job.baseHourBreakdown?.weekend) {
        tableBody.push([
          { text: 'Package fee (Weekend)', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.baseHourBreakdown?.weekend}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              reservation.pricelist.packageFlatFeeWeekend ?? packageFlatFee,
            ),
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              (job.baseHourBreakdown.weekend / reservation.pricelist.packageFlatFeeDuration) *
                (reservation.pricelist.packageFlatFeeWeekend ?? packageFlatFee),
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
    }
    if (concreteCost > 0) {
      tableBody.push([
        { text: 'Amount of concrete', style: ['columnMargin', 'defaultText'] },
        {
          text: `${
            reservation.pricelist.pricePerMeterPumped ? `${job.report.amountOfConcrete} m³` : 0
          }`,
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(reservation.pricelist.pricePerMeterPumped || 0),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(concreteCost),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    }
    const addPipeRow = (description, length, pricePerMeter, unit = 'm') => {
      if (length && length > 0) {
        tableBody.push([
          { text: description, style: ['columnMargin', 'defaultText'] },
          { text: `${length} ${unit}`, style: ['rightAlignment', 'defaultText'] },
          { text: this.formatCurrency(pricePerMeter), style: ['rightAlignment', 'defaultText'] },
          {
            text: this.formatCurrency(length * pricePerMeter),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
    };

    if (job.report.flexiblePipeLength80Mm) {
      addPipeRow(
        'Pipe (80mm)',
        job.report.flexiblePipeLength80Mm,
        reservation.pricelist.pricePerMeterOfFlexiblePipeLength80Mm,
      );
    }
    if (job.report.flexiblePipeLength90Mm) {
      addPipeRow(
        'Pipe (90mm)',
        job.report.flexiblePipeLength90Mm,
        reservation.pricelist.pricePerMeterOfFlexiblePipeLength90Mm,
      );
    }
    if (job.report.rigidPipeLength100Mm) {
      addPipeRow(
        'Pipe (100mm)',
        job.report.rigidPipeLength100Mm,
        reservation.pricelist.pricePerMeterOfFlexiblePipeLength100Mm,
      );
    }
    if (job.report.rigidPipeLength120Mm) {
      addPipeRow(
        'Pipe (120mm)',
        job.report.rigidPipeLength120Mm,
        reservation.pricelist.pricePerMeterOfRigidPipeLength120Mm,
      );
    }

    if (additionalHourQty > 0) {
      if (job.additionalHourBreakdown?.work > 0) {
        tableBody.push([
          { text: 'Additional hour', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.additionalHourBreakdown.work}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text:
              additionalHourQty != 0
                ? this.formatCurrency(reservation.pricelist.additionalHour || 0)
                : '0',
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              job.additionalHourBreakdown.work * reservation.pricelist.additionalHour || 0,
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
      if (job.additionalHourBreakdown?.night > 0) {
        tableBody.push([
          { text: 'Additional hour (Night)', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.additionalHourBreakdown.night}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text:
              additionalHourQty != 0
                ? this.formatCurrency(
                    reservation.pricelist.additionalHourNight ??
                      (reservation.pricelist.additionalHour || 0),
                  )
                : '0',
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              job.additionalHourBreakdown.night *
                (reservation.pricelist.additionalHourNight ??
                  (reservation.pricelist.additionalHour || 0)),
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
      if (job.additionalHourBreakdown?.weekend > 0) {
        tableBody.push([
          { text: 'Additional hour (Weekend)', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.additionalHourBreakdown.weekend}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text:
              additionalHourQty != 0
                ? this.formatCurrency(
                    reservation.pricelist.additionalHourWeekend ??
                      (reservation.pricelist.additionalHour || 0),
                  )
                : '0',
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              job.additionalHourBreakdown.weekend *
                (reservation.pricelist.additionalHourWeekend ??
                  (reservation.pricelist.additionalHour || 0)),
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
    }

    if (
      reservation.pricelist.pumpTiers &&
      reservation.pricelist.pumpTiers.length > 0 &&
      job.report.amountOfConcrete
    ) {
      tableBody.push([
        { text: 'Concrete Amount', style: ['columnMargin', 'defaultText'] },
        {
          text: `${job.report.amountOfConcrete}`,
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(
            (job?.pumpTiersCost || 0) / (job?.report?.amountOfConcrete || 0),
          ),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(job?.pumpTiersCost || 0),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    }

    if (job.enlistSecondTechnician && job.report.secondTechnicianMinutes) {
      if (job.secondTechnicianHourBreakdown.work) {
        tableBody.push([
          { text: '2nd technician fee', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.secondTechnicianHourBreakdown.work}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(reservation.pricelist.secondTechnicianHourFee || 0),
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              job.secondTechnicianHourBreakdown.work *
                reservation.pricelist.secondTechnicianHourFee || 0,
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
      if (job.secondTechnicianHourBreakdown.night) {
        tableBody.push([
          { text: '2nd technician fee (Night)', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.secondTechnicianHourBreakdown.night}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              reservation.pricelist.secondTechnicianHourFeeNight ??
                (reservation.pricelist.secondTechnicianHourFee || 0),
            ),
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              job.secondTechnicianHourBreakdown.night *
                (reservation.pricelist.secondTechnicianHourFeeNight ??
                  (reservation.pricelist.secondTechnicianHourFee || 0)),
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
      if (job.secondTechnicianHourBreakdown.weekend) {
        tableBody.push([
          { text: '2nd technician fee (Weekend)', style: ['columnMargin', 'defaultText'] },
          {
            text: `${job.secondTechnicianHourBreakdown.weekend}`,
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              reservation.pricelist.secondTechnicianHourFeeWeekend ??
                (reservation.pricelist.secondTechnicianHourFee || 0),
            ),
            style: ['rightAlignment', 'defaultText'],
          },
          {
            text: this.formatCurrency(
              job.secondTechnicianHourBreakdown.weekend *
                (reservation.pricelist.secondTechnicianHourFeeWeekend ??
                  (reservation.pricelist.secondTechnicianHourFee || 0)),
            ),
            style: ['rightAlignment', 'defaultText'],
          },
        ]);
      }
    }

    if (job.barbotine) {
      tableBody.push([
        { text: 'Barbotine', style: ['columnMargin', 'defaultText'] },
        {
          text: reservation.job.barbotine ? '1' : '0',
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(
            reservation.job.barbotine ? reservation.pricelist.barbotine : 0,
          ),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(
            reservation.job.barbotine ? reservation.pricelist.barbotine : 0,
          ),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    }

    if (job.supplyOfTheChemicalSlushie) {
      tableBody.push([
        { text: 'Chemical slushie', style: ['columnMargin', 'defaultText'] },
        {
          text: job.supplyOfTheChemicalSlushieCost ? '1' : '0',
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(
            job.supplyOfTheChemicalSlushieCost
              ? reservation.pricelist.supplyOfTheChemicalSlushie
              : 0,
          ),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(
            job.supplyOfTheChemicalSlushieCost
              ? reservation.pricelist.supplyOfTheChemicalSlushie
              : 0,
          ),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    }

    if (job.cleaning === Cleaning.CENTRAL) {
      tableBody.push([
        { text: 'Cleaning fee', style: ['columnMargin', 'defaultText'] },
        {
          text: `${reservation.job.cleaning == Cleaning.CENTRAL ? 1 : 0}`,
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(
            reservation.job.cleaning === Cleaning.CENTRAL ? reservation.pricelist.cleaningFee : 0,
          ),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(
            reservation.job.cleaning === Cleaning.CENTRAL ? reservation.pricelist.cleaningFee : 0,
          ),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    }

    if (job.report.extraCementBags) {
      const cementBags = job.report.cementBags || 0;
      tableBody.push([
        { text: 'Cement bags', style: ['columnMargin', 'defaultText'] },
        { text: `${cementBags} bag/s`, style: ['rightAlignment', 'defaultText'] },
        {
          text: this.formatCurrency(reservation.pricelist.extraCementBagPrice),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(job.extraCementBagCost),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    }

    if (job.chargedTransportRate) {
      tableBody.push([
        { text: 'Transport fee', style: ['columnMargin', 'defaultText'] },
        {
          text: '-',
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(job.chargedTransportRate.tariff),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(job.chargedTransportRate.tariff),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    }

    content.push({
      table: {
        widths: ['*', 80, 80, 80],
        body: tableBody,
      },
      layout: {
        fillColor: function (rowIndex) {
          return rowIndex === 0 ? '#F7F7F7' : null;
        },
        hLineWidth: function (i, node) {
          return i === node.table.body.length ? 0.5 : 0;
        },
        vLineWidth: function () {
          return 0;
        },
        hLineColor: function (i, node) {
          return i === node.table.body.length ? '#F4F4F4' : null;
        },
        vLineColor: function () {
          return null;
        },
        heights: function (rowIndex) {
          return rowIndex === 0 ? 32 : 28;
        },
      },
    });

    content.push({
      columns: [
        {
          table: {
            widths: ['*', 70],
            body: [
              [
                {
                  text: 'Total excl. VAT',
                  style: 'totalsLabel',
                },
                { text: `${this.formatCurrency(totalExclVat)}`, style: 'totalsValue' },
              ],
              [
                { text: `VAT (${vatRate}%)`, style: 'totalsLabel' },
                { text: `${this.formatCurrency(vatAmount)}`, style: 'totalsValue' },
              ],
              [
                { text: 'Total', style: 'totalText', bold: true },
                {
                  text: `${this.formatCurrency(totalSum)}`,
                  style: 'totalValue',
                },
              ],
            ],
          },
          layout: {
            hLineWidth: function (i, node) {
              return i === node.table.body.length - 1 ? 0.5 : 0;
            },
            vLineWidth: function () {
              return 0;
            },
            hLineColor: function (i, node) {
              return i === node.table.body.length - 1 ? '#F4F4F4' : null;
            },
            vLineColor: function () {
              return null;
            },
          },
          marginRight: 10,
          heights: 28,
        },
      ],
    });

    const documentDefinition = {
      content: content,
      pageSize: 'A4',
      styles: {
        defaultText: {
          fontSize: 9,
        },
        columnMargin: {
          margin: [10, 5, 0, 5],
        },
        rightAlignment: {
          margin: [10, 5, 10, 5],
          alignment: 'right',
        },
        totalsLabel: {
          fontSize: 9,
          alignment: 'right',
          margin: [0, 5, 10, 5],
        },
        totalsValue: {
          fontSize: 9,
          alignment: 'right',
          margin: [0, 5, 0, 5],
        },
        totalText: {
          fontSize: 10,
          alignment: 'right',
          bold: true,
          margin: [0, 5, 10, 5],
        },
        totalValue: {
          fontSize: 10,
          alignment: 'right',
          bold: true,
          margin: [0, 5, 0, 5],
        },
      },
    };

    const pdfDoc = pdfMake.createPdf(documentDefinition);
    return new Promise<Buffer>((resolve, reject) => {
      if (!this.logoBase64) {
        return reject(new Error('Logo is missing. Cannot generate PDF.'));
      }
      pdfDoc.getBuffer(buffer => {
        resolve(Buffer.from(buffer));
      });
    });
  }

  async generateBulkReservationReport(bulkReservationReport: GenerateBulkReservationReport) {
    const { dispatcherCompany, operatorCompany, totalSum } = bulkReservationReport;

    const vatRate = 21 / 100;
    const totalExclVat = totalSum / (1 + vatRate);
    const vatAmount = totalSum - totalExclVat;

    const tableBody = [
      [
        { text: 'Description', style: ['columnMargin', 'defaultText'] },
        { text: 'No. of reservations', style: ['rightAlignment', 'defaultText'] },
        { text: 'Amount', style: ['rightAlignment', 'defaultText'], alignment: 'right' },
      ],
      [
        { text: `Invoice for ${this.formattedDate}`, style: ['columnMargin', 'defaultText'] },
        {
          text: `${bulkReservationReport.totalReservations}`,
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(totalSum),
          style: ['rightAlignment', 'defaultText'],
          alignment: 'right',
        },
      ],
    ];

    const documentDefinition = {
      pageSize: 'A4',
      content: [
        {
          columns: [
            [
              { text: `${operatorCompany.name}`, style: 'defaultText', marginBottom: 3 },
              { text: `${operatorCompany.address || ''}`, style: 'defaultText', marginBottom: 3 },
              {
                text: `${operatorCompany.vatNumber || ''}`,
                style: 'defaultText',
                marginBottom: 3,
              },
              {
                text: `${operatorCompany.phoneNumber || ''}`,
                style: 'defaultText',
                marginBottom: 3,
              },
              { text: `${operatorCompany.billingEmail || ''}`, style: 'defaultText' },
              {
                text: 'Billed to',
                style: 'defaultText',
                marginTop: 10,
                marginBottom: 10,
                bold: true,
              },
              { text: `${dispatcherCompany.name}`, style: 'defaultText', marginBottom: 3 },
              {
                text: `${dispatcherCompany.address || ''}`,
                style: 'defaultText',
                marginBottom: 3,
              },
              {
                text: `${dispatcherCompany.vatNumber || ''}`,
                style: 'defaultText',
                marginBottom: 3,
              },
              {
                text: `${dispatcherCompany.phoneNumber || ''}`,
                style: 'defaultText',
                marginBottom: 3,
              },
              { text: `${dispatcherCompany.billingEmail || ''}`, style: 'defaultText' },
            ],
            [
              {
                image: this.logoBase64,
                width: 200,
                height: 80,
                alignment: 'right',
                marginBottom: 15,
              },
              {
                columns: [
                  { text: 'Invoice number: ', alignment: 'right', style: 'defaultText' },
                  {
                    text: `${bulkReservationReport.invoiceNumber}`,
                    alignment: 'right',
                    style: 'defaultText',
                    marginBottom: 4,
                  },
                ],
              },
              {
                columns: [
                  { text: 'Invoice date: ', alignment: 'right', style: 'defaultText' },
                  {
                    text: `${this.formattedDate}`,
                    alignment: 'right',
                    style: 'defaultText',
                    marginBottom: 4,
                  },
                ],
              },
            ],
          ],
          margin: [10, 0, 10, 30],
        },
        {
          table: {
            widths: ['*', 100, 80],
            body: tableBody,
          },
          layout: {
            fillColor: function (rowIndex) {
              return rowIndex === 0 ? '#F7F7F7' : null;
            },
            hLineWidth: function (i, node) {
              return i === node.table.body.length ? 0.5 : 0;
            },
            vLineWidth: function () {
              return 0;
            },
            hLineColor: function (i, node) {
              return i === node.table.body.length ? '#F4F4F4' : null;
            },
            vLineColor: function () {
              return null;
            },
            heights: function (rowIndex) {
              return rowIndex === 0 ? 32 : 28;
            },
          },
        },
        {
          columns: [
            {
              table: {
                widths: ['*', 70],
                body: [
                  [
                    {
                      text: 'Total exc. VAT (before surcharge)',
                      style: 'totalsLabel',
                    },
                    { text: this.formatCurrency(totalExclVat), style: 'totalsValue' },
                  ],
                  [
                    { text: 'VAT (21%)', style: 'totalsLabel' },
                    { text: `${this.formatCurrency(vatAmount)}`, style: 'totalsValue' },
                  ],
                  [
                    { text: 'Total', style: 'totalText', bold: true },
                    {
                      text: this.formatCurrency(totalSum),
                      style: 'totalValue',
                      bold: true,
                    },
                  ],
                ],
              },
              layout: {
                hLineWidth: function (i, node) {
                  return i === node.table.body.length - 1 ? 0.5 : 0;
                },
                vLineWidth: function () {
                  return 0;
                },
                hLineColor: function (i, node) {
                  return i === node.table.body.length - 1 ? '#F4F4F4' : null;
                },
                vLineColor: function () {
                  return null;
                },
              },
              marginRight: 10,
              heights: 28,
            },
          ],
        },
      ],
      styles: {
        defaultText: {
          fontSize: 9,
        },
        columnMargin: {
          margin: [10, 5, 0, 5],
        },
        rightAlignment: {
          margin: [10, 5, 10, 5],
          alignment: 'right',
        },
        totalsLabel: {
          fontSize: 9,
          alignment: 'right',
          margin: [0, 5, 10, 5],
        },
        totalsValue: {
          fontSize: 9,
          alignment: 'right',
          margin: [0, 5, 0, 5],
        },
        totalText: {
          fontSize: 10,
          alignment: 'right',
          margin: [0, 5, 10, 5],
          bold: true,
        },
        totalValue: {
          fontSize: 10,
          alignment: 'right',
          margin: [0, 5, 0, 5],
          bold: true,
        },
      },
    };

    const pdfDoc = pdfMake.createPdf(documentDefinition);
    return new Promise<Buffer>((resolve, reject) => {
      if (!this.logoBase64) {
        return reject(new Error('Logo is missing. Cannot generate PDF.'));
      }
      pdfDoc.getBuffer(buffer => {
        resolve(Buffer.from(buffer));
      });
    });
  }

  async generateCancellationReport(singleReservationReport: GenerateSingleReservationReport) {
    const {
      reservation,
      dispatcherCompany,
      operatorCompany,
      invoiceNumber,
      totalExclVat,
      vatRate,
      vatAmount,
      totalSum,
      job,
    } = singleReservationReport;

    const content: any[] = [
      {
        columns: [
          [
            { text: `${operatorCompany.name}`, style: 'defaultText', marginBottom: 3 },
            { text: `${operatorCompany.address || ''}`, style: 'defaultText', marginBottom: 3 },
            { text: `${operatorCompany.vatNumber || ''}`, style: 'defaultText', marginBottom: 3 },
            { text: `${operatorCompany.phoneNumber || ''}`, style: 'defaultText', marginBottom: 3 },
            { text: `${operatorCompany.billingEmail}`, style: 'defaultText' },
            {
              text: 'Billed to',
              style: 'defaultText',
              marginTop: 10,
              marginBottom: 10,
              bold: true,
            },
            { text: `${dispatcherCompany.name}`, style: 'defaultText', marginBottom: 3 },
            { text: `${dispatcherCompany.address || ''}`, style: 'defaultText', marginBottom: 3 },
            {
              text: `${dispatcherCompany.vatNumber || ''}`,
              style: 'defaultText',
              marginBottom: 3,
            },
            {
              text: `${dispatcherCompany.phoneNumber || ''}`,
              style: 'defaultText',
              marginBottom: 3,
            },
            { text: `${dispatcherCompany.billingEmail}`, style: 'defaultText' },
          ],
          [
            {
              image: this.logoBase64,
              width: 200,
              height: 80,
              alignment: 'right',
              marginBottom: 15,
            },
            {
              columns: [
                { text: 'Invoice number: ', alignment: 'right', style: 'defaultText' },
                {
                  text: `${invoiceNumber}`,
                  alignment: 'right',
                  noWrap: true,
                  style: 'defaultText',
                  marginBottom: 4,
                },
              ],
            },
            {
              columns: [
                { text: 'Invoice date: ', alignment: 'right', style: 'defaultText' },
                { text: `${this.formattedDate}`, alignment: 'right', style: 'defaultText' },
              ],
            },
          ],
        ],
        margin: [10, 0, 10, 30],
      },
    ];

    const tableBody = [
      [
        { text: 'Description', style: ['columnMargin', 'defaultText'] },
        { text: 'Qty/Hr', style: ['rightAlignment', 'defaultText'] },
        { text: 'Price per unit', style: ['rightAlignment', 'defaultText'] },
        { text: 'Price', style: ['rightAlignment', 'defaultText'] },
      ],
    ];

    if (job.cancellationFeeType === CancellationType.BASE) {
      tableBody.push([
        { text: 'Standard cancellation fee', style: ['columnMargin', 'defaultText'] },
        {
          text: `1`,
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(reservation.pricelist.cancellationFee),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(job.totalCost),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    } else {
      tableBody.push([
        { text: 'Late cancellation fee', style: ['columnMargin', 'defaultText'] },
        {
          text: `1`,
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(reservation.pricelist.dayCancellationFee),
          style: ['rightAlignment', 'defaultText'],
        },
        {
          text: this.formatCurrency(job.totalCost),
          style: ['rightAlignment', 'defaultText'],
        },
      ]);
    }

    content.push({
      table: {
        widths: ['*', 80, 80, 80],
        body: tableBody,
      },
      layout: {
        fillColor: function (rowIndex) {
          return rowIndex === 0 ? '#F7F7F7' : null;
        },
        hLineWidth: function (i, node) {
          return i === node.table.body.length ? 0.5 : 0;
        },
        vLineWidth: function () {
          return 0;
        },
        hLineColor: function (i, node) {
          return i === node.table.body.length ? '#F4F4F4' : null;
        },
        vLineColor: function () {
          return null;
        },
        heights: function (rowIndex) {
          return rowIndex === 0 ? 32 : 28;
        },
      },
    });

    content.push({
      columns: [
        {
          table: {
            widths: ['*', 70],
            body: [
              [
                {
                  text: 'Total excl. VAT',
                  style: 'totalsLabel',
                },
                { text: `${this.formatCurrency(totalExclVat)}`, style: 'totalsValue' },
              ],
              [
                { text: `VAT (${vatRate}%)`, style: 'totalsLabel' },
                { text: `${this.formatCurrency(vatAmount)}`, style: 'totalsValue' },
              ],
              [
                { text: 'Total', style: 'totalText', bold: true },
                {
                  text: `${this.formatCurrency(totalSum)}`,
                  style: 'totalValue',
                },
              ],
            ],
          },
          layout: {
            hLineWidth: function (i, node) {
              return i === node.table.body.length - 1 ? 0.5 : 0;
            },
            vLineWidth: function () {
              return 0;
            },
            hLineColor: function (i, node) {
              return i === node.table.body.length - 1 ? '#F4F4F4' : null;
            },
            vLineColor: function () {
              return null;
            },
          },
          marginRight: 10,
          heights: 28,
        },
      ],
    });

    const documentDefinition = {
      content: content,
      pageSize: 'A4',
      styles: {
        defaultText: {
          fontSize: 9,
        },
        columnMargin: {
          margin: [10, 5, 0, 5],
        },
        rightAlignment: {
          margin: [10, 5, 10, 5],
          alignment: 'right',
        },
        totalsLabel: {
          fontSize: 9,
          alignment: 'right',
          margin: [0, 5, 10, 5],
        },
        totalsValue: {
          fontSize: 9,
          alignment: 'right',
          margin: [0, 5, 0, 5],
        },
        totalText: {
          fontSize: 10,
          alignment: 'right',
          bold: true,
          margin: [0, 5, 10, 5],
        },
        totalValue: {
          fontSize: 10,
          alignment: 'right',
          bold: true,
          margin: [0, 5, 0, 5],
        },
      },
    };

    const pdfDoc = pdfMake.createPdf(documentDefinition);
    return new Promise<Buffer>((resolve, reject) => {
      if (!this.logoBase64) {
        return reject(new Error('Logo is missing. Cannot generate PDF.'));
      }
      pdfDoc.getBuffer(buffer => {
        resolve(Buffer.from(buffer));
      });
    });
  }

  formatCurrency(value: number): string {
    return (
      value?.toLocaleString('de-DE', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) +
        ' €' || 0 + ' €'
    );
  }

  limitText(text, limit = 250) {
    if (text.length > limit) {
      return text.substring(0, limit) + '...'; // Truncate the text and add ellipsis
    }
    return text;
  }
}
