import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1719313361848 implements MigrationInterface {
  name = 'Migrations1719313361848';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "operator_client" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "name" character varying NOT NULL, "lastName" character varying NOT NULL, "email" character varying NOT NULL, "phoneNumber" character varying NOT NULL, "companyName" character varying NOT NULL, "companyVatNumber" character varying NOT NULL, CONSTRAINT "PK_71c4b577c2b01b5d5d8cb3406a4" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "operator_client"`);
  }
}
