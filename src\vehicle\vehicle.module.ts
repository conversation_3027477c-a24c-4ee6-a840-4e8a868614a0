import { Module, forwardRef } from '@nestjs/common';
import { VehicleService } from './v1/vehicle.service';
import { VehicleController } from './v1/vehicle.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Vehicle } from './entities/vehicle.entity';
import { UsersModule } from 'src/users/users.module';
import { ReservationModule } from 'src/reservation/reservation.module';
import { FavoriteModule } from 'src/favorite/favorite.module';
import { CompanyModule } from 'src/company/company.module';
import { PartnerModule } from 'src/partner/partner.module';
import { UnavailablePeriodModule } from 'src/unavailable-period/unavailable-period.module';
import { ContractModule } from 'src/contract/contract.module';
import { FilesModule } from 'src/s3/files.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Vehicle]),
    forwardRef(() => UsersModule),
    forwardRef(() => ReservationModule),
    PartnerModule,
    FavoriteModule,
    forwardRef(() => CompanyModule),
    forwardRef(() => UnavailablePeriodModule),
    ContractModule,
    FilesModule,
  ],
  controllers: [VehicleController],
  providers: [VehicleService],
  exports: [VehicleService],
})
export class VehicleModule {}
