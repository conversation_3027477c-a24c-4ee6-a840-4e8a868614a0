import { JobStatus } from 'src/common/constants/job.enum';
import { ClientDetails } from 'src/reservation/entities/reservation.entity';

export interface VehicleReservationsResponse {
  id: number;
  vehicleUniqueId: string;
  vehicleCompanyId: number;
  operatorName: string;
  operatorId: number;
  operatorPhoneNumber: string;
  operatorStatus: string;
  managerId: number;
  companyName: string;
  companyEmail: string;
  companyPhoneNumber: string;
  boomSize: number;
  type: string;
  imageUrl?: string;
  unavailablePeriods?: Array<{
    id: number;
    from: string;
    to: string;
    operatorId: number | null;
    contract: any;
  }>;
  hasAssignedPricelist?: boolean;
  reservations?: VehicleReservationDetails[];
  company?: {
    settings?: {
      holidays?: Array<{
        id: number;
        date: string;
        reason: string;
      }>;
    };
  };
}

export interface VehicleReservationDetails {
  id: number;
  dispatcherId?: number;
  dateFrom: Date;
  dateTo: Date;
  operatorName: string;
  siteAddress: string;
  amount?: number;
  flowRate?: number;
  jobStatus: JobStatus | null;
  hasBeenRead: boolean;
  ownReservation: boolean;
  clientDetails: ClientDetails;
  city?: string;
  pricelistId: number;
}
