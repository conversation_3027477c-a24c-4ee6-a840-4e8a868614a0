import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsEmail,
  IsEnum,
  IsArray,
  ValidateNested,
  IsNumber,
  ArrayMaxSize,
} from 'class-validator';
import { CompanyCategory, CompanyType } from 'src/common/constants/company.enum';
import { Status } from 'src/common/constants/status.enum';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';

class TaxDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNumber()
  percentage: number;
}

export class CreateCompanyDto {
  @ApiProperty({ description: 'Name of the company', required: true })
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'name'))
  name: string;

  @ApiProperty({ enum: CompanyType, enumName: 'CompanyType' })
  @IsOptional()
  @IsEnum(CompanyType)
  type?: CompanyType;

  @ApiProperty({ enum: CompanyCategory, enumName: 'CompanyCategory' })
  @IsOptional()
  @IsEnum(CompanyCategory)
  category?: CompanyCategory;

  @ApiProperty({ description: 'Contact email', required: true })
  @IsEmail()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'contactEmail'))
  contactEmail: string;

  @ApiProperty({ description: 'Billing email', required: false })
  @IsOptional()
  @IsEmail()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'billingEmail'))
  billingEmail?: string;

  @ApiProperty({ description: 'Planning email', required: false })
  @IsOptional()
  @IsEmail()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'planningEmail'))
  planningEmail?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'phoneNumber'))
  phoneNumber?: string;

  @ApiProperty({ description: 'Company address', required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'address'))
  address?: string;

  @ApiProperty({ description: 'Company secondary address', required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'secondaryAddress'))
  secondaryAddress?: string;

  @ApiProperty({ description: 'Country', required: false })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({ description: 'City', required: false })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({ description: 'Zip Code', required: false })
  @IsOptional()
  @IsNumber()
  zipCode?: number;

  @ApiProperty({ enum: Status, enumName: 'Status' })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiProperty({ description: 'VAT Number', required: false })
  @IsOptional()
  @IsString()
  vatNumber?: string;

  @ApiProperty({ description: 'Taxes', type: TaxDto, required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMaxSize(9)
  @Type(() => TaxDto)
  taxes?: TaxDto[];
}
