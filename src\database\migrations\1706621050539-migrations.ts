import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706621050539 implements MigrationInterface {
  name = 'Migrations1706621050539';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "companyName"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "companyName" character varying NOT NULL`);
  }
}
