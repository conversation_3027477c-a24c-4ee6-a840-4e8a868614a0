import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { from<PERSON>uffer } from 'file-type';

@Injectable()
export class FileValidationPipe implements PipeTransform {
  private readonly MAX_SIZE = 2 * 1024 * 1024; // 2 MB
  private readonly MIME_TYPES = ['image/jpeg', 'image/png', 'image/jpg'];

  async transform(files: Record<string, Express.Multer.File[]>) {
    if (!files || Object.keys(files).length === 0) {
      return files;
    }
    for (const [fieldName, fileArray] of Object.entries(files)) {
      for (const file of fileArray) {
        if (!file || !file.buffer) {
          throw new BadRequestException(`File is missing or corrupt for field: ${fieldName}`);
        }

        if (file.size > this.MAX_SIZE) {
          throw new BadRequestException(
            `Field ${fieldName}: The file size should not exceed 2 MB.`,
          );
        }

        const fileType = await from<PERSON>uffer(file.buffer);

        if (!fileType) {
          throw new BadRequestException(`Unable to determine file type for field: ${fieldName}`);
        }

        const { mime } = fileType;

        if (!this.MIME_TYPES.includes(mime)) {
          throw new BadRequestException(
            `Field ${fieldName}: The image should be either jpeg, png, or jpg.`,
          );
        }
      }
    }

    return files;
  }
}
