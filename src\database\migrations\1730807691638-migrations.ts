import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1730807691638 implements MigrationInterface {
  name = 'Migrations1730807691638';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "task_comment" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "content" text NOT NULL, "taskId" integer NOT NULL, CONSTRAINT "PK_28da4411b195bfc3c451cfa21ff" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0fed042ede2365de8b32e105cc" ON "task_comment" ("taskId") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."task_activity_actiontype_enum" AS ENUM('StatusUpdate', 'AssigneeChange', 'PriorityUpdate')`,
    );
    await queryRunner.query(
      `CREATE TABLE "task_activity" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "actionType" "public"."task_activity_actiontype_enum" NOT NULL, "oldValue" character varying NOT NULL, "taskId" integer NOT NULL, CONSTRAINT "PK_a8f24c7952c9ff5533f88279941" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dd4d1f026f618e434d9254c0d6" ON "task_activity" ("taskId") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."task_priority_enum" AS ENUM('Low', 'Medium', 'High')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."task_status_enum" AS ENUM('To Do', 'Doing', 'Done')`,
    );
    await queryRunner.query(
      `CREATE TABLE "task" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "dueDate" TIMESTAMP, "label" character varying NOT NULL, "priority" "public"."task_priority_enum" NOT NULL, "status" "public"."task_status_enum" NOT NULL, "description" text NOT NULL, "assigneeId" integer NOT NULL, "companyId" integer NOT NULL, CONSTRAINT "PK_fb213f79ee45060ba925ecd576e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7384988f7eeb777e44802a0bac" ON "task" ("assigneeId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1dab627a527877426f230e2913" ON "task" ("companyId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "task_comment" ADD CONSTRAINT "FK_0fed042ede2365de8b32e105cc6" FOREIGN KEY ("taskId") REFERENCES "task"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "task_activity" ADD CONSTRAINT "FK_dd4d1f026f618e434d9254c0d68" FOREIGN KEY ("taskId") REFERENCES "task"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "task" ADD CONSTRAINT "FK_7384988f7eeb777e44802a0baca" FOREIGN KEY ("assigneeId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "task" ADD CONSTRAINT "FK_1dab627a527877426f230e2913e" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "task" DROP CONSTRAINT "FK_1dab627a527877426f230e2913e"`);
    await queryRunner.query(`ALTER TABLE "task" DROP CONSTRAINT "FK_7384988f7eeb777e44802a0baca"`);
    await queryRunner.query(
      `ALTER TABLE "task_activity" DROP CONSTRAINT "FK_dd4d1f026f618e434d9254c0d68"`,
    );
    await queryRunner.query(
      `ALTER TABLE "task_comment" DROP CONSTRAINT "FK_0fed042ede2365de8b32e105cc6"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_1dab627a527877426f230e2913"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7384988f7eeb777e44802a0bac"`);
    await queryRunner.query(`DROP TABLE "task"`);
    await queryRunner.query(`DROP TYPE "public"."task_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."task_priority_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dd4d1f026f618e434d9254c0d6"`);
    await queryRunner.query(`DROP TABLE "task_activity"`);
    await queryRunner.query(`DROP TYPE "public"."task_activity_actiontype_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0fed042ede2365de8b32e105cc"`);
    await queryRunner.query(`DROP TABLE "task_comment"`);
  }
}
