import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersModule } from 'src/users/users.module';
import { TaskComment } from './entities/comment.entity';
import { TaskActivity } from './entities/task-activity.entity';
import { TaskLabel } from './entities/task-label.entity';
import { Task } from './entities/task.entity';
import { TaskLabelController } from './v1/task-label.controller';
import { TaskLabelService } from './v1/task-label.service';
import { TaskController } from './v1/task.controller';
import { TaskService } from './v1/task.service';

@Module({
  imports: [TypeOrmModule.forFeature([Task, TaskComment, TaskActivity, TaskLabel]), UsersModule],
  providers: [TaskService, TaskLabelService],
  controllers: [TaskController, TaskLabelController],
})
export class TaskModule {}
