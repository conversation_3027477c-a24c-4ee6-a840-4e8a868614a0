import { ApiProperty } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import {
  ArrayMaxSize,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { processBoolean } from 'src/common/helpers/processBooleans';
import { processNumber } from 'src/common/helpers/processNumbers';

class PumpTier {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsNumber()
  minimum: number;

  @ApiProperty()
  @IsNumber()
  maximum: number;
}

class TransportRate {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNumber()
  tariff: number;

  @ApiProperty()
  @IsNumber()
  from: number;

  @ApiProperty()
  @IsNumber()
  to: number;
}

class VatDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  percentage: number;
}

export class CreatePricelistDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  title: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  packageFlatFee?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  pricePerMeterPumped?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  additionalHour?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  cleaningFee?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  pricePerMeterOfFlexiblePipeLength80Mm?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  pricePerMeterOfFlexiblePipeLength90Mm?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  pricePerMeterOfFlexiblePipeLength100Mm?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  pricePerMeterOfRigidPipeLength120Mm?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  supplyOfTheChemicalSlushie: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  barbotine: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  extraCementBagPrice?: number;

  @ApiProperty({ type: Number })
  @IsOptional()
  @IsNumber()
  @Min(0)
  packageFlatFeeDuration?: number;

  @ApiProperty({ type: Number })
  @IsOptional()
  @IsNumber()
  @Min(0)
  pipeInvoicingStartsFrom?: number;

  @ApiProperty({ type: PumpTier })
  @IsOptional()
  @ArrayMaxSize(9)
  @Type(() => PumpTier)
  pumpTiers?: PumpTier[];

  @ApiProperty({ type: TransportRate })
  @IsOptional()
  @ArrayMaxSize(9)
  @Type(() => TransportRate)
  transportRates?: TransportRate[];

  @ApiProperty()
  @IsOptional()
  containerId?: number;

  @ApiProperty()
  @Type(() => Number)
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  vehicleId?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  dayContractFee?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  dayContractDuration?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  dayContractOvertimeRate?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  packageFlatFeeWeekend?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  additionalHourWeekend?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  packageFlatFeeNight?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  additionalHourNight?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  minimumChargeFlatFee?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  minimumM3Charged?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  dayCancellationFee?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  cancellationFee?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  secondTechnicianHourFeeWeekend?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  vat?: VatDto;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  secondTechnicianHourFeeNight?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  isContract?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  contractLateCancellationPeriod?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  contractCancellationPeriod?: string;
}
