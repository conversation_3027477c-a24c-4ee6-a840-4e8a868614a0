import { BadRequestException } from '@nestjs/common';

export const trimStringAndValidateNotEmpty = (value: any, propName: string): string => {
  if (typeof value === 'string') {
    const trimmedValue = value.trim();
    if (trimmedValue === "''" || trimmedValue.length === 0) {
      throw new BadRequestException(`${propName} must not be empty!`);
    }
    return trimmedValue;
  }
  return value;
};
