import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsNumber } from 'class-validator';
import { processNumber } from 'src/common/helpers/processNumbers';

export class SubmitSignatureDto {
  @ApiProperty({ type: String, required: true })
  @IsString()
  @IsNotEmpty()
  signature: string;

  @ApiProperty({ type: Number, required: true })
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  jobId: number;
}
