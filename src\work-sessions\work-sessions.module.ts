import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkSession } from './entities/work-session.entity';
import { WorkSessionsController } from './v1/work-sessions.controller';
import { WorkSessionsService } from './v1/work-sessions.service';

@Module({
  imports: [TypeOrmModule.forFeature([WorkSession])],
  controllers: [WorkSessionsController],
  providers: [WorkSessionsService],
  exports: [WorkSessionsService],
})
export class WorkSessionsModule {}
