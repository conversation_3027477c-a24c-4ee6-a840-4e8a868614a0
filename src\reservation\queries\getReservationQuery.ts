const joins = `
LEFT JOIN "job" AS "job" ON "reservation"."jobId" = "job"."id"
LEFT JOIN "vehicle" AS "vehicle" ON "reservation"."vehicleId" = "vehicle"."id"
LEFT JOIN "user" AS "operator" ON "reservation"."operatorId" = "operator"."id"
LEFT JOIN "user" AS "manager" ON "reservation"."managerId" = "manager"."id"
LEFT JOIN "user" AS "dispatcher" ON "reservation"."dispatcherId" = "dispatcher"."id"
LEFT JOIN "pricelist" ON "reservation"."pricelistId" = "pricelist"."id"
LEFT JOIN "company" AS "operatorCompany" ON "manager"."companyId" = "operatorCompany"."id"
LEFT JOIN "company" AS "dispatcherCompany" ON "dispatcher"."companyId" = "dispatcherCompany"."id"

`;

const groupBy = `GROUP BY 
"reservation"."id",
"job"."id",
"vehicle"."id",
"operator"."id",
"manager"."id",
"dispatcher"."id",
"pricelist"."id",
"operatorCompany"."id",
"dispatcherCompany"."id"
`;

export const getReservationRawQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
) => {
  return `
  SELECT 
    "reservation".*,
    CASE 
      WHEN "reservation"."location" IS NOT NULL THEN "reservation"."location"::jsonb
      ELSE NULL
    END AS "location",
    CASE 
      WHEN "reservation"."clientDetails" IS NOT NULL THEN "reservation"."clientDetails"::jsonb
      ELSE NULL
    END AS "clientDetails",    
    CASE 
    WHEN "job"."id" IS NULL THEN NULL
    ELSE 
        to_jsonb("job")
        || jsonb_build_object('report', ("job"."report")::jsonb)
        || jsonb_build_object('jobEvents', (
            SELECT jsonb_agg("job_event")
            FROM "job_event"
            WHERE "job_event"."jobId" = "job"."id"
        ))
    END AS "job",
    CASE WHEN "vehicle"."id" IS NULL THEN NULL
    ELSE jsonb_build_object(
      'id', "vehicle"."id",
      'created_at', "vehicle"."created_at",
      'updated_at', "vehicle"."updated_at",
      'deleted_at', "vehicle"."deleted_at",
      'created_by', "vehicle"."created_by",
      'operatorId', "vehicle"."operatorId",
      'managerId', "vehicle"."managerId",
      'boomSize', "vehicle"."boomSize",
      'vehicleBrand', "vehicle"."vehicleBrand",
      'brandModel', "vehicle"."brandModel",
      'typeOfMotorization', "vehicle"."typeOfMotorization",
      'licensePlateNumber', "vehicle"."licensePlateNumber",
      'weight', "vehicle"."weight",
      'height', "vehicle"."height",
      'length', "vehicle"."length",
      'width', "vehicle"."width",
      'maxVerticalReach', "vehicle"."maxVerticalReach",
      'maxHorizontalReach', "vehicle"."maxHorizontalReach",
      'endHoseLength', "vehicle"."endHoseLength",
      'maxFlowRate', "vehicle"."maxFlowRate",
      'maxConcretePressure', "vehicle"."maxConcretePressure",
      'availableFlexiblePipeLength80Mm', "vehicle"."availableFlexiblePipeLength80Mm",
      'availableFlexiblePipeLength90Mm', "vehicle"."availableFlexiblePipeLength90Mm",
      'availableFlexiblePipeLength100Mm', "vehicle"."availableFlexiblePipeLength100Mm",
      'availableFlexiblePipeLength120Mm', "vehicle"."availableFlexiblePipeLength120Mm",
      'maxDownwardReach', "vehicle"."maxDownwardReach",
      'numberOfBoomSections', "vehicle"."numberOfBoomSections",
      'minUnfoldingHeight', "vehicle"."minUnfoldingHeight",
      'boomRotation', "vehicle"."boomRotation",
      'frontOutriggerSpan', "vehicle"."frontOutriggerSpan",
      'rearOutriggerSpan', "vehicle"."rearOutriggerSpan",
      'frontPressureOnOutrigger', "vehicle"."frontPressureOnOutrigger",
      'rearPressureOnOutrigger', "vehicle"."rearPressureOnOutrigger",
      'boomUnfoldingSystem', "vehicle"."boomUnfoldingSystem",
      'bacExit', "vehicle"."bacExit",
      'bacExitReverse', "vehicle"."bacExitReverse",
      'siteAddress', "vehicle"."siteAddress",
      'hasStrangler', "vehicle"."hasStrangler",
      'frontSideOpening', "vehicle"."frontSideOpening",
      'rearSideOpening', "vehicle"."rearSideOpening",
      'invoicingPipesFrom', "vehicle"."invoicingPipesFrom",
      'pipeLengthForSecondTechnician', "vehicle"."pipeLengthForSecondTechnician",
      'location', 
        CASE 
            WHEN "vehicle"."location" IS NOT NULL 
            THEN ("vehicle"."location")::jsonb 
            ELSE NULL
        END,
      'uniqueIdentificationNumber', "vehicle"."uniqueIdentificationNumber",
      'completedJobs', "vehicle"."completedJobs",
      'type', "vehicle"."type"
    ) END AS "vehicle",
    CASE WHEN "operator"."id" IS NULL THEN NULL
    ELSE jsonb_build_object(
      'id', "operator"."id",
      'created_at', "operator"."created_at",
      'updated_at', "operator"."updated_at",
      'deleted_at', "operator"."deleted_at",
      'created_by', "operator"."created_by",
      'name', "operator"."firstName",
      'surname', "operator"."lastName",
      'email', "operator"."email",
      'birthdate', "operator"."birthdate",
      'address', "operator"."address",
      'secondaryAddress', "operator"."secondaryAddress",
      'phoneNumber', "operator"."phoneNumber",
      'phoneNumberPersonal', "operator"."phoneNumberPersonal",
      'country', "operator"."country",
      'city', "operator"."city",
      'zipCode', "operator"."zipCode",
      'status', "operator"."status",
      'role', "operator"."role",
      'companyId', "operator"."companyId"
    ) END AS "operator",
    CASE WHEN "manager"."id" IS NULL THEN NULL
    ELSE jsonb_build_object(
      'id', "manager"."id",
      'created_at', "manager"."created_at",
      'updated_at', "manager"."updated_at",
      'deleted_at', "manager"."deleted_at",
      'created_by', "manager"."created_by",
      'name', "manager"."firstName",
      'surname', "manager"."lastName",
      'email', "manager"."email",
      'birthdate', "manager"."birthdate",
      'address', "manager"."address",
      'secondaryAddress', "manager"."secondaryAddress",
      'phoneNumber', "manager"."phoneNumber",
      'phoneNumberPersonal', "manager"."phoneNumberPersonal",
      'country', "manager"."country",
      'city', "manager"."city",
      'zipCode', "manager"."zipCode",
      'status', "manager"."status",
      'role', "manager"."role",
      'companyId', "manager"."companyId",
      'company',
       CASE 
        WHEN "operatorCompany"."id" IS NULL THEN NULL
        ELSE jsonb_build_object(
            'id', "operatorCompany"."id",
            'name', "operatorCompany"."name",
            'type', "operatorCompany"."type",
            'contactEmail', "operatorCompany"."contactEmail",
            'billingEmail', "operatorCompany"."billingEmail",
            'planningEmail', "operatorCompany"."planningEmail",
            'phoneNumber', "operatorCompany"."phoneNumber",
            'address', "operatorCompany"."address",
            'secondaryAddress', "operatorCompany"."secondaryAddress",
            'country', "operatorCompany"."country",
            'city', "operatorCompany"."city",
            'zipCode', "operatorCompany"."zipCode",
            'vatNumber', "operatorCompany"."vatNumber",
            'taxes', "operatorCompany"."taxes"
    )
        END
    ) END AS "manager",
    CASE WHEN "dispatcher"."id" IS NULL THEN NULL
    ELSE jsonb_build_object(
      'id', "dispatcher"."id",
      'created_at', "dispatcher"."created_at",
      'updated_at', "dispatcher"."updated_at",
      'deleted_at', "dispatcher"."deleted_at",
      'created_by', "dispatcher"."created_by",
      'name', "dispatcher"."firstName",
      'surname', "dispatcher"."lastName",
      'email', "dispatcher"."email",
      'birthdate', "dispatcher"."birthdate",
      'address', "dispatcher"."address",
      'secondaryAddress', "dispatcher"."secondaryAddress",
      'phoneNumber', "dispatcher"."phoneNumber",
      'phoneNumberPersonal', "dispatcher"."phoneNumberPersonal",
      'country', "dispatcher"."country",
      'city', "dispatcher"."city",
      'zipCode', "dispatcher"."zipCode",
      'status', "dispatcher"."status",
      'role', "dispatcher"."role",
      'companyId', "dispatcher"."companyId",
      'company',
       CASE 
        WHEN "dispatcherCompany"."id" IS NULL THEN NULL
        ELSE jsonb_build_object(
            'id', "dispatcherCompany"."id",
            'name', "dispatcherCompany"."name"
        )
        END
    ) END AS "dispatcher",
    CASE WHEN "pricelist"."id" IS NULL THEN NULL
    ELSE jsonb_build_object(
      'id', "pricelist"."id",
      'created_at', "pricelist"."created_at",
      'updated_at', "pricelist"."updated_at",
      'deleted_at', "pricelist"."deleted_at",
      'created_by', "pricelist"."created_by",
      'title', "pricelist"."title",
      'packageFlatFee', "pricelist"."packageFlatFee",
      'pricePerMeterPumped', "pricelist"."pricePerMeterPumped",
      'additionalHour', "pricelist"."additionalHour",
      'cleaningFee', "pricelist"."cleaningFee",
      'pricePerMeterOfFlexiblePipeLength80Mm', "pricelist"."pricePerMeterOfFlexiblePipeLength80Mm",
      'pricePerMeterOfFlexiblePipeLength90Mm', "pricelist"."pricePerMeterOfFlexiblePipeLength90Mm",
      'pricePerMeterOfFlexiblePipeLength100Mm', "pricelist"."pricePerMeterOfFlexiblePipeLength100Mm",
      'pricePerMeterOfRigidPipeLength120Mm', "pricelist"."pricePerMeterOfRigidPipeLength120Mm",
      'supplyOfTheChemicalSlushie', "pricelist"."supplyOfTheChemicalSlushie",
      'barbotine', "pricelist"."barbotine",
      'extraCementBagPrice', "pricelist"."extraCementBagPrice",
      'packageFlatFeeDuration', "pricelist"."packageFlatFeeDuration",
      'pipeInvoicingStartsFrom', "pricelist"."pipeInvoicingStartsFrom",
      'containerId', "pricelist"."containerId",
      'pumpTiers', "pricelist"."pumpTiers",
      'transportRates', "pricelist"."transportRates",
      'secondTechnicianHourFee', "pricelist"."secondTechnicianHourFee",
      'dayContractFee', "pricelist"."dayContractFee",
      'dayContractDuration', "pricelist"."dayContractDuration",
      'dayContractOvertimeRate', "pricelist"."dayContractOvertimeRate",
      'packageFlatFeeWeekend', "pricelist"."packageFlatFeeWeekend",
      'additionalHourWeekend', "pricelist"."additionalHourWeekend",
      'packageFlatFeeNight', "pricelist"."packageFlatFeeNight",
      'additionalHourNight', "pricelist"."additionalHourNight",
      'minimumChargeFlatFee', "pricelist"."minimumChargeFlatFee",
      'minimumM3Charged', "pricelist"."minimumM3Charged",
      'dayCancellationFee', "pricelist"."dayCancellationFee",
      'cancellationFee', "pricelist"."cancellationFee",
      'secondTechnicianHourFeeWeekend', "pricelist"."secondTechnicianHourFeeWeekend",
      'secondTechnicianHourFeeNight', "pricelist"."secondTechnicianHourFeeNight",
      'vat', "pricelist"."vat"
    ) END AS "pricelist"
  FROM "reservation"
  ${joins}
  ${whereQuery}
  ${groupBy}
  ${querySort}
  ${queryLimit}`;
};

export const getReservationCountQuery = (whereQuery: string) => {
  return `
    SELECT
      COUNT(DISTINCT "reservation"."id") AS "totalCount"
    FROM "reservation"
    ${joins}
    ${whereQuery}
    LIMIT 1;
  `;
};
