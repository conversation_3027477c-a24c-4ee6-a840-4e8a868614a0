import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  UseInterceptors,
  UploadedFiles,
  Put,
} from '@nestjs/common';
import { JobService } from './job.service';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { SecurityValidationDto } from './dto/security-validation.dto';
import { ReportDto } from './dto/report.dto';
import { FileValidationPipe } from '../pipes/file-validation.pipe';
import { GetJobsDto } from './dto/get-jobs.dto';

@ApiTags('Jobs')
@Controller('job')
export class JobController {
  constructor(private readonly jobService: JobService) {}

  @Post()
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async create(@Body() createJobDto: CreateJobDto, @Req() req) {
    return this.jobService.create(createJobDto, req?.user?.sub);
  }

  @Post('get')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  findAll(@Body() body: GetJobsDto, @Req() req) {
    return this.jobService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  findOne(@Param('id') id: string) {
    return this.jobService.findOneById(+id);
  }

  @Patch(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async update(
    @UploadedFiles(new FileValidationPipe()) files: Record<string, Express.Multer.File[]>,
    @Param('id') id: string,
    @Body() updateJobDto: UpdateJobDto,
    @Req() req,
  ) {
    return this.jobService.update(+id, updateJobDto, req?.user?.sub);
  }

  @Delete(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  remove(@Param('id') id: string) {
    return this.jobService.remove(+id);
  }

  @Put('report')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  updateReport(@Body() reportDto: ReportDto, @Req() req) {
    return this.jobService.updateReport(reportDto, req?.user?.sub);
  }

  @Put('security-validation')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'terrainStabilityFile', maxCount: 1 },
      { name: 'parkingComplianceFile', maxCount: 1 },
      { name: 'accessComplianceFile', maxCount: 1 },
      { name: 'electricalRiskFile', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  async updateSecurityValidation(
    @UploadedFiles(new FileValidationPipe()) files: Record<string, Express.Multer.File[]>,
    @Body() securityValidationDto: SecurityValidationDto,
    @Req() req,
  ) {
    await this.handleFiles(securityValidationDto, files);

    return this.jobService.updateSecurityValidation(securityValidationDto, req?.user?.sub);
  }

  private async handleFiles<T extends CreateJobDto | UpdateJobDto | SecurityValidationDto>(
    jobDto: T,
    files?: Record<string, Express.Multer.File[]>,
  ) {
    files = files || {};

    const fileFields = {
      terrainStabilityFile: 'terrainStabilityFile',
      parkingComplianceFile: 'parkingComplianceFile',
      accessComplianceFile: 'accessComplianceFile',
      electricalRiskFile: 'electricalRiskFile',
    };

    for (const [key, field] of Object.entries(fileFields)) {
      if (files[key] && files[key].length) {
        jobDto[field] = files[key][0]; // Assign only the first file
      }
    }
  }
}
