import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsISO8601, IsOptional } from 'class-validator';
import { CreateContractDto } from './create-contract.dto';

export class UpdateContractDto extends PartialType(CreateContractDto) {
  @ApiProperty({ required: false })
  @IsISO8601()
  @IsOptional()
  suspensionStart?: Date;

  @ApiProperty({ required: false })
  @IsISO8601()
  @IsOptional()
  suspensionEnd?: Date;
}
