import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1715800124505 implements MigrationInterface {
  name = 'Migrations1715800124505';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "flowRate" integer`);
    await queryRunner.query(`ALTER TABLE "job" ADD "balance" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "balance"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "flowRate"`);
  }
}
