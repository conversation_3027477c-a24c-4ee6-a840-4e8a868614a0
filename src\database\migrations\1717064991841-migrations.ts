import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1717064991841 implements MigrationInterface {
  name = 'Migrations1717064991841';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_55b219065150443755f93d2671" ON "vehicle" ("operatorId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5bcee0af773f5c5c6efc07ef18" ON "vehicle" ("managerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b639b0e8a6d8095d93f7397adf" ON "vehicle" ("pricelistId") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_e5c16596d14b9cb2527e8e4633" ON "vehicle" ("type") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_849e0cc4d98a094f3b21a01578" ON "vehicle" ("licensePlateNumber") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d9125a9de8846cc21e701e6a99" ON "vehicle" ("completedJobs") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_d9125a9de8846cc21e701e6a99"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_849e0cc4d98a094f3b21a01578"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e5c16596d14b9cb2527e8e4633"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b639b0e8a6d8095d93f7397adf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5bcee0af773f5c5c6efc07ef18"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_55b219065150443755f93d2671"`);
  }
}
