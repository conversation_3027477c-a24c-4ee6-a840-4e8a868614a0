import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712871516881 implements MigrationInterface {
  name = 'Migrations1712871516881';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "adminCleanup"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingPermitAcquiredPath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingPermitAcquiredKey" character varying`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "localAdministrationAuthorizationPath" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD "localAdministrationAuthorizationKey" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "trafficPlanPath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "trafficPlanKey" character varying`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "isElectricalRisk" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "electricalRiskKey" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "electricalRiskPath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "electricalRiskComment" character varying`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "isAccessCompliance" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "accessComplianceKey" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "accessCompliancePath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "accessComplianceComment" character varying`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "isParkingCompliance" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingComplianceKey" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingCompliancePath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingComplianceComment" character varying`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "isTerrainStability" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "terrainStabilityKey" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "terrainStabilityPath" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "terrainStabilityComment" character varying`);
    await queryRunner.query(`ALTER TABLE "job" ADD "report" text`);
    await queryRunner.query(
      `ALTER TYPE "public"."job_status_enum" RENAME TO "job_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."job_status_enum" AS ENUM('NOT_STARTED', 'DRIVING_TO_SITE', 'SITE_ARRIVAL', 'SECURITY_VALIDATION', 'START_SETUP', 'END_SETUP', 'START_PUMPING', 'END_PUMPING', 'REPORT', 'SIGNATURE', 'START_CLEANUP', 'END_CLEANUP', 'LEAVE_SITE', 'COMPLETE', 'CANCELLED')`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" TYPE "public"."job_status_enum" USING "status"::"text"::"public"."job_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "status" SET DEFAULT 'NOT_STARTED'`);
    await queryRunner.query(`DROP TYPE "public"."job_status_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."job_status_enum_old" AS ENUM('NOT_STARTED', 'DRIVING_TO_SITE', 'SECURITY_VALIDATION', 'SETUP', 'PUMPING', 'ADMIN_CLEANUP', 'SIGNATURE', 'LEAVE_SITE', 'COMPLETE', 'CANCELLED')`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "status" TYPE "public"."job_status_enum_old" USING "status"::"text"::"public"."job_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "job" ALTER COLUMN "status" SET DEFAULT 'NOT_STARTED'`);
    await queryRunner.query(`DROP TYPE "public"."job_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."job_status_enum_old" RENAME TO "job_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "report"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "terrainStabilityComment"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "terrainStabilityPath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "terrainStabilityKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "isTerrainStability"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingComplianceComment"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingCompliancePath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingComplianceKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "isParkingCompliance"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "accessComplianceComment"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "accessCompliancePath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "accessComplianceKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "isAccessCompliance"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "electricalRiskComment"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "electricalRiskPath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "electricalRiskKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "isElectricalRisk"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "trafficPlanKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "trafficPlanPath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "localAdministrationAuthorizationKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "localAdministrationAuthorizationPath"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingPermitAcquiredKey"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingPermitAcquiredPath"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "adminCleanup" text`);
  }
}
