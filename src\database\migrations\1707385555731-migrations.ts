import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1707385555731 implements MigrationInterface {
  name = 'Migrations1707385555731';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "pricelist" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "title" character varying, "packageFlatFee" integer, "pricePerMeterPumped" integer, "additionalHour" integer, "cleaningFee" integer, "pricePerMeterOfFlexiblePipeLength80Mm" integer, "pricePerMeterOfFlexiblePipeLength90Mm" integer, "pricePerMeterOfFlexiblePipeLength100Mm" integer, "pricePerMeterOfRigidPipeLength120Mm" integer, "extraCementBagPrice" integer, CONSTRAINT "PK_2ae09574182aa8ed4de3ae2009c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "pricelist_dispatcher_managers_user" ("pricelistId" integer NOT NULL, "userId" integer NOT NULL, CONSTRAINT "PK_cdedcf425236900ee04cd8baa6e" PRIMARY KEY ("pricelistId", "userId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7642988648d8d6c3f0e6eef5fa" ON "pricelist_dispatcher_managers_user" ("pricelistId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_41a0f9931a4a994be6443f8298" ON "pricelist_dispatcher_managers_user" ("userId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist_dispatcher_managers_user" ADD CONSTRAINT "FK_7642988648d8d6c3f0e6eef5fac" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist_dispatcher_managers_user" ADD CONSTRAINT "FK_41a0f9931a4a994be6443f82986" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist_dispatcher_managers_user" DROP CONSTRAINT "FK_41a0f9931a4a994be6443f82986"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist_dispatcher_managers_user" DROP CONSTRAINT "FK_7642988648d8d6c3f0e6eef5fac"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_41a0f9931a4a994be6443f8298"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7642988648d8d6c3f0e6eef5fa"`);
    await queryRunner.query(`DROP TABLE "pricelist_dispatcher_managers_user"`);
    await queryRunner.query(`DROP TABLE "pricelist"`);
  }
}
