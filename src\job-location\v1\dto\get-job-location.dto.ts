import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsOptional, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { transformFriendlyCategory } from 'src/common/utility/transform-category.util';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';

export enum JobLocationCategoryTypes {
  jobLocationId = '"job_location"."id"',
  status = '"job_location"."status"',
  state = '"job_location"."state"',
  progress = '"job_location"."progress"',
  coords = '"job_location"."coords"',
  jobId = '"job_location"."jobId"',
}

export class JobLocationExpression extends Expression {
  @IsOptional()
  @IsEnum(JobLocationCategoryTypes, {
    message: 'invalid filter category for JobLocation',
  })
  @Transform(({ value }) => transformFriendlyCategory(value, JobLocationCategoryTypes))
  category?: string | null | undefined;
}

export class JobLocationSortItem extends SortItem {
  @IsEnum(JobLocationCategoryTypes, {
    message: 'invalid sort field for JobLocation',
  })
  @Transform(({ value }) => transformFriendlyCategory(value, JobLocationCategoryTypes))
  field: string;
}

export class GetJobLocationDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: JobLocationExpression })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JobLocationExpression)
  expressions: JobLocationExpression[] = [];

  @ApiProperty({ required: true, isArray: true, type: JobLocationSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JobLocationSortItem)
  sortModel: JobLocationSortItem[] = [];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
