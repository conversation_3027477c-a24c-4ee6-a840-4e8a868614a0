import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Param,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { WorkSessionsService } from './work-sessions.service';
import { StartWorkSessionDto } from './dto/start-work-session.dto';
import { EndWorkSessionDto } from './dto/end-work-session.dto';
import { StartBreakDto } from './dto/start-break.dto';
import { EndBreakDto } from './dto/end-break.dto';
import { GetWorkSessionsDto } from './dto/get-work-sessions.dto';
import { AuthGuard } from '../../common/guards/auth.guard';

@ApiTags('work-sessions')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('v1/work-sessions')
export class WorkSessionsController {
  constructor(private readonly workSessionsService: WorkSessionsService) {}

  @Post('start')
  @ApiOperation({ summary: 'Start a new work session' })
  async startWorkSession(@Body() dto: StartWorkSessionDto) {
    return await this.workSessionsService.startWorkSession(dto);
  }

  @Post('start-break')
  @ApiOperation({ summary: 'Start a break during work session' })
  async startBreak(@Body() dto: StartBreakDto) {
    return await this.workSessionsService.startBreak(dto);
  }

  @Post('end-break')
  @ApiOperation({ summary: 'End the current break' })
  async endBreak(@Body() dto: EndBreakDto) {
    return await this.workSessionsService.endBreak(dto);
  }

  @Post('end')
  @ApiOperation({ summary: 'End the work session' })
  async endWorkSession(@Body() dto: EndWorkSessionDto) {
    return await this.workSessionsService.endWorkSession(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get work sessions with filters' })
  async getWorkSessions(@Query() dto: GetWorkSessionsDto) {
    return await this.workSessionsService.getWorkSessions(dto);
  }

  @Get('active/:operatorId')
  @ApiOperation({ summary: 'Get active session for operator' })
  async getActiveSession(@Param('operatorId') operatorId: number) {
    return await this.workSessionsService.getActiveSession(operatorId);
  }

  @Post(':sessionId/add-job/:jobId')
  @ApiOperation({ summary: 'Add job to work session' })
  async addJobToSession(
    @Param('sessionId') sessionId: number,
    @Param('jobId') jobId: number,
  ) {
    return await this.workSessionsService.addJobToSession(sessionId, jobId);
  }
}
