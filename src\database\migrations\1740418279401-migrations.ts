import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1740418279401 implements MigrationInterface {
  name = 'Migrations1740418279401';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659"`,
    );
    await queryRunner.query(`ALTER TABLE "contract" ADD "vehicleId" integer NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "contract" ADD "startDate" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "contract" ADD "endDate" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "dispatcherCompanyId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "operatorCompanyId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "pricelistId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_62dd5af91a0d20d09486118cca0" FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2" FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_c58208881a665f08e5e8d25ff1f" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_c58208881a665f08e5e8d25ff1f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_62dd5af91a0d20d09486118cca0"`,
    );
    await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "pricelistId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "contract" ALTER COLUMN "operatorCompanyId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ALTER COLUMN "dispatcherCompanyId" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "endDate"`);
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "startDate"`);
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "vehicleId"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_15bfcb8f8de8a202405bbf5ffa"`);
  }
}
