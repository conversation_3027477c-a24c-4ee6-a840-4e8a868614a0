import { ApiProperty } from '@nestjs/swagger';
import { IsISO8601, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Transform, Type } from 'class-transformer';
import { processNumber } from 'src/common/helpers/processNumbers';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';
import { SearchVehicleType } from 'src/common/types/search-vehicle.type';

export class GetVehiclesSearchDto extends CommonQueryParams {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'siteAddress'))
  readonly siteAddress?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'operatorName'))
  readonly operatorName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'clientName'))
  readonly clientName?: string;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsOptional()
  readonly coordinates?: number[] | null;

  @ApiProperty({ required: false, description: 'Radius to search nearby locations example: 5000' })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  readonly radius?: number = 20000;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly boomSize?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly availableFlexiblePipeLength80Mm?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly availableFlexiblePipeLength90Mm?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly availableFlexiblePipeLength100Mm?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly availableFlexiblePipeLength120Mm?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly frontOutriggerSpan?: number;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly rearOutriggerSpan?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsISO8601()
  readonly dateFrom?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsISO8601()
  readonly dateTo?: Date;

  @ApiProperty({ required: false })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  readonly operatorManagerId?: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsOptional()
  readonly operatorManagerIds?: number[] | null;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsOptional()
  readonly operatorCompanyIds?: number[] | null;

  @ApiProperty({ type: Number, required: false })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  readonly dispatcherCompanyId?: number | null;

  @ApiProperty({ required: true })
  readonly searchType: SearchVehicleType = 'All';
}
