import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1710372798524 implements MigrationInterface {
  name = 'Migrations1710372798524';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "rigidPipeLength100Mm" double precision`);
    await queryRunner.query(`ALTER TABLE "job" ADD "rigidPipeLength120Mm" double precision`);
    await queryRunner.query(`UPDATE "job" SET "rigidPipeLength100Mm" = "rigidPipeLength"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "rigidPipeLength"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "rigidPipeLength" double precision`);
    await queryRunner.query(`UPDATE "job" SET "rigidPipeLength" = "rigidPipeLength100Mm"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "rigidPipeLength120Mm"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "rigidPipeLength100Mm"`);
  }
}
