import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1717329361277 implements MigrationInterface {
  name = 'Migrations1717329361277';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "favorite" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "dispatcherManagerId" integer, "operatorManagerId" integer, CONSTRAINT "PK_495675cec4fb09666704e4f610f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."contract_status_enum" AS ENUM('approved', 'pending', 'declined')`,
    );
    await queryRunner.query(
      `CREATE TABLE "contract" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "status" "public"."contract_status_enum" NOT NULL, "dispatcherManagerId" integer, "operatorManagerId" integer, "pricelistId" integer, CONSTRAINT "PK_17c3a89f58a2997276084e706e8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "favorites"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "contracted"`);
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_ea78f3aadec5197454998ca2ee5" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_fa43f7b3050b011dd02fc5aa2b3" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_346103d35cbd1a48521384a81b4" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_9d9b187fe55ae269d29492eec1f" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_9d9b187fe55ae269d29492eec1f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_346103d35cbd1a48521384a81b4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_fa43f7b3050b011dd02fc5aa2b3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_ea78f3aadec5197454998ca2ee5"`,
    );
    await queryRunner.query(`ALTER TABLE "user" ADD "contracted" jsonb`);
    await queryRunner.query(`ALTER TABLE "user" ADD "favorites" jsonb`);
    await queryRunner.query(`DROP TABLE "contract"`);
    await queryRunner.query(`DROP TYPE "public"."contract_status_enum"`);
    await queryRunner.query(`DROP TABLE "favorite"`);
  }
}
