import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1726698116215 implements MigrationInterface {
  name = 'Migrations1726698116215';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "vehicle_type" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "name" character varying NOT NULL, "pricelistId" integer, "operatorManagerId" integer NOT NULL, CONSTRAINT "PK_465137c10960b54f82f1b145e43" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_972be84035335f3a9c1e8db66e" ON "vehicle_type" ("name") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2a58f8a08911fb8e8048d1cb11" ON "vehicle_type" ("operatorManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_07a7783d640995e2120c29ed15" ON "vehicle_type" ("name", "operatorManagerId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" ADD CONSTRAINT "FK_2a58f8a08911fb8e8048d1cb114" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" ADD CONSTRAINT "FK_691cfe437aaf4c9ecb3f67cb2cc" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" DROP CONSTRAINT "FK_691cfe437aaf4c9ecb3f67cb2cc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" DROP CONSTRAINT "FK_2a58f8a08911fb8e8048d1cb114"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_07a7783d640995e2120c29ed15"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2a58f8a08911fb8e8048d1cb11"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_972be84035335f3a9c1e8db66e"`);
    await queryRunner.query(`DROP TABLE "vehicle_type"`);
  }
}
