import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1735149819707 implements MigrationInterface {
  name = 'Migrations1735149819707';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" DROP CONSTRAINT "FK_423f522201353c94c687a4211fb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" DROP CONSTRAINT "FK_dad3512819f9062d1379016711b"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_423f522201353c94c687a4211f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4ac13693574655eefccfe32334"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c52ab39d2e388db5d03571b739"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dad3512819f9062d1379016711"`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "dateFrom"`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "dateTo"`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "operatorManagerId"`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "dispatcherManagerId"`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" ADD "operatorCompanyId" integer`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" ADD "dispatcherCompanyId" integer`);
    await queryRunner.query(
      `CREATE INDEX "IDX_ae6a14f44c2ace64aee813f0ac" ON "bulk_invoice_log" ("operatorCompanyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_89d01b127f21952f54c13fb51b" ON "bulk_invoice_log" ("dispatcherCompanyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9c7bcfb3f12456f7c6312b5c8d" ON "bulk_invoice_log" ("operatorCompanyId", "dispatcherCompanyId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD CONSTRAINT "FK_ae6a14f44c2ace64aee813f0ac9" FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD CONSTRAINT "FK_89d01b127f21952f54c13fb51bf" FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" DROP CONSTRAINT "FK_89d01b127f21952f54c13fb51bf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" DROP CONSTRAINT "FK_ae6a14f44c2ace64aee813f0ac9"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_9c7bcfb3f12456f7c6312b5c8d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_89d01b127f21952f54c13fb51b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ae6a14f44c2ace64aee813f0ac"`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "dispatcherCompanyId"`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" DROP COLUMN "operatorCompanyId"`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" ADD "dispatcherManagerId" integer`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" ADD "operatorManagerId" integer`);
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" ADD "dateTo" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD "dateFrom" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dad3512819f9062d1379016711" ON "bulk_invoice_log" ("operatorManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c52ab39d2e388db5d03571b739" ON "bulk_invoice_log" ("dateFrom", "dateTo") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4ac13693574655eefccfe32334" ON "bulk_invoice_log" ("operatorManagerId", "dispatcherManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_423f522201353c94c687a4211f" ON "bulk_invoice_log" ("dispatcherManagerId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD CONSTRAINT "FK_dad3512819f9062d1379016711b" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bulk_invoice_log" ADD CONSTRAINT "FK_423f522201353c94c687a4211fb" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
