import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1724504525994 implements MigrationInterface {
  name = 'Migrations1724504525994';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job_location" DROP CONSTRAINT "FK_0b31c3a9761f15fb173df4254d1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location" ADD CONSTRAINT "FK_0b31c3a9761f15fb173df4254d1" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job_location" DROP CONSTRAINT "FK_0b31c3a9761f15fb173df4254d1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location" ADD CONSTRAINT "FK_0b31c3a9761f15fb173df4254d1" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
