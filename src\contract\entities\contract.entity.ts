import { BaseEntity } from '../../common/entities/base.entity';
import { Entity, Column, ManyToOne, JoinColumn, Index, OneToMany } from 'typeorm';
import { Pricelist } from 'src/pricelist/entities/pricelist.entity';
import { ContractedStatus } from 'src/common/constants/user.enum';
import { ContractComment } from './contract-comment.entity';
import { Vehicle } from 'src/vehicle/entities/vehicle.entity';
import { Company } from 'src/company/entities/company.entity';
import { SuspensionPeriod } from './suspension-period.entity';

@Entity()
export class Contract extends BaseEntity {
  @Column()
  @Index()
  dispatcherCompanyId: number;

  @Column()
  @Index()
  operatorCompanyId: number;

  @Column()
  vehicleId: number;

  @Column()
  pricelistId: number;

  @Column({
    type: 'enum',
    enum: ContractedStatus,
    default: ContractedStatus.PENDING,
  })
  status: ContractedStatus;

  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  startDate: Date;

  @Column({ nullable: true })
  endDate?: Date;

  @ManyToOne(() => Company, company => company.dispatcherContracts, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'dispatcherCompanyId' })
  dispatcherCompany: Company;

  @ManyToOne(() => Company, company => company.operatorContracts, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'operatorCompanyId' })
  operatorCompany: Company;

  @ManyToOne(() => Vehicle, vehicle => vehicle, {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'vehicleId' })
  vehicle: Vehicle;

  @ManyToOne(() => Pricelist, pricelist => pricelist, {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'pricelistId' })
  pricelist: Pricelist;

  @OneToMany(() => ContractComment, comment => comment.contract)
  comments: ContractComment[];

  @OneToMany(() => SuspensionPeriod, entity => entity.contract, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  suspensionPeriods?: SuspensionPeriod[];
}
