import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreateUnavailablePeriodDto } from './dto/create-unavailable-period.dto';
import { UpdateUnavailablePeriodDto } from './dto/update-unavailable-period.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { UnavailablePeriod } from '../entities/unavailable-period.entity';
import { DataSource, Repository } from 'typeorm';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { UsersService } from 'src/users/v1/users.service';
import { Status } from 'src/common/constants/status.enum';
import {
  GetUnavailablePeriodsDto,
  UnavailablePeriodCategoryTypes,
  UnavailablePeriodExpression,
} from './dto/get-unavailable-period.dto';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import {
  addExpressions,
  ConditionType,
  getWhereQuery,
  SortDirections,
  getQuerySort,
  getQueryLimit,
  QueryOperator,
} from 'src/libs/helpers/CeQuery';
import {
  getUnavailablePeriodsQuery,
  getUnavailablePeriodsCountQuery,
} from '../queries/get-unavailable-period-query';
import { ReservationService } from 'src/reservation/v1/reservation.service';
import { JobState, JobStatus } from 'src/common/constants/job.enum';
import { VehicleService } from 'src/vehicle/v1/vehicle.service';
import { CreateVehicleUnavailablePeriodDto } from './dto/create-vehicle-unavailable-period.dto';
import { UpdateVehicleUnavailablePeriodDto } from './dto/update-vehicle-unavailable-period.dto';
import { JOBS, QUEUES } from 'src/libs/bullmq/constants';
import { Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { ReservationCategoryTypes } from 'src/reservation/v1/dto/get-reservation.dto';

@Injectable()
export class UnavailablePeriodService {
  private readonly logger = new Logger(UnavailablePeriodService.name);

  constructor(
    @InjectRepository(UnavailablePeriod)
    private unavailablePeriodRepository: Repository<UnavailablePeriod>,
    @Inject(forwardRef(() => UsersService))
    private usersService: UsersService,
    private dataSource: DataSource,
    private reservationService: ReservationService,
    @Inject(forwardRef(() => VehicleService))
    private vehicleService: VehicleService,
    @InjectQueue(QUEUES.UNAVAILABLE_PERIOD_STATUS_UPDATE) private statusUpdateQueue: Queue,
  ) {}

  async create(
    createUnavailablePeriodDto: CreateUnavailablePeriodDto,
    creatingUser: SerializedUser,
  ): Promise<UnavailablePeriod> {
    const userId = creatingUser.sub;
    const companyId = creatingUser.companyId;
    this.logger.log({
      method: 'createUnavailablePeriod',
      creatingUser: userId,
    });

    const operator = await this.usersService.findOneWithVehicles(
      createUnavailablePeriodDto.operatorId,
    );

    const body: GetUnavailablePeriodsDto = {
      expressions: [
        {
          category: UnavailablePeriodCategoryTypes.operatorId,
          operator: '=' as QueryOperator,
          value: createUnavailablePeriodDto.operatorId,
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.from,
          operator: '<' as QueryOperator,
          value: new Date(createUnavailablePeriodDto.to).toISOString(),
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.to,
          operator: '>' as QueryOperator,
          value: new Date(createUnavailablePeriodDto.from).toISOString(),
          conditionType: 'AND',
        },
      ],
      relations: ['operator'],
      limit: 1,
      offset: 0,
      sortModel: [],
    };

    const result = await this.findMany(body, creatingUser, false);
    const [existingUnavailablePeriod] = result.data;

    if (existingUnavailablePeriod) {
      throw new BadRequestException(
        'There is already an active unavailable period for this operator during the specified time.',
      );
    }

    const operatorManager = operator.created_by
      ? await this.usersService.findOneById(operator.created_by)
      : null;

    const vehicles = operator.operatorVehicles || [];
    const vehicleIds = vehicles.map(vehicle => vehicle.id);

    if (vehicleIds.length > 0) {
      const vehicleReservation = await this.reservationService.findMany({
        vehicleIds: vehicleIds,
        job: { state: [JobState.RUNNING, JobState.WAITING] },
        dateFrom: new Date(createUnavailablePeriodDto.from),
        dateTo: new Date(createUnavailablePeriodDto.to),
        expressions: [],
      });

      if (vehicleReservation.totalCount > 0) {
        throw new BadRequestException(
          'One or more vehicles have active or incomplete reservations.',
        );
      }
    }

    const unavailablePeriod = this.unavailablePeriodRepository.create({
      ...createUnavailablePeriodDto,
      created_by: userId,
      operator,
      operatorManager: operatorManager,
      companyId: companyId,
      vehicles: vehicles,
    });

    const savedUnavailablePeriod = await this.unavailablePeriodRepository.save(unavailablePeriod);

    this.scheduleStatusActivation(savedUnavailablePeriod);
    this.scheduleStatusInactivation(savedUnavailablePeriod);

    this.logger.log({
      method: 'createUnavailablePeriod',
      unavailablePeriodId: savedUnavailablePeriod.id,
      creatingUser: userId,
    });
    return savedUnavailablePeriod;
  }

  async createVehiclesUnavailablePeriod(
    createUnavailablePeriodDto: CreateVehicleUnavailablePeriodDto,
    creatingUser: SerializedUser,
  ): Promise<UnavailablePeriod> {
    const userId = creatingUser.sub;
    const companyId = creatingUser.companyId;
    this.logger.log({
      method: 'createVehicleUnavailablePeriod',
      creatingUser: userId,
    });
    const vehicleIds = createUnavailablePeriodDto.vehicleIds;
    const vehicles = await this.vehicleService.findManyByIds(vehicleIds);

    const body: GetUnavailablePeriodsDto = {
      expressions: [
        {
          category: UnavailablePeriodCategoryTypes.vehicleIds,
          operator: 'in' as QueryOperator,
          value: createUnavailablePeriodDto.vehicleIds,
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.from,
          operator: '<' as QueryOperator,
          value: new Date(createUnavailablePeriodDto.to).toISOString(),
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.to,
          operator: '>' as QueryOperator,
          value: new Date(createUnavailablePeriodDto.from).toISOString(),
          conditionType: 'AND',
        },
        {
          category: UnavailablePeriodCategoryTypes.contractId,
          operator: '=' as QueryOperator,
          value: createUnavailablePeriodDto.contractId || null,
          conditionType: 'AND',
        },
      ],
      relations: ['vehicles'],
      limit: 1,
      offset: 0,
      sortModel: [],
    };

    const result = await this.findMany(body, creatingUser, false);
    const [existingUnavailablePeriod] = result.data;

    if (vehicleIds.length === 1 && existingUnavailablePeriod) {
      throw new BadRequestException(
        'There is already an active unavailable period for this vehicle during the specified time.',
      );
    }
    const existingVehicles = existingUnavailablePeriod?.vehicles ?? [];
    const existingVehicleIds = new Set(existingVehicles.map(vehicle => vehicle.id));

    // Filter out vehicleIds that already have unavailable periods
    const newVehicles = vehicles.filter(vehicle => !existingVehicleIds.has(vehicle.id));

    const vehicleReservation = await this.reservationService.findMany({
      vehicleIds: vehicleIds,
      dateFrom: new Date(createUnavailablePeriodDto.from),
      dateTo: new Date(createUnavailablePeriodDto.to),
      expressions: [
        {
          category: ReservationCategoryTypes.jobStatus,
          operator: '!=',
          value: JobStatus.CANCELLED,
          conditionType: 'AND',
        },
      ],
    });

    if (vehicleReservation.totalCount > 0) {
      throw new BadRequestException(
        'Cannot create unavailable period. One or more vehicles have existing reservations during the specified time period.',
      );
    }

    const today = new Date();
    if (
      !createUnavailablePeriodDto.isHidden &&
      today >= new Date(createUnavailablePeriodDto.from) &&
      today <= new Date(createUnavailablePeriodDto.to)
    ) {
      await this.vehicleService.updateVehicleStatus(vehicleIds, Status.INACTIVE);
    }
    // Create new unavailable periods for the remaining vehicleIds
    const unavailablePeriod = this.unavailablePeriodRepository.create({
      ...createUnavailablePeriodDto,
      created_by: userId,
      companyId: companyId,
      vehicles: newVehicles,
    });

    const savedUnavailablePeriod = await this.unavailablePeriodRepository.save(unavailablePeriod);

    // Schedule Vehicle status updates
    this.scheduleStatusActivation(unavailablePeriod);
    this.scheduleStatusInactivation(unavailablePeriod);

    this.logger.log({
      method: 'createUnavailablePeriod',
      unavailablePeriodId: savedUnavailablePeriod.id,
      creatingUser: userId,
    });
    return savedUnavailablePeriod;
  }

  async findMany(
    body: GetUnavailablePeriodsDto,
    fetchingUser?: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<UnavailablePeriod>> {
    this.logger.log({ method: 'findManyUnavailablePeriods' });

    const { limit, offset, sortModel = [], relations = ['vehicles'] } = body;
    const userId = fetchingUser?.sub;
    const companyId = fetchingUser?.companyId;

    const queryRunner = this.dataSource.createQueryRunner('slave');
    const expressionsToAdd: UnavailablePeriodExpression[] = [];
    if (fetchingUser) {
      expressionsToAdd.push({
        category: UnavailablePeriodCategoryTypes.companyId,
        operator: '=',
        value: companyId,
        conditionType: 'AND',
      });
      expressionsToAdd.push({
        category: UnavailablePeriodCategoryTypes.createdBy,
        operator: '=',
        value: userId,
        conditionType: 'OR',
      });
      if (relations.includes('vehicles')) {
        expressionsToAdd.push({
          category: UnavailablePeriodCategoryTypes.vehicleManagerId,
          operator: '=',
          value: userId,
          conditionType: 'OR',
        });
      }
    }

    const expressionsWithOtherData = addExpressions(
      expressionsToAdd,
      body.expressions || [],
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);

    if (!sortModel.length) {
      sortModel.push({ field: '"unavailable_period"."id"', sort: SortDirections.ASC });
    }
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;

    const unavailablePeriodsSQL = getUnavailablePeriodsQuery(
      whereQuery.query,
      querySort,
      queryLimit,
      relations,
    );

    try {
      let totalCount = 0;

      const unavailablePeriods = await queryRunner.manager.query(
        unavailablePeriodsSQL,
        whereQuery.params,
      );

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getUnavailablePeriodsCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw?.[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({
        method: 'findManyUnavailablePeriods',
        query: body,
        unavailablePeriodsLength: unavailablePeriods.length,
      });

      return new EntityWithCountDto<UnavailablePeriod>(
        UnavailablePeriod,
        unavailablePeriods,
        totalCount,
      );
    } catch (e) {
      this.logger.error({
        method: 'findManyUnavailablePeriods',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser?: SerializedUser): Promise<UnavailablePeriod> {
    this.logger.log({ method: 'findOneById', id });

    const body: GetUnavailablePeriodsDto = {
      expressions: [
        {
          category: UnavailablePeriodCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
      ],
      relations: ['operatorManager', 'vehicles', 'operator', 'contract'],
      limit: 1,
      offset: 0,
      sortModel: [],
    };

    const result = await this.findMany(body, fetchingUser, false);
    const unavailablePeriod = result.data[0];

    if (!unavailablePeriod) {
      throw new NotFoundException('Unavailable Period was not found');
    }

    this.logger.log({
      method: 'findOneById',
      id,
      unavailablePeriodId: unavailablePeriod.id,
    });
    return unavailablePeriod;
  }

  async updateOnyById(
    id: number,
    updateUnavailablePeriodDto: UpdateUnavailablePeriodDto,
    updatingUser: SerializedUser,
  ): Promise<UnavailablePeriod> {
    const userId = updatingUser.sub;
    this.logger.log({
      method: 'updateOnyById',
      id,
      updatingUser: userId,
    });

    const unavailablePeriod = await this.findOneById(id, updatingUser);

    const oldFrom = new Date(unavailablePeriod.from).toISOString();
    const oldTo = new Date(unavailablePeriod.to).toISOString();

    const scheduleStatusInactivation =
      updateUnavailablePeriodDto.from &&
      oldFrom !== new Date(updateUnavailablePeriodDto.from).toISOString();
    const scheduleStatusActivation =
      updateUnavailablePeriodDto.to &&
      oldTo !== new Date(updateUnavailablePeriodDto.to).toISOString();

    if (updateUnavailablePeriodDto.operatorId) {
      const operator = await this.usersService.findOneWithVehicles(
        updateUnavailablePeriodDto.operatorId,
      );

      if (operator.id !== updateUnavailablePeriodDto.operatorId) {
        const operatorManager = await this.usersService.findOneById(operator.created_by);

        unavailablePeriod.operatorManager = operatorManager;
      }

      unavailablePeriod.vehicles = operator.operatorVehicles ?? [];
    }

    if (scheduleStatusInactivation) {
      this.scheduleStatusInactivation(unavailablePeriod);
    }
    if (scheduleStatusActivation) {
      this.scheduleStatusActivation(unavailablePeriod);
    }

    Object.assign(unavailablePeriod, updateUnavailablePeriodDto);
    const savedUnavailablePeriod = await this.unavailablePeriodRepository.save(unavailablePeriod);

    this.logger.log({
      method: 'updateOnyById',
      savedUnavailablePeriodId: savedUnavailablePeriod.id,
      updatingUser: userId,
    });
    return savedUnavailablePeriod;
  }

  async updateVehiclesUnavailablePeriod(
    id: number,
    updateVehicleUnavailablePeriod: UpdateVehicleUnavailablePeriodDto,
    updatingUser: SerializedUser,
  ): Promise<UnavailablePeriod> {
    const userId = updatingUser.sub;
    this.logger.log({
      method: 'updateVehiclesUnavailablePeriod',
      vehicleIds: updateVehicleUnavailablePeriod.vehicleIds,
      updatingUser: userId,
    });

    const unavailablePeriod = await this.findOneById(id);

    const oldFrom = unavailablePeriod.from;
    const oldTo = unavailablePeriod.to;

    const scheduleStatusInactivation =
      updateVehicleUnavailablePeriod.from &&
      oldFrom !== new Date(updateVehicleUnavailablePeriod.from);
    const scheduleStatusActivation =
      updateVehicleUnavailablePeriod.to && oldTo !== new Date(updateVehicleUnavailablePeriod.to);

    if (updateVehicleUnavailablePeriod.from || updateVehicleUnavailablePeriod.to) {
      const fromDate = updateVehicleUnavailablePeriod.from
        ? new Date(updateVehicleUnavailablePeriod.from)
        : unavailablePeriod.from;
      const toDate = updateVehicleUnavailablePeriod.to
        ? new Date(updateVehicleUnavailablePeriod.to)
        : unavailablePeriod.to;

      const vehicleIds = unavailablePeriod.vehicles?.map(vehicle => vehicle.id) || [];

      if (vehicleIds.length > 0) {
        const vehicleReservation = await this.reservationService.findMany({
          vehicleIds: vehicleIds,
          dateFrom: fromDate,
          dateTo: toDate,
          expressions: [
            {
              category: ReservationCategoryTypes.jobStatus,
              operator: '!=',
              value: JobStatus.CANCELLED,
              conditionType: 'AND',
            },
          ],
        });

        if (vehicleReservation.totalCount > 0) {
          throw new BadRequestException(
            'Cannot update unavailable period. One or more vehicles have existing reservations during the specified time period.',
          );
        }
      }
    }

    Object.assign(unavailablePeriod, updateVehicleUnavailablePeriod);

    const savedUnavailablePeriod = await this.unavailablePeriodRepository.save(unavailablePeriod);

    if (scheduleStatusInactivation) {
      this.scheduleStatusInactivation(savedUnavailablePeriod);
    }
    if (scheduleStatusActivation) {
      this.scheduleStatusActivation(savedUnavailablePeriod);
    }

    this.logger.log({
      method: 'updateVehiclesUnavailablePeriod',
      savedUnavailablePeriodId: savedUnavailablePeriod.id,
      updatingUser: userId,
    });
    return savedUnavailablePeriod;
  }

  async removeOneById(id: number, removingUser?: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removeOneById', id });

    const unavailablePeriod = await this.findOneById(id, removingUser);

    const vehicleIds = unavailablePeriod.vehicles?.map(vehicle => vehicle.id) || [];

    this.statusUpdateQueue
      .getJobs(['delayed'])
      .then(delayedJobs => {
        const relatedJobs = delayedJobs.filter(
          job => job.data.unavailablePeriodId === unavailablePeriod.id,
        );
        Promise.all(relatedJobs.map(job => job.remove())).catch(error => {
          this.logger.error({
            method: 'removeUnavailablePeriod',
            message: 'Error removing delayed jobs',
            error,
            unavailablePeriodId: unavailablePeriod.id,
          });
        });
      })
      .catch(error => {
        this.logger.error({
          method: 'removeUnavailablePeriod',
          message: 'Error fetching delayed jobs',
          error,
          unavailablePeriodId: unavailablePeriod.id,
        });
      });

    const today = new Date();
    const periodIsActiveForToday =
      today >= new Date(unavailablePeriod.from) && today <= new Date(unavailablePeriod.to);

    if (periodIsActiveForToday) {
      if (vehicleIds.length > 0) {
        await this.vehicleService.updateVehicleStatus(vehicleIds, Status.ACTIVE);
      }
      if (unavailablePeriod.operatorId) {
        await this.usersService.updateOperatorStatus([unavailablePeriod.operatorId], Status.ACTIVE);
      }
    }

    await this.unavailablePeriodRepository.delete(unavailablePeriod.id);
    this.logger.log({
      method: 'removeOneById',
      note: 'Unavailable period removed.',
      vehicleIds,
    });
  }

  async findExpiredUnavailability(): Promise<void> {
    this.logger.log({ method: 'findExpiredUnavailability' });

    const now = new Date();

    const body: GetUnavailablePeriodsDto = {
      expressions: [
        {
          category: UnavailablePeriodCategoryTypes.to,
          operator: '<' as QueryOperator,
          value: now.toISOString(),
          conditionType: 'AND',
        },
      ],
      relations: ['operator'],
      limit: 1000,
      offset: 0,
      sortModel: [],
    };

    const result = await this.findMany(body);

    const expiredRecords = result.data;

    if (expiredRecords.length > 0) {
      // Get only operator IDs from records that have a non-null operator.
      const operatorIds: number[] = expiredRecords
        .filter(record => record.operator !== null)
        .map(record => record.operator.id);

      // Collect vehicle IDs from the vehicles collections attached to each record.
      const vehicleIds: number[] = expiredRecords.flatMap(record =>
        record.vehicles ? record.vehicles.map(vehicle => vehicle.id) : [],
      );

      this.logger.log({
        method: 'findExpiredUnavailability',
        expiredCount: expiredRecords.length,
        expiredIds: expiredRecords.map(record => record.id),
        operatorIds,
        vehicleIds,
      });

      if (operatorIds.length > 0) {
        await this.usersService.updateOperatorStatus(operatorIds, Status.ACTIVE);
        this.logger.log({
          method: 'findExpiredUnavailability',
          message: `Updated ${operatorIds.length} operators to ACTIVE`,
        });
      }

      if (vehicleIds.length > 0) {
        await this.vehicleService.updateVehicleStatus(vehicleIds, Status.ACTIVE);
        this.logger.log({
          method: 'findExpiredUnavailability',
          message: `Updated ${vehicleIds.length} vehicles to ACTIVE`,
        });
      }
    }
  }

  async removeVehiclesFromUnavailablePeriods(
    vehicleIds: number[],
    operatorId: number,
  ): Promise<void> {
    if (!vehicleIds.length) {
      this.logger.log({
        method: 'removeVehiclesFromUnavailablePeriods',
        message: 'No vehicle IDs provided, skipping deletion.',
      });
      return;
    }
    const queryRunner = this.dataSource.createQueryRunner();

    try {
      const subQuerySQL = `
        SELECT up.id 
        FROM unavailable_period up 
        WHERE up.operatorId = ${operatorId}
      `;
      const vehicleIdsStr = vehicleIds.join(', ');

      const deleteSQL = `
        DELETE FROM unavailable_period_vehicles
        WHERE vehicleId IN (${vehicleIdsStr})
        AND unavailablePeriodId IN (${subQuerySQL})
      `;

      await queryRunner.manager.query(deleteSQL);

      // TODO: Check if each vehicle has another unavailable period not connected to this operator
      await this.vehicleService.updateVehicleStatus(vehicleIds, Status.ACTIVE);

      this.logger.log({
        method: 'removeVehiclesFromUnavailablePeriods',
        vehicleIds,
        operatorId,
        message: 'Successfully removed vehicles from unavailable periods and updated their status.',
      });
    } catch (e) {
      this.logger.error({
        method: 'removeVehiclesFromUnavailablePeriods',
        errorMessage: e.message,
        error: e,
        vehicleIds,
        operatorId,
      });
      throw new BadRequestException('Error removing vehicles from unavailable periods');
    } finally {
      await queryRunner.release();
    }
  }

  private async scheduleStatusInactivation(unavailablePeriod: UnavailablePeriod): Promise<void> {
    if (unavailablePeriod.isHidden) {
      this.logger.log({
        method: 'scheduleVehicleInactivation',
        message: `Skipping INACTIVATION job for hidden period ID ${unavailablePeriod.id}.`,
      });
      return;
    }

    const startDateTime = new Date(unavailablePeriod.from);
    const delay = startDateTime.getTime() - Date.now();

    this.logger.log({
      method: 'scheduleVehicleInactivation',
      message: `Checking for existing INACTIVATION job with period ID ${unavailablePeriod.id}...`,
    });

    // Check for an existing inactivation job in the queue
    const existingJobs = await this.statusUpdateQueue.getJobs(['delayed']);
    const existingJob = existingJobs.find(
      job =>
        job.name === JOBS.STATUS_UPDATE_INACTIVE &&
        job.data.unavailablePeriodId === unavailablePeriod.id,
    );

    if (existingJob) {
      await existingJob.remove(); // Remove the existing job
    }

    // Add a new INACTIVATION job
    await this.statusUpdateQueue.add(
      JOBS.STATUS_UPDATE_INACTIVE,
      {
        unavailablePeriodId: unavailablePeriod.id,
        vehicleIds: unavailablePeriod.vehicles.map(vehicle => vehicle.id),
        operatorId: unavailablePeriod.operatorId,
        newStatus: Status.INACTIVE,
      },
      { delay },
    );

    this.logger.log({
      method: 'scheduleVehicleInactivation',
      message: `Scheduled INACTIVATION job for period ID ${unavailablePeriod.id} in ${delay} ms.`,
    });
  }

  private async scheduleStatusActivation(unavailablePeriod: UnavailablePeriod): Promise<void> {
    if (unavailablePeriod.isHidden) {
      this.logger.log({
        method: 'scheduleVehicleActivation',
        message: `Skipping ACTIVATION job for hidden period ID ${unavailablePeriod.id}.`,
      });
      return;
    }

    const endDateTime = new Date(unavailablePeriod.to);
    const delay = endDateTime.getTime() - Date.now();

    this.logger.log({
      method: 'scheduleVehicleActivation',
      message: `Checking for existing ACTIVATION job with period ID ${unavailablePeriod.id}...`,
    });

    // Check for an existing activation job in the queue
    const existingJobs = await this.statusUpdateQueue.getJobs(['delayed']);
    const existingJob = existingJobs.find(
      job =>
        job.name === JOBS.STATUS_UPDATE_ACTIVE &&
        job.data.unavailablePeriodId === unavailablePeriod.id,
    );

    if (existingJob) {
      await existingJob.remove(); // Remove the existing job
    }

    // Add a new ACTIVATION job
    await this.statusUpdateQueue.add(
      JOBS.STATUS_UPDATE_ACTIVE,
      {
        unavailablePeriodId: unavailablePeriod.id,
        vehicleIds: unavailablePeriod.vehicles.map(vehicle => vehicle.id),
        operatorId: unavailablePeriod.operatorId,
        newStatus: Status.ACTIVE,
      },
      { delay },
    );

    this.logger.log({
      method: 'scheduleVehicleActivation',
      message: `Scheduled ACTIVATION job for period ID ${unavailablePeriod.id} in ${delay} ms.`,
    });
  }
}
