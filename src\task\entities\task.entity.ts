import { Priority, Status } from 'src/common/constants/task.enum';
import { Company } from 'src/company/entities/company.entity';
import { User } from 'src/users/entities/user.entity';
import { Entity, Column, ManyToOne, OneToMany, JoinColumn, Index } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { TaskComment } from './comment.entity';
import { TaskActivity } from './task-activity.entity';
import { TaskLabel } from './task-label.entity';

@Entity()
export class Task extends BaseEntity {
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  startDate: Date;

  @Column({ nullable: true })
  dueDate: Date;

  @Column({ type: 'enum', enum: Priority })
  priority: Priority;

  @Column({ type: 'enum', enum: Status })
  status: Status;

  @Column({ type: 'text' })
  description: string;

  @Column()
  @Index()
  assigneeId: number;

  @ManyToOne(() => User, user => user.tasks, { nullable: false })
  @JoinColumn({ name: 'assigneeId' })
  assignee: User;

  @Column()
  @Index()
  companyId: number;

  @ManyToOne(() => Company, company => company.tasks, { nullable: false })
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @OneToMany(() => TaskComment, comment => comment.task)
  comments: TaskComment[];

  @OneToMany(() => TaskActivity, activity => activity.task)
  activities: TaskActivity[];

  @Column({
    nullable: true,
  })
  labelId?: number;

  @ManyToOne(() => TaskLabel, label => label.tasks, { nullable: true })
  @JoinColumn({ name: 'labelId' })
  label: TaskLabel | null;
}
