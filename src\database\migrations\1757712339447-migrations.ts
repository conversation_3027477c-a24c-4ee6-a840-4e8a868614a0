import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1757712339447 implements MigrationInterface {
  name = 'Migrations1757712339447';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "partner" DROP CONSTRAINT "FK_7f7ce8585b3fde2f2ac9f21b50a"`,
    );
    await queryRunner.query(
      `CREATE TABLE "partner_container_pricelists" ("partnerId" integer NOT NULL, "containerPricelistsId" integer NOT NULL, CONSTRAINT "PK_e982d3d56fa02a88383df5364d9" PRIMARY KEY ("partnerId", "containerPricelistsId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7499a58e7c85f426e939b0a0ad" ON "partner_container_pricelists" ("partnerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_177b4f9c2fd9fac7b79c3a20e1" ON "partner_container_pricelists" ("containerPricelistsId") `,
    );
    await queryRunner.query(
      `INSERT INTO "partner_container_pricelists" ("partnerId", "containerPricelistsId") SELECT "id", "containerPricelistsId" FROM "partner" WHERE "containerPricelistsId" IS NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "partner" DROP COLUMN "containerPricelistsId"`);
    await queryRunner.query(
      `ALTER TABLE "partner_container_pricelists" ADD CONSTRAINT "FK_7499a58e7c85f426e939b0a0adb" FOREIGN KEY ("partnerId") REFERENCES "partner"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner_container_pricelists" ADD CONSTRAINT "FK_177b4f9c2fd9fac7b79c3a20e15" FOREIGN KEY ("containerPricelistsId") REFERENCES "container_pricelists"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "partner_container_pricelists" DROP CONSTRAINT "FK_177b4f9c2fd9fac7b79c3a20e15"`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner_container_pricelists" DROP CONSTRAINT "FK_7499a58e7c85f426e939b0a0adb"`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "imageUrl" character varying`);
    await queryRunner.query(`ALTER TABLE "partner" ADD "containerPricelistsId" integer`);
    await queryRunner.query(`DROP INDEX "public"."IDX_177b4f9c2fd9fac7b79c3a20e1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7499a58e7c85f426e939b0a0ad"`);
    await queryRunner.query(
      `ALTER TABLE "partner" ADD CONSTRAINT "FK_7f7ce8585b3fde2f2ac9f21b50a" FOREIGN KEY ("containerPricelistsId") REFERENCES "container_pricelists"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
