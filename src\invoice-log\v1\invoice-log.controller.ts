import { Controller, Get, Query, VERSION_NEUTRAL } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { InvoiceLogService } from './invoice-log.service';
import { GetInvoiceLogsDto } from './dto/get-invoice-logs.dto';
import { GetMultiInvoiceDto } from './dto/get-multi-invoice.dto';

@ApiTags('InvoiceLogs')
@Controller({ path: 'invoice-log', version: VERSION_NEUTRAL })
export class InvoiceLogController {
  constructor(private readonly invoiceLogService: InvoiceLogService) {}

  @Get('')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  findAll(@Query() query: GetInvoiceLogsDto) {
    return this.invoiceLogService.findAll(query);
  }

  @Get('pdf')
  async generateInvoicePdf(
    @Query() getMultiInvoiceDto: GetMultiInvoiceDto,
  ): Promise<string | null> {
    return this.invoiceLogService.generateInvoicePdf(getMultiInvoiceDto);
  }
}
