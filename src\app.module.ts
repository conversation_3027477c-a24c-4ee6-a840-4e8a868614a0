import { RedisModule } from './libs/redis/redis.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { OperatorClientModule } from './operator-client/v1/operator-client.module';
import { MailerModule } from './mailer/mailer.module';
import { UnavailablePeriodModule } from './unavailable-period/unavailable-period.module';
import {
  ClassSerializerInterceptor,
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
  ValidationPipe,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { AuthModule } from './auth/auth.module';
import { AuthGuard } from './common/guards/auth.guard';
import { RolesGuard } from './common/guards/roles.guard';
import { VehicleModule } from './vehicle/vehicle.module';
import { ReservationModule } from './reservation/reservation.module';
import typeorm from './database/typeorm';
import { TokenRefreshMiddleware } from './common/middlewares/token-refresh.middleware';
import { PricelistModule } from './pricelist/pricelist.module';
import { JobModule } from './job/job.module';
import { ScheduleModule } from '@nestjs/schedule';
import { FilesModule } from './s3/files.module';
import { JobEventsModule } from './job-events/job-events.module';
import {
  makeCounterProvider,
  makeHistogramProvider,
  PrometheusModule,
} from '@willsoto/nestjs-prometheus';
import { LoggingAndMetricsInterceptor } from './interceptors/logging-metrics.interceptor';
import { FavoriteModule } from './favorite/favorite.module';
import { ContractModule } from './contract/contract.module';
import { JobLocationModule } from './job-location/job-location.module';
import { AllExceptionFilter } from './common/filters/all-exceptions.filter';
import { TaskModule } from './task/task.module';
import { CompanySettingsModule } from './company-settings/company-settings.module';
import { PartnerModule } from './partner/partner.module';
import { TrackAndTraceModule } from './track-and-trace/v1/track-and-trace.module';
import { WorkSessionsModule } from './work-sessions/work-sessions.module';

@Module({
  imports: [
    TrackAndTraceModule,
    RedisModule,
    AnalyticsModule,
    OperatorClientModule,
    PrometheusModule.register({}),
    FilesModule,
    MailerModule,
    ConfigModule.forRoot({ isGlobal: true, load: [typeorm] }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => configService.get('typeorm'),
    }),
    AuthModule,
    UsersModule,
    VehicleModule,
    ReservationModule,
    PricelistModule,
    JobModule,
    UnavailablePeriodModule,
    ScheduleModule.forRoot(),
    JobEventsModule,
    FavoriteModule,
    ContractModule,
    JobLocationModule,
    TaskModule,
    CompanySettingsModule,
    PartnerModule,
    WorkSessionsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingAndMetricsInterceptor,
    },
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ClassSerializerInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    makeCounterProvider({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests by status code.',
      labelNames: ['status', 'statusGroup', 'method'],
    }),
    makeHistogramProvider({
      name: 'http_request_duration_milliseconds',
      help: 'Duration of HTTP requests in milliseconds',
      labelNames: ['method', 'route', 'statusCode'],
      buckets: [0.1, 3, 5, 7, 10],
    }),
    makeHistogramProvider({
      name: 'http_request_size_bytes',
      help: 'Size of HTTP requests in bytes',
      labelNames: ['method', 'route', 'statusCode'],
    }),
    makeHistogramProvider({
      name: 'http_response_size_bytes',
      help: 'Size of HTTP responses in bytes',
      labelNames: ['method', 'route', 'statusCode'],
    }),
    makeCounterProvider({
      name: 'http_errors_total',
      help: 'Total number of HTTP errors by status code and method.',
      labelNames: ['status', 'statusGroup', 'method'],
    }),
    {
      provide: APP_FILTER,
      useClass: AllExceptionFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(TokenRefreshMiddleware)
      .exclude(
        { path: 'auth/login', method: RequestMethod.POST }, // Exclude specific route
        { path: 'auth/logout', method: RequestMethod.GET }, // Exclude another route
        // Add more routes to exclude as needed
      )
      .forRoutes('*');
  }
}
