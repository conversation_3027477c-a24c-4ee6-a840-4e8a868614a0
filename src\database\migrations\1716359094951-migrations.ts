import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1716359094951 implements MigrationInterface {
  name = 'Migrations1716359094951';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "favorites" jsonb`);
    await queryRunner.query(`ALTER TABLE "user" ADD "contracted" jsonb`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "favorites"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "contracted"`);
  }
}
