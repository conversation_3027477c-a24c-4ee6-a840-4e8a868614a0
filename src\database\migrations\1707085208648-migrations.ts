import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1707085208648 implements MigrationInterface {
  name = 'Migrations1707085208648';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "verticalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "horizontalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "name"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "type" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "boomSize" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxVerticalReach" integer`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "maxHorizontalReach" integer`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "hasStrangler" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "invoicingPipesFrom" character varying`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "pipeLengthForSecondTechnician" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "typeOfMotorization" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "licensePlateNumber" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "weight" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "height" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "length" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "width" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "endHoseLength" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "maxFlowRate" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "maxConcretePressure" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableFlexiblePipeLength80Mm" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableFlexiblePipeLength90Mm" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableFlexiblePipeLength100Mm" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableFlexiblePipeLength120Mm" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableRigidPipeLength" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "maxDownwardReach" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "numberOfBoomSections" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "minUnfoldingHeight" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "boomRotation" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "rubberEndHoseLength" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "frontOutriggerSpan" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "rearOutriggerSpan" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "frontPressureOnOutrigger" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "rearPressureOnOutrigger" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "boomUnfoldingSystem" DROP NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "bacExit" SET DEFAULT false`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "bacExitReverse" SET DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "siteAddress" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "country" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "country" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "siteAddress" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "bacExitReverse" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "bacExit" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "boomUnfoldingSystem" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "rearPressureOnOutrigger" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "frontPressureOnOutrigger" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "rearOutriggerSpan" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "frontOutriggerSpan" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "rubberEndHoseLength" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "boomRotation" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "minUnfoldingHeight" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "numberOfBoomSections" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "maxDownwardReach" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableRigidPipeLength" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableFlexiblePipeLength120Mm" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableFlexiblePipeLength100Mm" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableFlexiblePipeLength90Mm" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "availableFlexiblePipeLength80Mm" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ALTER COLUMN "maxConcretePressure" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "maxFlowRate" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "endHoseLength" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "width" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "length" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "height" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "weight" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "licensePlateNumber" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ALTER COLUMN "typeOfMotorization" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "pipeLengthForSecondTechnician"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "invoicingPipesFrom"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "hasStrangler"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxHorizontalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "maxVerticalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "boomSize"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "type"`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "name" character varying NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "horizontalReach" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "verticalReach" integer NOT NULL`);
  }
}
