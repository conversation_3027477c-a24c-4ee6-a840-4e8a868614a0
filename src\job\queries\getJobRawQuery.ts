const joins = `
  LEFT JOIN "job_event"
    ON "job"."id" = "job_event"."jobId"
`;

const groupBy = `
  GROUP BY 
    "job"."id"
`;

export const getJobRawQuery = (whereQuery: string, querySort: string, queryLimit: string) => {
  return `
    SELECT 
      "job".*,
      COALESCE(
        JSON_AGG(
          JSON_BUILD_OBJECT(
            'id',               "job_event"."id",
            'currentStatus',    "job_event"."currentStatus",
            'statusDescription',"job_event"."statusDescription",
            'created_by',       "job_event"."created_by",
            'created_at',       "job_event"."created_at",
            'updated_at',       "job_event"."updated_at",
            'deleted_at',       "job_event"."deleted_at",
            'jobId',            "job_event"."jobId"
          )
        ) FILTER (WHERE "job_event"."id" IS NOT NULL),
        '[]'
      ) AS "jobEvents",
     CASE 
        WHEN "job"."report" IS NOT NULL THEN "job"."report"::jsonb
        ELSE NULL
      END AS "report"
    FROM "job"
    ${joins}
    ${whereQuery}
    ${groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getJobCountQuery = (whereQuery: string) => {
  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
    FROM "job"
    ${joins}
    ${whereQuery}
    LIMIT 1;
  `;
};
