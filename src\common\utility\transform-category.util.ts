// transform-category.util.ts

/**
 * A universal helper that rewrites a "friendly" category
 * into the actual enum value that your cequery-based code expects.
 *
 * @param category The user-provided "category" string from the request
 * @param map A dictionary from user-friendly key -> the final enum value
 * @returns The final enum value if found, else the original string
 */
export function transformFriendlyCategory(category: string, map: Record<string, string>): string {
  if (!category) return category;
  return map[category] || category;
}
