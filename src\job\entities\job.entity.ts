import { BaseEntity } from '../../common/entities/base.entity';
import { Column, Entity, OneToMany } from 'typeorm';
import {
  JobStatus,
  JobState,
  ParkingOn,
  TerrainStability,
  Cleaning,
  JobType,
} from 'src/common/constants/job.enum';
import { JobEvent } from 'src/job-events/entities/job-event.entity';
import { JobLocation } from 'src/job-location/entities/job-location.entity';

@Entity()
export class Job extends BaseEntity {
  @Column({ nullable: true, type: 'float' })
  amountOfConcrete?: number;

  @Column({ nullable: true })
  flowRate?: number;

  @Column({ nullable: true })
  balance?: boolean;

  @Column({ nullable: true })
  barbotine?: boolean;

  @Column({ nullable: true, type: 'float' })
  flexiblePipeLength80Mm?: number;

  @Column({ nullable: true, type: 'float' })
  flexiblePipeLength90Mm?: number;

  @Column({ nullable: true, type: 'float' })
  frontOutriggersSpan?: number;

  @Column({ nullable: true, type: 'float' })
  rearOutriggersSpan?: number;

  @Column({ nullable: true, type: 'float' })
  frontSideOpening: number;

  @Column({ nullable: true, type: 'float' })
  rearSideOpening: number;

  @Column({ nullable: true, type: 'float' })
  rigidPipeLength100Mm?: number;

  @Column({ nullable: true, type: 'float' })
  rigidPipeLength120Mm?: number;

  @Column({ nullable: true })
  extraCementBag?: boolean;

  @Column({ nullable: true })
  units?: number;

  @Column({ nullable: true })
  presenceOfPowerLines: boolean;

  @Column({ nullable: true, type: 'float' })
  voltage?: number;

  @Column({ type: 'boolean', default: false })
  pipeStartingFromBAC: boolean;

  @Column({ type: 'text', nullable: true })
  comments?: string;

  @Column({
    type: 'enum',
    enum: TerrainStability,
    nullable: true,
  })
  terrainStability?: TerrainStability;

  @Column({ nullable: true })
  tonnageRestriction?: boolean;

  @Column({ nullable: true, type: 'float' })
  authorizedWeight?: number;

  @Column({ nullable: true })
  heightRestriction?: boolean;

  @Column({ nullable: true, type: 'float' })
  heightLimit?: number;

  @Column({ type: 'boolean', nullable: true })
  enlistSecondTechnician?: boolean;

  @Column({ default: false })
  isElectricalRisk: boolean;

  @Column({ nullable: true })
  electricalRiskKey: string;

  @Column({ nullable: true })
  electricalRiskComment: string;

  @Column({ default: false })
  isAccessCompliance: boolean;

  @Column({ nullable: true })
  accessComplianceKey: string;

  @Column({ nullable: true })
  accessComplianceComment: string;

  @Column({ default: false })
  isParkingCompliance: boolean;

  @Column({ nullable: true })
  parkingComplianceKey: string;

  @Column({ nullable: true })
  parkingComplianceComment: string;

  @Column({ default: false })
  isTerrainStability: boolean;

  @Column({ nullable: true })
  terrainStabilityKey: string;

  @Column({ nullable: true })
  terrainStabilityComment: string;

  @Column({
    type: 'enum',
    enum: ParkingOn,
    nullable: true,
  })
  parkingOn?: ParkingOn | null;

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.NOT_STARTED,
  })
  status: JobStatus;

  @Column({
    type: 'enum',
    enum: JobState,
    default: JobState.WAITING,
  })
  state: JobState;

  @Column({
    type: 'enum',
    enum: Cleaning,
    nullable: true,
  })
  cleaning?: Cleaning;

  @Column({ type: 'timestamptz', nullable: true })
  start?: Date;

  @Column({ type: 'timestamptz', nullable: true })
  end?: Date;

  @Column({ default: 0 })
  progress: number;

  @Column({ type: 'simple-json', nullable: true })
  report?: {
    cleaningTime?: number;
    amountOfConcrete?: number;
    flexiblePipeLength80Mm?: number;
    flexiblePipeLength90Mm?: number;
    rigidPipeLength100Mm?: number;
    rigidPipeLength120Mm?: number;
    extraCementBags?: boolean;
    cementBags?: number;
    secondTechnicianMinutes?: number;
    signature?: string;
  };

  @Column({ default: 0, type: 'float' })
  estimatedTotalCost: number;

  @Column({
    type: 'enum',
    enum: JobType,
    nullable: true,
  })
  jobType?: JobType;

  @Column({ type: 'boolean', nullable: true })
  supplyOfTheChemicalSlushie?: boolean;

  @Column({ nullable: true })
  ciaw: string;

  @Column({ nullable: true })
  reasonForDelay: string;

  @OneToMany(() => JobEvent, jobEvent => jobEvent.job, { cascade: true })
  jobEvents: JobEvent[];

  @OneToMany(() => JobLocation, jobLocation => jobLocation.job, { cascade: true })
  jobLocations: JobLocation;
}
