import { Company } from 'src/company/entities/company.entity';
import { JobWithSignature } from 'src/job/v1/interfaces/jobWithSignature';
import { Reservation } from 'src/reservation/entities/reservation.entity';

export interface GenerateBulkReservationReport {
  dispatcherCompany: Company;
  operatorCompany: Company;
  totalSum: number;
  invoiceNumber: string;
  totalReservations: number;
}

export interface GenerateSingleReservationReport {
  reservation: Reservation | null;
  dispatcherCompany?: Company;
  operatorCompany: Company;
  invoiceNumber: string;
  totalExclVat: number;
  vatRate: number;
  vatAmount: number;
  totalSum: number;
  job: JobWithSignature;
}
