import { Injectable, Logger } from '@nestjs/common';
import { UnavailablePeriodService } from './v1/unavailable-period.service';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class UnavailablePeriodCronService {
  private readonly logger = new Logger(UnavailablePeriodCronService.name);
  constructor(private unavailablePeriodService: UnavailablePeriodService) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleExpiredUnavailability() {
    this.logger.log({ method: 'handleExpiredUnavailability', message: 'Cron job started' });

    await this.unavailablePeriodService.findExpiredUnavailability();

    this.logger.log({ method: 'handleExpiredUnavailability', message: 'Cron job completed' });
  }
}
