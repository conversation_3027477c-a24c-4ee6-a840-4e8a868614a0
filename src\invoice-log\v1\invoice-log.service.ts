import { HttpException, HttpStatus, Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { Repository } from 'typeorm';
import { InvoiceLog } from '../entities/invoice-log.entity';
import { CreateInvoiceLogDto } from './dto/create-invoice-logs.dto';
import { GetInvoiceLogsDto } from './dto/get-invoice-logs.dto';
import { PdfService } from 'src/pdf-report/pdf.service';
import { getInvoiceLogQuery } from '../queries/getInvoiceLog';
import { GenerateBulkReservationReport } from 'src/pdf-report/interface/generate-reservation-report.interface';
import { GetMultiInvoiceDto } from './dto/get-multi-invoice.dto';
import { UpdateInvoiceLogPayload } from './interface/update-invoice-log.interface';
import { ReservationService } from 'src/reservation/v1/reservation.service';
import { InvoiceType } from 'src/invoice-log/enums/invoice-type.enum';

@Injectable()
export class InvoiceLogService {
  private readonly logger = new Logger(InvoiceLogService.name);

  constructor(
    @InjectRepository(InvoiceLog)
    private repositoryInvoiceLog: Repository<InvoiceLog>,
    private pdfService: PdfService,
    @Inject(forwardRef(() => ReservationService))
    private reservationService: ReservationService,
  ) {}
  async create(createInvoiceLogDto: CreateInvoiceLogDto): Promise<InvoiceLog> {
    const { reservationIds, operatorCompanyId, dispatcherCompanyId, type, invoiceNumber, total } =
      createInvoiceLogDto;

    this.logger.log({
      method: 'createInvoiceLog',
      reservationIds,
      operatorCompanyId,
      dispatcherCompanyId,
      type,
      invoiceNumber,
      total,
    });

    const InvoiceLog = this.repositoryInvoiceLog.create({
      reservationIds,
      operatorCompanyId,
      dispatcherCompanyId,
      type,
      invoiceNumber,
      total,
    });

    await this.repositoryInvoiceLog.save(InvoiceLog);
    this.logger.log({
      method: 'createInvoiceLog',
      reservationIds,
      operatorCompanyId,
      dispatcherCompanyId,
      type,
      invoiceNumber,
      total,
    });
    return InvoiceLog;
  }

  async findAll(query: GetInvoiceLogsDto): Promise<EntityWithCountDto<InvoiceLog>> {
    this.logger.log({ method: 'findAllInvoiceLog', query });

    const {
      limit,
      offset,
      sortBy = 'invoiceLog.created_at',
      sortDir = 'ASC',
      operatorCompanyIds,
      dispatcherCompanyIds,
      type,
      status,
    } = query;

    const queryBuilder = this.repositoryInvoiceLog.createQueryBuilder('invoiceLog');

    queryBuilder.leftJoinAndSelect('invoiceLog.operatorCompany', 'operatorCompany');
    queryBuilder.leftJoinAndSelect('invoiceLog.dispatcherCompany', 'dispatcherCompany');

    if (operatorCompanyIds && operatorCompanyIds.length > 0) {
      queryBuilder.andWhere('invoiceLog.operatorCompanyId IN (:...operatorCompanyIds)', {
        operatorCompanyIds,
      });
    }

    if (dispatcherCompanyIds && dispatcherCompanyIds.length > 0) {
      queryBuilder.andWhere('invoiceLog.dispatcherCompanyId IN (:...dispatcherCompanyIds)', {
        dispatcherCompanyIds,
      });
    }

    if (status) {
      queryBuilder.andWhere('invoiceLog.status = :status', { status: status });
    }

    if (type) {
      queryBuilder.andWhere('invoiceLog.type = :type', { type: type });
    }

    queryBuilder.orderBy(sortBy, sortDir).skip(offset).take(limit);

    const [invoiceLogs, totalCount] = await queryBuilder.getManyAndCount();

    this.logger.log({
      method: 'findAllInvoiceLogs',
      query,
      invoiceLogsLength: invoiceLogs.length,
    });

    return new EntityWithCountDto<InvoiceLog>(InvoiceLog, invoiceLogs, totalCount);
  }

  async findOne(id: number): Promise<InvoiceLog> {
    this.logger.log({ method: 'findOneInvoiceLog', id });
    const InvoiceLog = await this.repositoryInvoiceLog.findOne({
      where: {
        id: id,
      },
    });

    if (!InvoiceLog) {
      throw new HttpException('Bulk invoice was not found!', HttpStatus.NOT_FOUND);
    }
    this.logger.log({ method: 'findOneInvoiceLog', id, InvoiceLogId: InvoiceLog.id });
    return InvoiceLog;
  }

  async generatePdfForBulkInvoice(getMultiInvoiceDto: GetMultiInvoiceDto): Promise<string | null> {
    const { reservationId, invoiceNumber } = getMultiInvoiceDto;
    this.logger.log({
      method: 'generatePdfForBulkInvoice',
      invoiceNumber,
      reservationId,
    });

    const queryBuilder = this.repositoryInvoiceLog.createQueryBuilder('invoiceLog');
    const InvoiceLog = await getInvoiceLogQuery(queryBuilder, invoiceNumber, reservationId);

    if (!InvoiceLog) {
      this.logger.warn(
        `InvoiceLog not found for invoiceNumber: ${invoiceNumber} or reservationId: ${reservationId}`,
      );
      return null;
    }

    const dispatcherCompany = InvoiceLog.dispatcherCompany;
    const operatorCompany = InvoiceLog.operatorCompany;

    const bulkReservationReport: GenerateBulkReservationReport = {
      dispatcherCompany,
      operatorCompany,
      totalSum: InvoiceLog.total,
      invoiceNumber: InvoiceLog.invoiceNumber,
      totalReservations: InvoiceLog.reservationIds.length,
    };

    const reservationsBuffer =
      await this.pdfService.generateBulkReservationReport(bulkReservationReport);

    this.logger.log({
      method: 'generatePdfForBulkInvoice',
      invoiceNumber,
      reservationId,
      pdfGenerated: true,
    });

    // Convert buffer to base64
    const pdfBase64 = `data:application/pdf;base64,${reservationsBuffer.toString('base64')}`;

    return pdfBase64;
  }

  async generateInvoicePdf(getMultiInvoiceDto: GetMultiInvoiceDto): Promise<string | null> {
    const { reservationId, invoiceNumber, invoiceType = InvoiceType.BULK } = getMultiInvoiceDto;
    this.logger.log({
      method: 'generateInvoicePdf',
      invoiceNumber,
      reservationId,
      invoiceType,
    });

    if (invoiceType === InvoiceType.SINGLE) {
      return this.reservationService.generatePdfForReservation(reservationId);
    } else {
      return this.generatePdfForBulkInvoice(getMultiInvoiceDto);
    }
  }

  async updateStatusAndTotal(updateInvoiceLog: UpdateInvoiceLogPayload): Promise<InvoiceLog> {
    const { logId, newStatus, newTotal } = updateInvoiceLog;
    this.logger.log({
      method: 'updateStatusAndTotal',
      logId,
      newStatus,
      newTotal,
    });
    const InvoiceLog = await this.findOne(logId);

    InvoiceLog.status = newStatus;

    if (newTotal !== undefined || newTotal !== null) {
      InvoiceLog.total = newTotal;
    }

    const updatedLog = await this.repositoryInvoiceLog.save(InvoiceLog);

    this.logger.log({
      method: 'updateStatusAndTotal',
      logId,
      oldStatus: InvoiceLog.status,
      newStatus,
      newTotal,
    });

    return updatedLog;
  }
}
