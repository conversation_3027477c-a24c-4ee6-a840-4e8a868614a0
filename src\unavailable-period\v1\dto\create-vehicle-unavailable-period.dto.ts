import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsISO8601, IsNumber, IsOptional, IsString } from 'class-validator';
import { processNumber } from 'src/common/helpers/processNumbers';

export class CreateVehicleUnavailablePeriodDto {
  @ApiProperty()
  @IsISO8601()
  from: Date;

  @ApiProperty()
  @IsISO8601()
  to: Date;

  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
  })
  @Transform(({ value }) => processNumber(value))
  vehicleIds: number[];

  @ApiProperty({ type: Number })
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  @IsOptional()
  contractId?: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiProperty({ required: false, default: false })
  @IsBoolean()
  @IsOptional()
  isHidden?: boolean;
}
