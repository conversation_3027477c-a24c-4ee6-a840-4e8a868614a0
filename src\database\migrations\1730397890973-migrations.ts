import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1730397890973 implements MigrationInterface {
  name = 'Migrations1730397890973';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "work_schedule_settings" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "day" character varying NOT NULL, "startTime" TIME NOT NULL, "endTime" TIME NOT NULL, "companySettingsId" integer, CONSTRAINT "PK_6c8db54bf2db87c250672948e7e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cffe2bcdeadf1653d9229bb456" ON "work_schedule_settings" ("companySettingsId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "cancellation_settings" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "cancellationWindow" interval NOT NULL, "reducedCancellationWindow" interval NOT NULL, "minTimeBetweenJobs" interval NOT NULL, "companySettingsId" integer, CONSTRAINT "REL_b8468bd28b7c7c5eccd9e408de" UNIQUE ("companySettingsId"), CONSTRAINT "PK_8ab177c9f024bd1172b61cc9171" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b8468bd28b7c7c5eccd9e408de" ON "cancellation_settings" ("companySettingsId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "company_settings" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "companyId" integer, CONSTRAINT "REL_474a36aafd4ff4a422eab6a3a9" UNIQUE ("companyId"), CONSTRAINT "PK_036b4634217db79c17305442dbe" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_474a36aafd4ff4a422eab6a3a9" ON "company_settings" ("companyId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "work_schedule_settings" ADD CONSTRAINT "FK_cffe2bcdeadf1653d9229bb4560" FOREIGN KEY ("companySettingsId") REFERENCES "company_settings"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" ADD CONSTRAINT "FK_b8468bd28b7c7c5eccd9e408de1" FOREIGN KEY ("companySettingsId") REFERENCES "company_settings"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "company_settings" ADD CONSTRAINT "FK_474a36aafd4ff4a422eab6a3a9d" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company_settings" DROP CONSTRAINT "FK_474a36aafd4ff4a422eab6a3a9d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" DROP CONSTRAINT "FK_b8468bd28b7c7c5eccd9e408de1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "work_schedule_settings" DROP CONSTRAINT "FK_cffe2bcdeadf1653d9229bb4560"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_474a36aafd4ff4a422eab6a3a9"`);
    await queryRunner.query(`DROP TABLE "company_settings"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b8468bd28b7c7c5eccd9e408de"`);
    await queryRunner.query(`DROP TABLE "cancellation_settings"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cffe2bcdeadf1653d9229bb456"`);
    await queryRunner.query(`DROP TABLE "work_schedule_settings"`);
  }
}
