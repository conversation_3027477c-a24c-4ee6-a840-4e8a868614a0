import { Module, forwardRef } from '@nestjs/common';
import { JobService } from './v1/job.service';
import { JobController } from './v1/job.controller';
import { Job } from './entities/job.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VehicleModule } from 'src/vehicle/vehicle.module';
import { ReservationModule } from 'src/reservation/reservation.module';
import { FilesService } from 'src/s3/files.service';
import { JobEventsModule } from 'src/job-events/job-events.module';
import { PdfService } from 'src/pdf-report/pdf.service';
import { MailerService } from 'src/mailer/mailer.service';
import { JobLocationModule } from 'src/job-location/job-location.module';
import { UsersModule } from 'src/users/users.module';
import { CompanyModule } from 'src/company/company.module';
import { WorkSessionsModule } from 'src/work-sessions/work-sessions.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Job]),
    forwardRef(() => VehicleModule),
    forwardRef(() => ReservationModule),
    forwardRef(() => JobEventsModule),
    forwardRef(() => JobLocationModule),
    forwardRef(() => UsersModule),
    forwardRef(() => CompanyModule),
    forwardRef(() => WorkSessionsModule),
  ],
  controllers: [JobController],
  providers: [JobService, FilesService, PdfService, MailerService],
  exports: [JobService],
})
export class JobModule {}
