import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1733406884911 implements MigrationInterface {
  name = 'Migrations1733406884911';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Drop constraints and indices related to the old columns
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_62dd5af91a0d20d09486118cca0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_2d6f82992f9ea8327b9e5b8bef"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_62dd5af91a0d20d09486118cca"`);

    // Step 2: Create the new "partner" table
    await queryRunner.query(
      `CREATE TYPE "public"."partner_status_enum" AS ENUM('approved', 'pending', 'declined')`,
    );
    await queryRunner.query(`
          CREATE TABLE "partner" (
            "id" SERIAL NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "deleted_at" TIMESTAMP,
            "created_by" integer,
            "dispatcherCompanyId" integer NOT NULL,
            "operatorCompanyId" integer NOT NULL,
            "status" "public"."partner_status_enum" NOT NULL DEFAULT 'pending',
            CONSTRAINT "UQ_00ee92a14deb02f084f537500b4" UNIQUE ("dispatcherCompanyId", "operatorCompanyId"),
            CONSTRAINT "PK_8f34ff11ddd5459eacbfacd48ca" PRIMARY KEY ("id")
          )
        `);
    await queryRunner.query(
      `CREATE INDEX "IDX_866bd06d0d18cf9f3ba62ee3d9" ON "partner" ("dispatcherCompanyId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_95f1e55c9721f42df2a7443bbf" ON "partner" ("operatorCompanyId")`,
    );

    // Step 3: Migrate existing data from "contract" to "partner"
    //   await queryRunner.query(`
    //   INSERT INTO "partner" ("dispatcherCompanyId", "operatorCompanyId", "status", "created_at", "updated_at")
    //   SELECT DISTINCT
    //     "dispatcherCompanyId",
    //     "operatorCompanyId",
    //     'approved'::partner_status_enum,
    //     NOW() as "created_at",
    //     NOW() as "updated_at"
    //   FROM "contract"
    //   WHERE "dispatcherCompanyId" IS NOT NULL AND "operatorCompanyId" IS NOT NULL
    // `);

    // Step 4: Add the "partnerId" column to "contract"
    await queryRunner.query(`ALTER TABLE "contract" ADD "partnerId" integer`);

    // Step 5: Update "contract" records to reference the appropriate "partner" records
    await queryRunner.query(`
          UPDATE "contract"
          SET "partnerId" = partner.id
          FROM (
            SELECT id, "dispatcherCompanyId", "operatorCompanyId"
            FROM "partner"
          ) AS partner
          WHERE "contract"."dispatcherCompanyId" = partner."dispatcherCompanyId"
          AND "contract"."operatorCompanyId" = partner."operatorCompanyId"
        `);

    // Step 6: Add index and foreign key for "partnerId"
    await queryRunner.query(
      `CREATE INDEX "IDX_1622f57e6fd5d12f7e36c1626c" ON "contract" ("partnerId")`,
    );
    await queryRunner.query(`
          ALTER TABLE "contract" ADD CONSTRAINT "FK_1622f57e6fd5d12f7e36c1626c8"
          FOREIGN KEY ("partnerId") REFERENCES "partner"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);

    // Step 7: Remove old columns from "contract"
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "dispatcherCompanyId"`);
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "operatorCompanyId"`);
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "comment"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Add the old columns back to "contract"
    await queryRunner.query(`ALTER TABLE "contract" ADD "dispatcherCompanyId" integer`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "operatorCompanyId" integer`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "comment" character varying`);

    // Step 2: Restore the "dispatcherCompanyId" and "operatorCompanyId" values from the "partner" table
    await queryRunner.query(`
          UPDATE "contract"
          SET "dispatcherCompanyId" = partner."dispatcherCompanyId",
              "operatorCompanyId" = partner."operatorCompanyId"
          FROM (
            SELECT id, "dispatcherCompanyId", "operatorCompanyId"
            FROM "partner"
          ) AS partner
          WHERE "contract"."partnerId" = partner.id
        `);

    // Step 3: Drop the "partnerId" column from "contract"
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_1622f57e6fd5d12f7e36c1626c8"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_1622f57e6fd5d12f7e36c1626c"`);
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "partnerId"`);

    // Step 4: Drop the "partner" table and its associated enum type
    await queryRunner.query(`DROP TABLE "partner"`);
    await queryRunner.query(`DROP TYPE "public"."partner_status_enum"`);

    // Step 5: Recreate the old indices and constraints on "contract"
    await queryRunner.query(
      `CREATE INDEX "IDX_62dd5af91a0d20d09486118cca" ON "contract" ("dispatcherCompanyId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2d6f82992f9ea8327b9e5b8bef" ON "contract" ("operatorCompanyId")`,
    );
    await queryRunner.query(`
          ALTER TABLE "contract" ADD CONSTRAINT "FK_62dd5af91a0d20d09486118cca0"
          FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION
        `);
    await queryRunner.query(`
          ALTER TABLE "contract" ADD CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2"
          FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION
        `);
  }
}
