const groupBy = `
GROUP BY
  "container_pricelists"."id"
`;

export const getContainersQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT 
      "container_pricelists".*
      ${selects ? `, ${selects}` : ''}
    FROM "container_pricelists"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getContainersCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "container_pricelists"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  partners: {
    join: `LEFT JOIN "partner_container_pricelists" AS "pcp" ON "container_pricelists"."id" = "pcp"."containerPricelistsId" LEFT JOIN "partner" AS "partners" ON "pcp"."partnerId" = "partners"."id"`,
    select: `COALESCE(json_agg(DISTINCT to_jsonb(partners)
    ) FILTER (WHERE "partners"."id" IS NOT NULL), '[]') AS "partners"`,
  },
  pricelists: {
    join: `LEFT JOIN "pricelist" AS "pricelists" ON "container_pricelists"."id" = "pricelists"."containerId"`,
    select: `COALESCE(json_agg(DISTINCT to_jsonb(pricelists)
    ) FILTER (WHERE "pricelists"."id" IS NOT NULL), '[]') AS "pricelists"`,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
