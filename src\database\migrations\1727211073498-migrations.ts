import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1727211073498 implements MigrationInterface {
  name = 'Migrations1727211073498';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_9d9b187fe55ae269d29492eec1f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_346103d35cbd1a48521384a81b4"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_346103d35cbd1a48521384a81b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9d9b187fe55ae269d29492eec1"`);
    await queryRunner.query(`CREATE TYPE "public"."company_type_enum" AS ENUM('1', '2', '3')`);
    await queryRunner.query(`CREATE TYPE "public"."company_status_enum" AS ENUM('1', '2', '3')`);
    await queryRunner.query(
      `CREATE TABLE "company" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "name" character varying NOT NULL, "type" "public"."company_type_enum" NOT NULL DEFAULT '2', "contactEmail" character varying NOT NULL, "billingEmail" character varying, "planningEmail" character varying, "phoneNumber" character varying, "address" character varying, "country" character varying, "city" character varying, "zipCode" integer, "status" "public"."company_status_enum" NOT NULL DEFAULT '1', "taxes" jsonb, CONSTRAINT "PK_056f7854a7afdba7cbd6d45fc20" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_43558f6152bdfedab7b92e6a65" ON "company" ("contactEmail") `,
    );
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "dispatcherManagerId"`);
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "operatorManagerId"`);
    await queryRunner.query(`ALTER TABLE "user" ADD "companyId" integer`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "dispatcherCompanyId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "operatorCompanyId" integer NOT NULL`);
    await queryRunner.query(
      `CREATE INDEX "IDX_86586021a26d1180b0968f9850" ON "user" ("companyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_62dd5af91a0d20d09486118cca" ON "contract" ("dispatcherCompanyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2d6f82992f9ea8327b9e5b8bef" ON "contract" ("operatorCompanyId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_86586021a26d1180b0968f98502" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_62dd5af91a0d20d09486118cca0" FOREIGN KEY ("dispatcherCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2" FOREIGN KEY ("operatorCompanyId") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_2d6f82992f9ea8327b9e5b8bef2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_62dd5af91a0d20d09486118cca0"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_2d6f82992f9ea8327b9e5b8bef"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_62dd5af91a0d20d09486118cca"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_86586021a26d1180b0968f9850"`);
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "operatorCompanyId"`);
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "dispatcherCompanyId"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "companyId"`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "operatorManagerId" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "contract" ADD "dispatcherManagerId" integer NOT NULL`);
    await queryRunner.query(`DROP INDEX "public"."IDX_43558f6152bdfedab7b92e6a65"`);
    await queryRunner.query(`DROP TABLE "company"`);
    await queryRunner.query(`DROP TYPE "public"."company_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."company_type_enum"`);
    await queryRunner.query(
      `CREATE INDEX "IDX_9d9b187fe55ae269d29492eec1" ON "contract" ("operatorManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_346103d35cbd1a48521384a81b" ON "contract" ("dispatcherManagerId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_346103d35cbd1a48521384a81b4" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_9d9b187fe55ae269d29492eec1f" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
