import { Transform, Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import * as moment from 'moment';
import { QueryRunner, RemoveOptions, Repository, SaveOptions } from 'typeorm';
import { BadRequestException, LoggerService } from '@nestjs/common';
import * as Retry from 'retry';
import { validateNumeric } from 'src/common/helpers/processNumbers';
import { ApiProperty } from '@nestjs/swagger';

const MAX_RETRIES = 3;

export enum SortDirections {
  ASC = 'ASC',
  DESC = 'DESC',
}

export const QueryOperatorValues = [
  '=',
  '==',
  'equals',
  'is',
  '!=',
  'not',
  'in',
  'isAnyOf',
  'not_in',
  'lt',
  'lte',
  '<',
  '&lt;',
  '<=',
  '&lt;=',
  'before',
  'onOrBefore',
  'gt',
  '&gt;',
  'gte',
  '>',
  '>=',
  '&gt;=',
  'after',
  'onOrAfter',
  'contains',
  'not_contains',
  'not_contains_or_empty',
  'starts_with',
  'startsWith',
  'not_starts_with',
  'ends_with',
  'endsWith',
  'not_ends_with',
  'isEmpty',
  'isNotEmpty',
  'array_contains_some_int',
  'array_contains_some_string',
  'check_element_in_array',
  '@>',
  'check_array_intersection',
  '&&',
  'before_or_empty',
  'onOrBefore_or_empty',
  'after_or_empty',
  'onOrAfter_or_empty',
  'true_or_empty',
  'false_or_empty',
  '<_or_empty',
  '&lt;_or_empty',
  'lt_or_empty',
  '<=_or_empty',
  '&lt;=_or_empty',
  'lte_or_empty',
  '>_or_empty',
  'gt_or_empty',
  '&gt;_or_empty',
  '>=_or_empty',
  '&gt;=_or_empty',
  'gte_or_empty',
  '!=_or_empty',
  'between',
  '<_days_ago',
  'lt_days_ago',
  '&lt;_days_ago',
  '>_days_ago',
  'gt_days_ago',
  '&gt;_days_ago',
  '<_days_ago_or_empty',
  'lt_days_ago_or_empty',
  '&lt;_days_ago_or_empty',
  '>_days_ago_or_empty',
  'gt_days_ago_or_empty',
  '&gt;_days_ago_or_empty',
  '<_days_in_future',
  'lt_days_in_future',
  '&lt;_days_in_future',
  '<_days_in_future_or_empty',
  '&lt;_days_in_future_or_empty',
  'lt_days_in_future_or_empty',
  '>_days_in_future_or_empty',
  '&gt;_days_in_future_or_empty',
  'gt_days_in_future_or_empty',
  '>_days_in_future',
  'gt_days_in_future',
  '&gt;_days_in_future',
  'search_in_jsonb',
  'search_in_jsonb_Ьy_orgs',
  'isOn',
  'isNotOn',
  'isInCurrentCalendarYear',
  'isNotInCurrentCalendarYear',
] as const;

export type QueryOperator = (typeof QueryOperatorValues)[number];

export interface CeSortItem {
  field: string;
  sort: SortDirections;
}

export interface WhereQuery {
  query: string;
  params: string[] | number[];
}

export declare type CeSortModel = CeSortItem[];

export enum ConditionType {
  AND = 'AND',
  OR = 'OR',
}

export class Expression {
  @ApiProperty({ required: false, enum: ConditionType })
  @IsOptional()
  @IsEnum(ConditionType)
  @Transform(({ value }) => (value ? value.toUpperCase() : value))
  conditionType?: string | null | undefined;

  @IsOptional()
  @IsString()
  category?: string | null | undefined;

  @ApiProperty({ required: false, enum: QueryOperatorValues })
  @IsOptional()
  @IsString()
  operator?: QueryOperator | null | undefined;

  @ApiProperty({ required: false })
  @IsOptional()
  value?: string | string[] | number | number[] | boolean | null | undefined;

  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => Expression)
  expressions?: Expression[] | null | undefined;
}

export class SortItem {
  @IsString()
  field: string;

  @ApiProperty({ required: true, enum: SortDirections })
  @IsEnum(SortDirections)
  @Transform(({ value }) => (value ? value.toUpperCase() : value)) // ASC | DESC
  sort: SortDirections;
}

export const getQuerySort = (sortModel: SortItem[]) => {
  if (!sortModel?.length) {
    return '';
  }
  const sortStrsArr = sortModel.map(({ field, sort }) => `${field} ${sort || 'ASC'}`);
  const sortStr = sortStrsArr.join(', ');
  const resultStr = `ORDER BY ${sortStr} NULLS LAST`;
  return resultStr;
};

export const getQueryLimit = (limit?: number, offset?: number) => {
  let resultStr = '';
  if (limit && validateNumeric(limit)) {
    resultStr = `${resultStr} LIMIT ${limit}`;
  } else {
    resultStr = `${resultStr} LIMIT 100`;
  }
  if (offset && validateNumeric(offset)) {
    resultStr = `${resultStr} OFFSET ${offset}`;
  }
  return resultStr;
};

const traverseExpressionTree = (
  query: string,
  params: string[] | number[],
  expressions: Expression[],
  handleQueryArgs: (query: string, expression: Expression, i: number) => string,
) => {
  if (!expressions.length) {
    return { query, params };
  }
  expressions.forEach((expression, i) => {
    if (expression.expressions) {
      if (!query.startsWith('(')) {
        query = `(${query})`;
      }
      query = `${query} ${expression.conditionType} (${
        traverseExpressionTree('', params, expression.expressions, handleQueryArgs).query
      })`;
    } else {
      query = handleQueryArgs(query, expression, i);
    }
  });
  return { query, params };
};

export const getWhereQuery = (expressions: Expression[]): WhereQuery => {
  const params = [];
  const query = '';

  const handleQueryArgs = (query: string, expression: Expression, i: number) => {
    const { category, operator, value, conditionType } = expression;
    // if first condition after where or (, we do not need to add the condition. else if the condition is undefined, we use ''

    const condType = i === 0 ? '' : conditionType || '';
    switch (operator) {
      case '=':
      case '==':
      case 'is':
      case 'equals':
        params.push(value);
        return `${query} ${condType} ${category} = $${params.length}`;
      case 'isOn': {
        const date = moment(value as string)
          .startOf('day')
          .format('YYYY-MM-DDTHH:mm:ss');
        const nextDay = moment(date).add(1, 'day').format('YYYY-MM-DDTHH:mm:ss');
        params.push(date);
        return `${query} ${condType} ${category} >= $${params.length} AND ${category} < '${nextDay}'`;
      }
      case 'isNotOn': {
        const date = moment(value as string)
          .startOf('day')
          .format('YYYY-MM-DDTHH:mm:ss');
        const nextDay = moment(date).add(1, 'day').format('YYYY-MM-DDTHH:mm:ss');
        params.push(date);
        return `${query} ${condType} ${category} < $${params.length} OR ${category} >= '${nextDay}'`;
      }

      case 'isInCurrentCalendarYear': {
        const currentYear = moment().year();
        return `${query} ${condType} EXTRACT(YEAR FROM ${category}) = ${currentYear}`;
      }

      case 'isNotInCurrentCalendarYear': {
        const currentYear = moment().year();
        return `${query} ${condType} EXTRACT(YEAR FROM ${category}) != ${currentYear} OR ${category} IS NULL`;
      }

      case 'not':
      case '!=':
        params.push(value);
        return `${query} ${condType} ${category} != $${params.length}`;
      case 'array_contains_some_int': {
        if (!value || !Array.isArray(value) || !value.length) {
          return query;
        }
        const argsArrContainsSome = [];
        value.forEach((val: number | string) => {
          params.push(val);
          argsArrContainsSome.push(`$${params.length}`);
        });
        return `${query} ${condType} ${category} && ARRAY[${argsArrContainsSome.join(
          ',',
        )}]::integer[]`; // in postgres '&&' is contains some
      }
      case 'array_contains_some_string': {
        if (!value || !Array.isArray(value) || !value.length) {
          return query;
        }
        const argsArrContainsSome = [];
        value.forEach((val: number | string) => {
          params.push(val);
          argsArrContainsSome.push(`$${params.length}`);
        });
        return `${query} ${condType} ${category} && ARRAY[${argsArrContainsSome.join(
          ',',
        )}]::text[]`; // in postgres '&&' is contains some
      }
      case 'isAnyOf':
      case 'in': {
        if (!value || !Array.isArray(value) || !value.length) {
          return query;
        }

        const argsArrIn = [];
        value.forEach(val => {
          params.push(val);
          argsArrIn.push(`$${params.length}`);
        });

        return `${query} ${condType} ${category} IN (${argsArrIn.join(',')})`;
      }
      case 'not_in': {
        if (!value || !Array.isArray(value) || !value.length) {
          return query;
        }
        const argsArrNotIn = [];
        value.forEach(val => {
          params.push(val);
          argsArrNotIn.push(`$${params.length}`);
        });
        return `${query} ${condType} ${category} NOT IN (${argsArrNotIn.join(',')})`;
      }
      case '<':
      case 'before':
      case 'lt':
      case '&lt;':
        params.push(value);
        return `${query} ${condType} ${category} < $${params.length}`;
      case '<_days_ago':
      case 'lt_days_ago':
      case '&lt;_days_ago': {
        if (!value || !validateNumeric(value)) {
          return query;
        }
        const date = moment().subtract(Number(value), 'days').format('YYYY-MM-DD');
        params.push(date);
        return `${query} ${condType} ${category} > $${params.length}`;
      }
      case '<_days_in_future':
      case 'lt_days_in_future':
      case '&lt;_days_in_future': {
        if (!value || !validateNumeric(value)) {
          return query;
        }
        const date = moment().add(Number(value), 'days').format('YYYY-MM-DD');
        params.push(date);
        return `${query} ${condType} ${category} < $${params.length}`;
      }
      case '<=':
      case 'onOrBefore':
      case 'lte':
      case '&lt;=':
        params.push(value);
        return `${query} ${condType} ${category} <= $${params.length}`;
      case '>':
      case 'after':
      case 'gt':
      case '&gt;':
        params.push(value);
        return `${query} ${condType} ${category} > $${params.length}`;
      case '>=':
      case 'onOrAfter':
      case 'gte':
      case '&gt;=':
        params.push(value);
        return `${query} ${condType} ${category} >= $${params.length}`;
      case '>_days_ago':
      case 'gt_days_ago':
      case '&gt;_days_ago': {
        if (!value || !validateNumeric(value)) {
          return query;
        }
        const date = moment().subtract(Number(value), 'days').format('YYYY-MM-DD');
        params.push(date);
        return `${query} ${condType} ${category} < $${params.length}`;
      }
      case '>_days_in_future':
      case 'gt_days_in_future':
      case '&gt;_days_in_future': {
        if (!value || !validateNumeric(value)) {
          return query;
        }
        const date = moment().add(Number(value), 'days').format('YYYY-MM-DD');
        params.push(date);
        return `${query} ${condType} ${category} > $${params.length}`;
      }
      case 'contains':
        params.push(`%${value}%`);
        return `${query} ${condType} ${category} ILIKE $${params.length}`;
      case 'not_contains':
        params.push(`%${value}%`);
        return `${query} ${condType} ${category} NOT ILIKE $${params.length}`;
      case 'startsWith':
      case 'starts_with':
        params.push(`${value}%`);
        return `${query} ${condType} ${category} ILIKE $${params.length}`;
      case 'not_starts_with':
        params.push(`${value}%`);
        return `${query} ${condType} ${category} NOT ILIKE $${params.length}`;
      case 'endsWith':
      case 'ends_with':
        params.push(`%${value}`);
        return `${query} ${condType} ${category} ILIKE $${params.length}`;
      case 'not_ends_with':
        params.push(`%${value}`);
        return `${query} ${condType} ${category} NOT ILIKE $${params.length}`;
      case 'isEmpty':
        return `${query} ${condType} ${category} IS NULL`;
      case 'isNotEmpty':
        return `${query} ${condType} ${category} IS NOT NULL`;
      case '@>':
      case 'check_element_in_array': {
        if (!value || !Array.isArray(value) || !value.length) {
          const argsArrContainsSome = [];
          params.push(value);
          argsArrContainsSome.push(`$${params.length}`);
          return `${query} ${condType} ${category} @> ARRAY[${argsArrContainsSome}]`;
        }
        break;
      }

      case 'search_in_jsonb': {
        if (!value || !Array.isArray(value) || !value.length) {
          const argsArrContainsSome = [];
          params.push(value);
          argsArrContainsSome.push(`$${params.length}`);
          return `${query} ${condType} elem->>${argsArrContainsSome} = 'true'`;
        }
      }
      case 'search_in_jsonb_Ьy_orgs': {
        if (!value || !Array.isArray(value) || !value.length) {
          return query;
        }
        const argsArrContainsSome = value.map((_, index) => `$${params.length + index + 1}`);
        params.push(...value);
        return `${query} ${condType} (elem->>'notifOrgId')::int IN (${argsArrContainsSome})`;
      }

      case '&&':
      case 'check_array_intersection': {
        if (!value || !Array.isArray(value) || !value.length) {
          return query;
        }
        const argsArrContainsSome = value.map((_, index) => `$${params.length + index + 1}`);
        params.push(...value);
        return `${query} ${condType} ${category} && ARRAY[${argsArrContainsSome}]`;
      }
      case 'not_contains_or_empty':
        params.push(`%${value}%`);
        return `${query} ${condType} (${category} NOT ILIKE $${params.length} OR ${category} IS NULL)`;
      case 'before_or_empty':
      case '<_or_empty':
      case '&lt;_or_empty':
      case 'lt_or_empty':
        params.push(value);
        return `${query} ${condType} (${category} < $${params.length} OR ${category} IS NULL)`;
      case 'onOrBefore_or_empty':
      case '<=_or_empty':
      case 'lte_or_empty':
      case '&lt;=_or_empty': {
        const date = moment(value as string)
          .endOf('day')
          .format('YYYY-MM-DDTHH:mm:ss');
        params.push(date);
        return `${query} ${condType} (${category} <= $${params.length} OR ${category} IS NULL)`;
      }
      case 'after_or_empty':
      case '>_or_empty':
      case 'gt_or_empty':
      case '&gt;_or_empty':
        params.push(value);
        return `${query} ${condType} (${category} > $${params.length} OR ${category} IS NULL)`;
      case 'onOrAfter_or_empty':
      case '>=_or_empty':
      case '&gt;=_or_empty':
      case 'gte_or_empty':
        {
          const date = moment(value as string)
            .startOf('day')
            .format('YYYY-MM-DDTHH:mm:ss');
          params.push(date);
          return `${query} ${condType} (${category} >= $${params.length} OR ${category} IS NULL)`;
        }
        params.push(value);
        return `${query} ${condType} (${category} >= $${params.length} OR ${category} IS NULL)`;
      case 'true_or_empty':
        return `${query} ${condType} (${category} IS NULL OR ${category} = TRUE)`;
      case 'false_or_empty':
        return `${query} ${condType} (${category} IS NULL OR ${category} = FALSE)`;

      case '<_days_ago_or_empty':
      case 'lt_days_ago_or_empty':
      case '&lt;_days_ago_or_empty': {
        const date = moment().subtract(Number(value), 'days').format('YYYY-MM-DD');
        params.push(date);
        return `${query} ${condType} (${category} > $${params.length} OR ${category} IS NULL)`;
      }
      case '>_days_ago_or_empty':
      case 'gt_days_ago_or_empty':
      case '&gt;_days_ago_or_empty': {
        const date = moment().subtract(Number(value), 'days').format('YYYY-MM-DD');
        params.push(date);
        return `${query} ${condType} (${category} < $${params.length} OR ${category} IS NULL)`;
      }
      case '<_days_in_future_or_empty':
      case '&lt;_days_in_future_or_empty':
      case 'lt_days_in_future_or_empty': {
        const date = moment().add(Number(value), 'days').format('YYYY-MM-DD');
        params.push(date);
        return `${query} ${condType} (${category} < $${params.length} OR ${category} IS NULL)`;
      }
      case '>_days_in_future_or_empty':
      case '&gt;_days_in_future_or_empty':
      case 'gt_days_in_future_or_empty': {
        const date = moment().add(Number(value), 'days').format('YYYY-MM-DD');
        params.push(date);
        return `${query} ${condType} (${category} > $${params.length} OR ${category} IS NULL)`;
      }
      case '!=_or_empty':
        params.push(value);
        return `${query} ${condType} (${category} != $${params.length} OR ${category} IS NULL)`;
      // postgres between operator treats both `start` and `end` dates inclusive.
      case 'between': {
        if (!value || !Array.isArray(value) || value.length !== 2) {
          return query;
        }
        params.push(...value);
        return `${query} ${condType} ${category} BETWEEN $${params.length - 1} AND $${
          params.length
        }`;
      }
      default: {
        throw new Error(`Unknown operator supplied to handleQueryArgs, operator: ${operator}`);
      }
    }
  };

  if (!expressions.length) {
    // it is possible that no value was provided, or value was empty array, so we check to make sure we did not end up with just 'WHERE'
    return { query, params };
  } else {
    const whereSqlAndParams = traverseExpressionTree(query, params, expressions, handleQueryArgs);
    let formattedWhereQuery = whereSqlAndParams.query.trim();

    formattedWhereQuery = formattedWhereQuery
      .replace('AND () AND ()', '')
      .replace('AND ()', '')
      .replace('AND (() )', '')
      .replace('OR ()', '')
      .replace('OR (() )', '')
      .replace('( AND', '(')
      .replace('( OR', '(')
      .replace('(AND', '(')
      .replace('(OR', '(')
      .replace('AND)', ')')
      .replace('OR)', ')')
      .replace('AND )', ')')
      .replace('OR )', ')')
      .replace('()', '')
      .replace('OR ( )', '')
      .replace('AND ( )', '');

    if (formattedWhereQuery.endsWith('AND')) {
      formattedWhereQuery = formattedWhereQuery.substring(0, formattedWhereQuery.length - 3);
    }
    if (formattedWhereQuery.endsWith('OR')) {
      formattedWhereQuery = formattedWhereQuery.substring(0, formattedWhereQuery.length - 2);
    }
    if (formattedWhereQuery.length) {
      formattedWhereQuery = `WHERE ${formattedWhereQuery}`;
    }
    return { query: formattedWhereQuery, params: whereSqlAndParams.params };
  }
};

export const addExpressions = (
  expressionsToAdd: Expression[],
  expressions: Expression[],
  conditionType: ConditionType,
): Expression[] => {
  if (!expressions.length) {
    return expressionsToAdd;
  }
  return [
    ...expressions,
    {
      expressions: expressionsToAdd,
      conditionType,
    },
  ];
};

export const queryWithRetry = async <T>(
  queryRunner: QueryRunner,
  query: string,
  parameters?: any[],
): Promise<T> => {
  try {
    const queryResult = await queryRunner.manager.query(query, parameters);
    return queryResult;
  } catch (error) {
    //* Deadlock error code
    if (error?.code === '40P01') {
      return queryRunner.manager.query(query, parameters);
    }
    throw error;
  }
};

export const makeDbRequest = async <T = any>(
  queryRunner: QueryRunner,
  sql: string,
  params?: any[],
  logger?: LoggerService,
): Promise<T> => {
  try {
    const results = await queryWithRetry<T>(queryRunner, sql, params);
    return results;
  } catch (error) {
    logger && logger.error({ errorMessage: error.message, error, sql, params });
    throw new BadRequestException('Unable to get the data with that query');
  } finally {
    await queryRunner.release();
  }
};

export const makeDbRequestCount = async (
  queryRunner: QueryRunner,
  sql: string,
  params: any[],
  logger?: LoggerService,
): Promise<number> => {
  const countRaw = await makeDbRequest(queryRunner, sql, params, logger);
  return countRaw[0]?.totalCount ? Number(countRaw[0].totalCount) : 0;
};

export const makeDbSaveRequest = async <T>(
  entityRepository: Repository<T>,
  entities: T[],
  options?: SaveOptions,
  logger?: LoggerService,
): Promise<T[]> => {
  const retryOperation = Retry.operation({ retries: MAX_RETRIES });

  return new Promise<T[]>((resolve, reject) => {
    retryOperation.attempt(async currentAttempt => {
      try {
        const savedEntities = await entityRepository.save<T>(entities, options);
        resolve(savedEntities);
      } catch (error) {
        if (error?.code === '40P01') {
          logger?.error(
            {
              errorMessage: `Deadlock detected, retrying attempt ${currentAttempt}`,
              error,
              entities,
            },
            'makeDbSaveRequest.catch',
          );
          return;
        }
        logger &&
          logger.error({ errorMessage: error.message, error, entities }, 'makeDbSaveRequest.catch');
        reject(error);
      }
    });
  });
};

export const makeDbRemoveRequest = async <T>(
  entityRepository: Repository<T>,
  entities: T[],
  options?: RemoveOptions,
  logger?: LoggerService,
): Promise<T[]> => {
  try {
    const removedEntities = await entityRepository.remove(entities, options);
    return removedEntities;
  } catch (error) {
    //* Deadlock error code
    if (error?.code === '40P01') {
      return entityRepository.remove(entities, options);
    }
    logger && logger.error({ errorMessage: error.message, error, entities });
    throw error;
  }
};
