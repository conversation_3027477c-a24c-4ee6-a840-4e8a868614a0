import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708024282674 implements MigrationInterface {
  name = 'Migrations1708024282674';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "price_list_settings" DROP CONSTRAINT "FK_eef017adcf2398ee6b6d992deb2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_list_settings" DROP CONSTRAINT "REL_eef017adcf2398ee6b6d992deb"`,
    );
    await queryRunner.query(`ALTER TABLE "price_list_settings" DROP COLUMN "pricelistId"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "settingsId" integer`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_4748f570a7192613ab3606d86cd" FOREIGN KEY ("settingsId") REFERENCES "price_list_settings"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_4748f570a7192613ab3606d86cd"`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "settingsId"`);
    await queryRunner.query(`ALTER TABLE "price_list_settings" ADD "pricelistId" integer`);
    await queryRunner.query(
      `ALTER TABLE "price_list_settings" ADD CONSTRAINT "REL_eef017adcf2398ee6b6d992deb" UNIQUE ("pricelistId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "price_list_settings" ADD CONSTRAINT "FK_eef017adcf2398ee6b6d992deb2" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
