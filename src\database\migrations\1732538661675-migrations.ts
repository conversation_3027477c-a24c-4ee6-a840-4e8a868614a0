import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1732538661675 implements MigrationInterface {
  name = 'Migrations1732538661675';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "totalSum"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "totalSum" double precision`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "totalSum"`);
    await queryRunner.query(`ALTER TABLE "reservation" ADD "totalSum" integer`);
  }
}
