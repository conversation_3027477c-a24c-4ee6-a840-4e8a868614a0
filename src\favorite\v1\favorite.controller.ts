import { Controller, Post, Body, Get, Query, VERSION_NEUTRAL } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Role } from 'src/common/constants/roles.enum';
import { Roles } from 'src/common/decorators/roles.decorator';
import { CreateFavoriteDto } from './dto/create-favorite.dto';
import { GetFavoriteDto } from './dto/get-favorite.to';
import { FavoriteService } from './favorite.service';

@ApiTags('Favorites')
@Controller({ path: 'favorites', version: VERSION_NEUTRAL })
export class FavoriteController {
  constructor(private readonly favoriteService: FavoriteService) {}

  @Get()
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  findAll(@Query() query: GetFavoriteDto) {
    return this.favoriteService.findMany(query);
  }

  @Post()
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  favoriteCompany(@Body() createFavoriteDto: CreateFavoriteDto) {
    return this.favoriteService.favoriteCompany(createFavoriteDto);
  }

  @Post('unfavorite')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
  ])
  unFavoriteCompany(@Body() createFavoriteDto: CreateFavoriteDto) {
    return this.favoriteService.unFavoriteCompany(createFavoriteDto);
  }
}
