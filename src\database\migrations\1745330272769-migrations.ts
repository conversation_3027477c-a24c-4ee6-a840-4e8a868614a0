import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1745330272769 implements MigrationInterface {
  name = 'Migrations1745330272769';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "suspension_period" ADD CONSTRAINT "UQ_95a8be14ecfa57665f938dda1df" UNIQUE ("contractId", "suspensionStart", "suspensionEnd")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "suspension_period" DROP CONSTRAINT "UQ_95a8be14ecfa57665f938dda1df"`,
    );
  }
}
