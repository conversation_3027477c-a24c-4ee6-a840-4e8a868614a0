import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706813840897 implements MigrationInterface {
  name = 'Migrations1706813840897';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD "typeOfMotorization" character varying NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "typeOfMotorization"`);
  }
}
