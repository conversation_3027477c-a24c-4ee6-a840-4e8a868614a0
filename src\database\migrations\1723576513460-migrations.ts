import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1723576513460 implements MigrationInterface {
  name = 'Migrations1723576513460';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" ADD "invoiceNumber" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "invoiceNumber"`);
  }
}
