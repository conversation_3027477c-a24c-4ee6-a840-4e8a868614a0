import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  VERSION_NEUTRAL,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { Public } from 'src/common/decorators/public.decorator';
import { GetUsersQueryParam } from './dto/get-users.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';

@ApiTags('Users')
@Controller({ path: 'users', version: VERSION_NEUTRAL })
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
  ])
  async create(@Body() createUserDto: CreateUserDto, @Req() req) {
    return await this.usersService.create(createUserDto, req?.user);
  }

  @Post('get')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER },
  ])
  findMany(@Body() body: GetUsersQueryParam, @Req() req) {
    return this.usersService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR },
    { role: Role.OPERATOR_MANAGER },
  ])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.usersService.findOneById(+id, req?.user);
  }

  @Patch(':id')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto, @Req() req) {
    const isOperator = req?.user?.roleId === Role.OPERATOR;

    // If user is an OPERATOR, ensure they can only update their own data
    if (isOperator && req?.user.sub != id) {
      throw new ForbiddenException('Operators can only update their own data.');
    }

    return this.usersService.updateOneById(+id, updateUserDto, req?.user);
  }

  @Delete(':id')
  @Roles([
    { role: Role.MANAGER },
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
  ])
  remove(@Param('id') id: string, @Req() req) {
    return this.usersService.removeOneById(+id, req?.user);
  }

  @Post('reset-password')
  @Public()
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    await this.usersService.resetPassword(resetPasswordDto);
    return { message: 'Password has been successfully reset.' };
  }

  @Post('forgot-password')
  @Public()
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    await this.usersService.forgotPassword(forgotPasswordDto);
    return { message: 'Email has been successfully sent.' };
  }

  @Get(':id/favorited-by')
  async getFavoritedByCompanies(@Param('id') id: number) {
    return await this.usersService.getFavoritedByCompanies(id);
  }

  // Expose the seed endpoint only in non-production environments
  @Post('seed')
  @Public()
  seed() {
    if (process.env.NODE_ENV === 'production') {
      throw new NotFoundException('Route not found.');
    }
    return this.usersService.seed();
  }
}
