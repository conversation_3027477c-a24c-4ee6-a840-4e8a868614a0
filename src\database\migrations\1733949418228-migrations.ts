import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1733949418228 implements MigrationInterface {
  name = 'Migrations1733949418228';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "task" ADD "startDate" TIMESTAMP NOT NULL DEFAULT now()`);
    9;

    // Backfill startDate with created_at
    await queryRunner.query(`
    UPDATE task
    SET "startDate" = created_at
    WHERE "startDate" = now()
  `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "startDate"`);
  }
}
