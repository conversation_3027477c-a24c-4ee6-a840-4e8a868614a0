import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1759313200869 implements MigrationInterface {
    name = 'Migrations1759313200869'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "company_holiday" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "date" date NOT NULL, "reason" character varying NOT NULL, "companySettingsId" integer, CONSTRAINT "PK_2583e961555f4e8424f08dd24e4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "company_holiday" ADD CONSTRAINT "FK_e01d169613e68d0b1ee5d4504ed" FOREIGN KEY ("companySettingsId") REFERENCES "company_settings"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "company_holiday" DROP CONSTRAINT "FK_e01d169613e68d0b1ee5d4504ed"`);
        await queryRunner.query(`DROP TABLE "company_holiday"`);
    }

}
