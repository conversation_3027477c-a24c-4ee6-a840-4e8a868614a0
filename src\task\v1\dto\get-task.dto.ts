import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsEnum, IsArray, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';

export enum TaskCategoryTypes {
  id = '"task"."id"',
  startDate = '"task"."startDate"',
  dueDate = '"task"."dueDate"',
  priority = '"task"."priority"',
  status = '"task"."status"',
  companyId = '"task"."companyId"',
}

export class TaskSortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: TaskCategoryTypes,
  })
  @IsEnum(TaskCategoryTypes, { message: 'invalid sort field' })
  field: TaskCategoryTypes;
}

export class TaskExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: TaskCategoryTypes,
  })
  @IsOptional()
  @IsEnum(TaskCategoryTypes, { message: 'invalid filter category' })
  category?: TaskCategoryTypes | string | null | undefined;
}

export class GetTaskDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: TaskSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskSortItem)
  readonly sortModel: TaskSortItem[];

  @ApiProperty({ required: true, isArray: true, type: TaskExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskExpression)
  readonly expressions: TaskExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
