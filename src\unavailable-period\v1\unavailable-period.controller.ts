import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { UnavailablePeriodService } from './unavailable-period.service';
import { CreateUnavailablePeriodDto } from './dto/create-unavailable-period.dto';
import { UpdateUnavailablePeriodDto } from './dto/update-unavailable-period.dto';
import { ApiTags } from '@nestjs/swagger';
import { Role } from 'src/common/constants/roles.enum';
import { Roles } from 'src/common/decorators/roles.decorator';
import { GetUnavailablePeriodsDto } from './dto/get-unavailable-period.dto';
import { UpdateVehicleUnavailablePeriodDto } from './dto/update-vehicle-unavailable-period.dto';
import { CreateVehicleUnavailablePeriodDto } from './dto/create-vehicle-unavailable-period.dto';

@ApiTags('UnavailablePeriods')
@Controller({ path: 'unavailable-periods', version: VERSION_NEUTRAL })
export class UnavailablePeriodController {
  constructor(private readonly unavailablePeriodService: UnavailablePeriodService) {}

  @Post()
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  create(@Body() createUnavailablePeriodDto: CreateUnavailablePeriodDto, @Req() req) {
    return this.unavailablePeriodService.create(createUnavailablePeriodDto, req?.user);
  }

  @Post('vehicle')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  createVehiclesUnavailablePeriod(
    @Body() createVehicleUnavailablePeriodDto: CreateVehicleUnavailablePeriodDto,
    @Req() req,
  ) {
    return this.unavailablePeriodService.createVehiclesUnavailablePeriod(
      createVehicleUnavailablePeriodDto,
      req?.user,
    );
  }

  @Post('get')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  findMany(@Body() body: GetUnavailablePeriodsDto, @Req() req) {
    return this.unavailablePeriodService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.unavailablePeriodService.findOneById(+id, req?.user);
  }

  @Patch(':id/vehicle')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  updateVehiclesUnavailablePeriod(
    @Param('id') id: string,
    @Body() updateVehiclesUnavailablePeriod: UpdateVehicleUnavailablePeriodDto,
    @Req() req,
  ) {
    return this.unavailablePeriodService.updateVehiclesUnavailablePeriod(
      +id,
      updateVehiclesUnavailablePeriod,
      req?.user,
    );
  }

  @Patch(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  updateOnyById(
    @Param('id') id: string,
    @Body() updateUnavailablePeriodDto: UpdateUnavailablePeriodDto,
    @Req() req,
  ) {
    return this.unavailablePeriodService.updateOnyById(+id, updateUnavailablePeriodDto, req?.user);
  }

  @Delete(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  removeOneById(@Param('id') id: string, @Req() req) {
    return this.unavailablePeriodService.removeOneById(+id, req?.user);
  }
}
