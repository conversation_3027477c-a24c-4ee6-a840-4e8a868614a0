import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1710964665700 implements MigrationInterface {
  name = 'Migrations1710964665700';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "resetPasswordToken" character varying`);
    await queryRunner.query(`ALTER TABLE "user" ADD "resetPasswordTokenExpiresAt" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "resetPasswordTokenExpiresAt"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "resetPasswordToken"`);
  }
}
