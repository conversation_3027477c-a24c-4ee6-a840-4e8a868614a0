import { Entity, Column, OneToMany, Index, JoinColumn, ManyToOne, ManyToMany } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Partner } from 'src/partner/entities/partner.entity';
import { Pricelist } from './pricelist.entity';
import { Company } from 'src/company/entities/company.entity';

@Entity()
@Index(['companyId'], { unique: true, where: '"isDefault" = true' })
export class ContainerPricelists extends BaseEntity {
  @Column({ nullable: true })
  title: string;

  @Column({ default: false })
  isPublic?: boolean;

  @Column({ default: false })
  isDefault?: boolean;

  @Column()
  @Index()
  companyId: number;

  @ManyToOne(() => Company, company => company.tasks, { nullable: false })
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @ManyToMany(() => Partner, partner => partner.containerPricelists)
  partners: Partner[];

  @OneToMany(() => Pricelist, entity => entity.container)
  pricelists: Pricelist[];
}
