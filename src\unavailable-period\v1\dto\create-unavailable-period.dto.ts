import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsISO8601 } from 'class-validator';
import { processNumber } from 'src/common/helpers/processNumbers';

export class CreateUnavailablePeriodDto {
  @ApiProperty()
  @IsISO8601()
  from: Date;

  @ApiProperty()
  @IsISO8601()
  to: Date;

  @ApiProperty()
  @Transform(({ value }) => processNumber(value))
  operatorId: number;
}
