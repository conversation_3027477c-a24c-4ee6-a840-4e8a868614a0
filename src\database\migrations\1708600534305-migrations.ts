import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708600534305 implements MigrationInterface {
  name = 'Migrations1708600534305';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_4748f570a7192613ab3606d86cd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_25fad7f4feabe9a54449493001b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_02ab9e053fe898666845b620eb8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_55b219065150443755f93d2671f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_5ed0dc8f08cb6fa56feab890430"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_468c922064d81dd66a100f4d9c4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_15412881d4828fb6f32f7435f5b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_7ac433048824708125ad22a4f0b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888"`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "managerId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "vehicleId" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "jobId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_4748f570a7192613ab3606d86cd" FOREIGN KEY ("settingsId") REFERENCES "price_list_settings"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_02ab9e053fe898666845b620eb8" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_25fad7f4feabe9a54449493001b" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_55b219065150443755f93d2671f" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_15412881d4828fb6f32f7435f5b" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_7ac433048824708125ad22a4f0b" FOREIGN KEY ("dispatcherId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_468c922064d81dd66a100f4d9c4" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_5ed0dc8f08cb6fa56feab890430" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_5ed0dc8f08cb6fa56feab890430"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_468c922064d81dd66a100f4d9c4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_7ac433048824708125ad22a4f0b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888"`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_15412881d4828fb6f32f7435f5b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_55b219065150443755f93d2671f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_25fad7f4feabe9a54449493001b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_02ab9e053fe898666845b620eb8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_4748f570a7192613ab3606d86cd"`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "jobId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "vehicleId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reservation" ALTER COLUMN "managerId" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_eee33a7d3ac2fafb669814ef888" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_7ac433048824708125ad22a4f0b" FOREIGN KEY ("dispatcherId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_15412881d4828fb6f32f7435f5b" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_468c922064d81dd66a100f4d9c4" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_5ed0dc8f08cb6fa56feab890430" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_5bcee0af773f5c5c6efc07ef188" FOREIGN KEY ("managerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_55b219065150443755f93d2671f" FOREIGN KEY ("operatorId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_02ab9e053fe898666845b620eb8" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_25fad7f4feabe9a54449493001b" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_4748f570a7192613ab3606d86cd" FOREIGN KEY ("settingsId") REFERENCES "price_list_settings"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
