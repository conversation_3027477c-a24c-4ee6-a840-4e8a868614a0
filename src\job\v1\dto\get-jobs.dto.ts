import { IsArray, IsEnum, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';

export enum JobCategoryTypes {
  jobId = '"job"."id"',
  status = '"job"."status"',
  createdBy = '"job"."created_by"',
  start = '"job"."start"',
  end = '"job"."end"',
}

export class JobExpression extends Expression {
  @IsOptional()
  @IsEnum(JobCategoryTypes, { message: 'Invalid filter category' })
  category?: string | null | undefined;
}

export class JobSortItem extends SortItem {
  @IsEnum(JobCategoryTypes, { message: 'Invalid sort field' })
  field: string;
}

export class GetJobsDto extends CommonQueryParams {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JobSortItem)
  readonly sortModel: JobSortItem[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => JobExpression)
  readonly expressions: JobExpression[];
}
