export function formatDate(dateToFormat?: Date, showTime: boolean = true): string {
  const date = dateToFormat || new Date();

  const pad = (num: number, size = 2) => ('00' + num).slice(-size);

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1);
  const day = pad(date.getDate());

  let formattedDate = `${year}-${month}-${day}`;

  if (showTime) {
    const hours = pad(date.getHours());
    const minutes = pad(date.getMinutes());
    const seconds = pad(date.getSeconds());

    const offsetMinutes = -date.getTimezoneOffset();
    const sign = offsetMinutes >= 0 ? '+' : '-';
    const offsetHours = pad(Math.floor(Math.abs(offsetMinutes) / 60));
    const offsetRemainingMinutes = pad(Math.abs(offsetMinutes) % 60);

    formattedDate += `T${hours}:${minutes}:${seconds}${sign}${offsetHours}:${offsetRemainingMinutes}`;
  }

  return formattedDate;
}
