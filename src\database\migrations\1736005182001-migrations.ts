import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1736005182001 implements MigrationInterface {
  name = 'Migrations1736005182001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" ALTER COLUMN "total" DROP DEFAULT`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bulk_invoice_log" ALTER COLUMN "total" SET DEFAULT '0'`);
  }
}
