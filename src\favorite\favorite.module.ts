import { forwardRef, Module } from '@nestjs/common';
import { FavoriteService } from './v1/favorite.service';
import { FavoriteController } from './v1/favorite.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Favorite } from './entities/favorite.entity';
import { CompanyModule } from 'src/company/company.module';

@Module({
  imports: [TypeOrmModule.forFeature([Favorite]), forwardRef(() => CompanyModule)],
  controllers: [FavoriteController],
  providers: [FavoriteService],
  exports: [FavoriteService],
})
export class FavoriteModule {}
