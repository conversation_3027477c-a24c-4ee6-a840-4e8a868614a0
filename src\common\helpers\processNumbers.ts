export const validateNumeric = (val): boolean => {
  if (!val && val !== 0) {
    return false;
  }
  if (typeof val === 'object' || Array.isArray(val)) {
    return null;
  }
  if (Buffer.isBuffer(val)) {
    return false;
  }
  if (typeof val === 'string') {
    val = val.trim();
  }
  return typeof val === 'number' || !isNaN(val);
};

type ProcessedVal = string | number;

export const processNumber = (value: string): ProcessedVal => {
  if (validateNumeric(value)) {
    return Number(value);
  }
  return value;
};
