import { CancellationType } from 'src/common/constants/cancellation.enum';
import { Job } from 'src/job/entities/job.entity';
export interface HourTypes {
  work: number;
  night: number;
  weekend: number;
}
export interface JobWithSignature extends Job {
  signature?: string;
  secondTechnicianFee?: number;
  baseFee?: number;
  additionalHourFee?: number;
  concreteCost?: number;
  pipe80mmCost?: number;
  pipe90mmCost?: number;
  pipe100mmCost?: number;
  pipe120mmCost?: number;
  barbotineCost?: number;
  supplyOfTheChemicalSlushieCost?: number;
  cleaningFee?: number;
  extraCementBagCost?: number;
  pumpTiersCost?: number;
  chargedPackageFlatFee?: number;
  secondTechnicianHourBreakdown?: HourTypes;
  baseHourBreakdown?: HourTypes;
  estimateBaseHourBreakdown?: HourTypes;
  additionalHourBreakdown?: HourTypes;
  estimateAdditionalHourBreakdown?: HourTypes;
  estimateHourDuration?: number;
  hourDuration?: number;
  additionalHours?: number;
  chargedTransportRate?: {
    name: string;
    tariff: number;
    from: number;
    to: number;
  };
  totalCost?: number;
  cancellationFeeType?: CancellationType | null;
  contractDayFee?: number;
  contractOvertimeFee?: number;
}
