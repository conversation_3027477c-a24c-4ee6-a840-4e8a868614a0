import { IsISO8601, <PERSON><PERSON><PERSON><PERSON>mpt<PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>ptional, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { processNumber } from 'src/common/helpers/processNumbers';
import { Transform, Type } from 'class-transformer';
import { CreatePricelistDto } from 'src/pricelist/v1/dto/create-pricelist.dto';

export class CreateContractDto {
  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({ type: Number })
  @Transform(({ value }) => processNumber(value))
  dispatcherCompanyId: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({ type: Number })
  @Transform(({ value }) => processNumber(value))
  vehicleId: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty({ type: Number })
  @Transform(({ value }) => processNumber(value))
  pricelistId?: number;

  @ApiProperty({ required: false })
  @IsISO8601()
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ required: false })
  @IsISO8601()
  @IsOptional()
  endDate?: Date;

  @ApiProperty({ type: String })
  comment?: string;

  @ApiProperty({ type: CreatePricelistDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreatePricelistDto)
  pricelist?: CreatePricelistDto;
}
