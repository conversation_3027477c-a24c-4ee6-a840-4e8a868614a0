import {
  Injectable,
  BadRequestException,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command,
  HeadObjectCommand,
  CreateBucketCommand,
  HeadBucketCommand,
  BucketLocationConstraint,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuid } from 'uuid';

import { Readable } from 'node:stream';
import { Upload } from '@aws-sdk/lib-storage';

@Injectable()
export class FilesService {
  // For local testing uncomment this code:
  private readonly s3 = new S3Client({
    credentials: {
      accessKeyId: process.env.S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
      sessionToken: process.env.S3_SESSION_TOKEN,
    },
    region: process.env.AWS_REGION,
  });
  // private readonly s3 = new S3Client({ region: process.env.AWS_REGION });
  private readonly logger = new Logger(FilesService.name);

  async uploadPrivateFile(
    privateBucketName: string,
    dataBuffer: Buffer,
    filename: string,
    fileType?: string,
    fileExtention?: string,
  ) {
    if (!privateBucketName) {
      this.logger.error(
        { errorMessage: 'Unable to upload private file because the bucket was not specified' },
        'uploadPrivateFile',
      );
      return null;
    }
    return this.uploadFile(privateBucketName, dataBuffer, filename, fileType, fileExtention);
  }

  async streamS3File({
    bucketUri,
    streamData,
    filePath,
    fileType,
    fileExtension,
  }: {
    bucketUri: string;
    streamData: Readable;
    filePath: string;
    fileType?: string;
    fileExtension?: string;
  }) {
    try {
      const parallelUploads3 = new Upload({
        client: this.s3,
        params: {
          Bucket: bucketUri,
          Key: `${filePath}-${uuid()}${fileExtension || ''}`,
          Body: streamData,
          ContentType: fileType,
        },
        queueSize: 4, // optional concurrency configuration
        partSize: 1024 * 1024 * 15, // optional size of each part, in bytes, at least 5MB
        leavePartsOnError: false, // optional manually handle dropped parts
      });

      return await parallelUploads3.done();
    } catch (e) {
      this.logger.error({ errorMessage: e.message, error: e }, 'streamS3File');
      return null;
    }
  }

  async uploadFile(
    privateBucketName: string,
    dataBuffer: Buffer,
    filename: string,
    fileType?: string,
    fileExtention?: string,
  ) {
    await this.ensureBucketExists(privateBucketName);

    const params = {
      Bucket: privateBucketName,
      Body: dataBuffer,
      Key: `${filename}-${uuid()}${fileExtention || ''}`,
      ContentType: fileType,
    };

    const putUploadCommand = new PutObjectCommand(params);
    try {
      const response = await this.s3.send(putUploadCommand);
      if (response.$metadata.httpStatusCode === 200) {
        this.logger.log({ message: 'File uploaded successfully' });
      }
      return { Key: params.Key };
    } catch (err) {
      this.logger.error({ errorMessage: err.message, error: err }, 'uploadFile');
      return null;
    }
  }

  async listObjectsInDirectory(bucketName: string, directory: string) {
    if (!bucketName) {
      this.logger.error(
        { errorMessage: 'Unable to list private files because the bucket was not specified' },
        'listObjectsInDirectory',
      );
      return null;
    }

    const params = {
      Bucket: bucketName,
      Prefix: directory,
    };

    const listObjectsCommand = new ListObjectsV2Command(params);
    try {
      const response = await this.s3.send(listObjectsCommand);
      const objects = response.Contents || [];

      return objects.map(object => ({
        Key: object.Key || '',
        LastModified: object.LastModified,
        Size: object.Size,
      }));
    } catch (err) {
      this.logger.error({ errorMessage: err.message, error: err }, 'listObjectsInDirectory');
      return null;
    }
  }

  async generatePresignedUrl(privateBucketName: string, key: string, expiresInSeconds = 900) {
    try {
      if (!privateBucketName) {
        throw new BadRequestException(
          'Unable to generate presigned URL because the bucket was not specified',
        );
      }
      if (!key) {
        return '';
      }
      const objCommand = new GetObjectCommand({
        Bucket: privateBucketName,
        Key: key,
      });

      return await getSignedUrl(this.s3, objCommand, { expiresIn: expiresInSeconds });
    } catch (err) {
      this.logger.error({ errorMessage: err.message, error: err }, 'generatePresignedUrl');
      return '';
    }
  }

  async checkFileExists(privateBucketName: string, key: string) {
    try {
      if (!privateBucketName) {
        throw new BadRequestException(
          'Unable to check file existence because the bucket was not specified',
        );
      }
      if (!key) {
        this.logger.error('The key was not specified', 'checkFileExists');
        return false;
      }
      const command = new HeadObjectCommand({ Bucket: privateBucketName, Key: key });
      await this.s3.send(command);
      return true;
    } catch (error) {
      if (error.name === 'NotFound') {
        return false;
      } else {
        this.logger.error({ errorMessage: error.message, error }, 'checkFileExists');
        return false;
      }
    }
  }

  async removeFile(key: string, bucket: string): Promise<{ statusCode: number; message: string }> {
    if (!bucket) {
      throw new BadRequestException('Bucket name is required to remove the file.');
    }

    if (!key) {
      this.logger.error('Invalid key provided for file removal.', 'removeFile');
      throw new BadRequestException('Invalid file key provided.');
    }

    const deleteCommand = new DeleteObjectCommand({
      Bucket: bucket,
      Key: key,
    });

    try {
      const response = await this.s3.send(deleteCommand);
      if (response.$metadata.httpStatusCode === 204) {
        this.logger.log('File removed successfully.', 'removeFile');
        return {
          statusCode: 204,
          message: `File with key '${key}' was successfully removed.`,
        };
      } else {
        this.logger.error(
          `Unexpected status code while removing file. Status code: ${response.$metadata.httpStatusCode}`,
          'removeFile',
        );
        throw new InternalServerErrorException(
          `Failed to remove file with unexpected status code: ${response.$metadata.httpStatusCode}.`,
        );
      }
    } catch (err) {
      this.logger.error(
        `Error occurred while removing file with key '${key}': ${err.message}`,
        'removeFile',
      );
      throw new InternalServerErrorException(
        `Error removing file with key '${key}': ${err.message}`,
      );
    }
  }

  async ensureBucketExists(bucketName: string): Promise<void> {
    try {
      await this.s3.send(new HeadBucketCommand({ Bucket: bucketName }));
    } catch (error) {
      if (error.name === 'NotFound') {
        const createBucketCommand = new CreateBucketCommand({
          Bucket: bucketName,
          CreateBucketConfiguration: {
            LocationConstraint: process.env.AWS_REGION as BucketLocationConstraint,
          },
        });
        await this.s3.send(createBucketCommand);
        this.logger.log(`Bucket created: ${bucketName}`);
      } else {
        this.logger.error({ errorMessage: error.message, error }, 'ensureBucketExists');
      }
    }
  }

  async getImageBase64(bucketName: string, key: string): Promise<string> {
    const command = new GetObjectCommand({ Bucket: bucketName, Key: key });
    try {
      const response = await this.s3.send(command);
      const stream = response.Body as Readable;

      const chunks: Uint8Array[] = [];
      for await (const chunk of stream) {
        chunks.push(typeof chunk === 'string' ? Buffer.from(chunk) : chunk);
      }
      const buffer = Buffer.concat(chunks);
      return `data:image/png;base64,${buffer.toString('base64')}`;
    } catch (error) {
      this.logger.error({ errorMessage: error.message, error }, 'getImageBase64');
      return null;
    }
  }

  async uploadVehicleImage(
    dataBuffer: Buffer,
    filename: string,
    fileType?: string,
    fileExtension?: string,
  ) {
    const vehiclesBucketName = process.env.S3_VEHICLES_BUCKET;
    return this.uploadPrivateFile(vehiclesBucketName, dataBuffer, filename, fileType, fileExtension);
  }

  async generateVehicleImageUrl(key: string, expiresInSeconds = 900) {
    const vehiclesBucketName = process.env.S3_VEHICLES_BUCKET;
    return this.generatePresignedUrl(vehiclesBucketName, key, expiresInSeconds);
  }

  async removeVehicleImage(key: string): Promise<{ statusCode: number; message: string }> {
    const vehiclesBucketName = process.env.S3_VEHICLES_BUCKET;
    return this.removeFile(key, vehiclesBucketName);
  }
}
