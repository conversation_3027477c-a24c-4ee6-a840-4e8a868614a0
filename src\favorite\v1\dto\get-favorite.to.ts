import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { processNumber } from 'src/common/helpers/processNumbers';

export class GetFavoriteDto extends CommonQueryParams {
  @ApiProperty({ type: Number, required: false, description: 'dispatcher company ID' })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  readonly dispatcherCompanyId?: number | null;

  @ApiProperty({
    type: Number,
    isArray: true,
    required: false,
    description: 'operator company IDs',
  })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  readonly operatorCompanyIds?: number[] | null;
}
