const groupBy = `
GROUP BY
  "company_settings"."id"
`;

export const getCompanySettingsQuery = (
  whereQuery: string,
  querySort: string,
  queryLimit: string,
  relations?: string[],
) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT 
      "company_settings".*
      ${selects ? `, ${selects}` : ''}
    FROM "company_settings"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    ${querySort}
    ${queryLimit}`;
};

export const getCompanySettingsCountQuery = (whereQuery: string, relations?: string[]) => {
  const { joins, selects, groupBys } = getDynamicJoins(relations);

  return `
    SELECT
      COUNT(*) OVER() AS "totalCount"
      ${selects ? `, ${selects}` : ''}
    FROM "company_settings"
    ${joins}
    ${whereQuery}
    ${groupBys ? `${groupBy}, ${groupBys}` : groupBy}
    LIMIT 1;
  `;
};

interface RelationConfig {
  join: string;
  select: string;
  groupBy?: string;
}

const relationsConfig: Record<string, RelationConfig> = {
  workSchedules: {
    join: `LEFT JOIN "work_schedule_settings" ON "company_settings"."id" = "work_schedule_settings"."companySettingsId"`,
    select: `COALESCE(json_agg(DISTINCT jsonb_build_object(
      'id', "work_schedule_settings"."id",
      'day', "work_schedule_settings"."day",
      'startTime', "work_schedule_settings"."startTime",
      'endTime', "work_schedule_settings"."endTime"
    )) FILTER (WHERE "work_schedule_settings"."id" IS NOT NULL), '[]') AS "workSchedules"`,
  },
  holidays: {
    join: `LEFT JOIN "company_holiday" ON "company_settings"."id" = "company_holiday"."companySettingsId"`,
    select: `COALESCE(json_agg(DISTINCT jsonb_build_object(
      'id', "company_holiday"."id",
      'date', "company_holiday"."date",
      'reason', "company_holiday"."reason"
    )) FILTER (WHERE "company_holiday"."id" IS NOT NULL), '[]') AS "holidays"`,
  },
  cancellation: {
    join: `LEFT JOIN "cancellation_settings" ON "company_settings"."cancellationId" = "cancellation_settings"."id"`,
    select: `jsonb_build_object(
      'id', "cancellation_settings"."id",
      'cancellationWindow', "cancellation_settings"."cancellationWindow",
      'reducedCancellationWindow', "cancellation_settings"."reducedCancellationWindow",
      'minTimeBetweenJobs', "cancellation_settings"."minTimeBetweenJobs"
    ) AS "cancellation"`,
    groupBy: `"cancellation_settings"."id"`,
  },
};

export const getDynamicJoins = (
  relations: string[],
): { joins: string; selects: string; groupBys: string } => {
  let joins = '';
  let selects = '';
  let groupBys = '';

  for (const relation of relations) {
    if (relationsConfig[relation]) {
      joins += `${relationsConfig[relation].join} `;
      selects += `${relationsConfig[relation].select}, `;
      if (relationsConfig[relation].groupBy) {
        groupBys += `${relationsConfig[relation].groupBy}, `;
      }
    }
  }

  // Remove trailing commas and spaces
  selects = selects.trim().replace(/,$/, '');
  groupBys = groupBys.trim().replace(/,$/, ' ');

  return { joins, selects, groupBys };
};
