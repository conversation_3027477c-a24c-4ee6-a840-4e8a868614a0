import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1748168531820 implements MigrationInterface {
  name = 'Migrations1748168531820';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "unavailable_period" ADD "contractId" integer`);
    await queryRunner.query(
      `CREATE INDEX "IDX_d90650900e826850a401f5dd11" ON "unavailable_period" ("contractId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" ADD CONSTRAINT "FK_d90650900e826850a401f5dd119" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "unavailable_period" DROP CONSTRAINT "FK_d90650900e826850a401f5dd119"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_d90650900e826850a401f5dd11"`);
    await queryRunner.query(`ALTER TABLE "unavailable_period" DROP COLUMN "contractId"`);
  }
}
