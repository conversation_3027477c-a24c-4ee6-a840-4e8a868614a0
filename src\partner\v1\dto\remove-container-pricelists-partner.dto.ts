import { IsNumber, IsArray, ArrayNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { processNumber } from 'src/common/helpers/processNumbers';
import { Transform } from 'class-transformer';

export class RemoveContainerPricelistsDto {
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  @ApiProperty({ type: [Number], description: 'Array of container pricelist IDs to remove' })
  @Transform(({ value }) =>
    Array.isArray(value) ? value.map(v => processNumber(v)) : [processNumber(value)],
  )
  containerIds: number[];
}
