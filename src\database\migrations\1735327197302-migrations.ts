import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1735327197302 implements MigrationInterface {
  name = 'Migrations1735327197302';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE "pricelist"
            ADD COLUMN "vat" jsonb NOT NULL DEFAULT '{"name": "Standard Pumping Services", "percentage": 21}'
          `);

    await queryRunner.query(`
            UPDATE "pricelist"
            SET "vat" = '{"name": "Standard Pumping Services", "percentage": 21}'
          `);

    await queryRunner.query(`
            ALTER TABLE "pricelist"
            ALTER COLUMN "vat" DROP DEFAULT
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "vat"`);
  }
}
