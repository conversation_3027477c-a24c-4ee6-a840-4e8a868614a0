import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { UpdateJobDto } from 'src/job/v1/dto/update-job.dto';
import { CreateReservationDto } from './create-reservation.dto';

class BaseUpdateReservationDto extends OmitType(CreateReservationDto, ['job'] as const) {}
export class UpdateReservationDto extends PartialType(BaseUpdateReservationDto) {
  @ApiProperty()
  job: UpdateJobDto;

  @ApiProperty()
  jobId?: number;
}
