export enum JobEventEnum {
  DRIVING_TO_SITE = 'Driving to site',
  SITE_ARRIVAL = 'Site Arrival',
  SECURITY_VALIDATION = 'Security Validation',
  START_SETUP = 'Start of installation',
  END_SETUP = 'End of installation',
  START_PUMPING = 'Start of pumping',
  END_PUMPING = 'End of pumping',
  LEAVE_SITE = 'Site departure',
  REPORT = 'Report',
  SIGNATURE = 'Client signature',
  START_CLEANUP = 'Start of cleaning',
  END_CLEANUP = 'End of cleaning',
  DURATION = 'Duration',
  CLEANING_DURATION = 'Cleaning Duration',
  COMPLETE = 'Job Completion',
  TOTAL_DURATION = 'Total Duration',
}
