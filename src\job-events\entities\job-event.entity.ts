import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Job } from 'src/job/entities/job.entity';

@Entity()
export class JobEvent extends BaseEntity {
  @Column()
  statusDescription: string;

  @Column({ nullable: true })
  currentStatus: string;

  @Column()
  jobId: number;

  @ManyToOne(() => Job, job => job.jobEvents, { onDelete: 'CASCADE' })
  job: Job;
}
