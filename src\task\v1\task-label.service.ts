import { BadRequestException, HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import {
  addExpressions,
  ConditionType,
  Expression,
  getQueryLimit,
  getQuerySort,
  getWhereQuery,
  QueryOperator,
  SortDirections,
} from 'src/libs/helpers/CeQuery';
import { DataSource, Repository } from 'typeorm';
import { TaskLabel } from '../entities/task-label.entity';
import { getTaskLabelsCountQuery, getTaskLabelsQuery } from '../queries/get-task-label-query';
import { CreateTaskLabelDto } from './dto/create-task-label.dto';
import { GetTaskLabelDto, TaskLabelCategoryTypes } from './dto/get-task-label.dto';
import { UpdateTaskLabelDto } from './dto/update-task-label.dto';

@Injectable()
export class TaskLabelService {
  private readonly logger = new Logger(TaskLabelService.name);

  constructor(
    @InjectRepository(TaskLabel)
    private labelRepository: Repository<TaskLabel>,
    private dataSource: DataSource,
  ) {}

  async create(
    createTaskLabelDto: CreateTaskLabelDto,
    creatingUser: SerializedUser,
  ): Promise<TaskLabel> {
    const { name, color } = createTaskLabelDto;
    this.logger.log({ method: 'create', name, color, creatingUserId: creatingUser.sub });

    const results = await this.findMany(
      {
        expressions: [
          {
            category: TaskLabelCategoryTypes.name,
            operator: '=' as QueryOperator,
            value: name,
            conditionType: 'AND',
          },
          {
            category: TaskLabelCategoryTypes.color,
            operator: '=' as QueryOperator,
            value: color,
            conditionType: 'AND',
          },
          {
            category: TaskLabelCategoryTypes.companyId,
            operator: '=' as QueryOperator,
            value: creatingUser.companyId,
            conditionType: 'AND',
          },
        ],
        sortModel: [],
        limit: 1,
        offset: 0,
      },
      creatingUser,
    );

    const [existingLabel] = results.data;

    if (existingLabel) {
      throw new HttpException('Label already exists!', HttpStatus.CONFLICT);
    }

    const label = this.labelRepository.create({
      name,
      color,
      companyId: creatingUser.companyId,
      created_by: creatingUser.sub,
    });
    await this.labelRepository.save(label);
    this.logger.log({ method: 'create', labelId: label.id });
    return label;
  }

  async updateOneById(
    id: number,
    updateTaskLabelDto: UpdateTaskLabelDto,
    updatingUser: SerializedUser,
  ): Promise<TaskLabel> {
    this.logger.log({ method: 'updateOneById', labelId: id, updatingUserId: updatingUser.sub });
    const label = await this.findOneById(id, updatingUser);

    Object.assign(label, updateTaskLabelDto);
    await this.labelRepository.save(label);
    this.logger.log({ method: 'updateOneById', labelId: label.id });
    return label;
  }

  async findMany(
    body: GetTaskLabelDto,
    fetchingUser: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<TaskLabel>> {
    this.logger.log({ method: 'findMany' });
    const { limit, offset, relations = [], sortModel = [] } = body;
    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    expressionsToAdd.push({
      category: TaskLabelCategoryTypes.companyId,
      operator: '=' as QueryOperator,
      value: fetchingUser.companyId,
      conditionType: 'AND',
    });
    if (!sortModel?.length) {
      sortModel.push({ field: TaskLabelCategoryTypes.id, sort: SortDirections.ASC });
    }
    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const tasksSQL = getTaskLabelsQuery(whereQuery.query, querySort, queryLimit, relations);

    try {
      let totalCount = 0;

      const taskLabels = await queryRunner.manager.query(tasksSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getTaskLabelsCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({ method: 'findMany', taskLabelLength: totalCount || taskLabels.length });
      return new EntityWithCountDto<TaskLabel>(TaskLabel, taskLabels, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number, fetchingUser: SerializedUser): Promise<TaskLabel> {
    this.logger.log({ method: 'findOneById', id });
    const results = await this.findMany(
      {
        expressions: [
          {
            category: TaskLabelCategoryTypes.id,
            operator: '=' as QueryOperator,
            value: id,
            conditionType: 'AND',
          },
        ],
        sortModel: [],
        limit: 1,
        offset: 0,
      },
      fetchingUser,
      false,
    );
    const [tasklabel] = results.data;
    if (!tasklabel) {
      throw new HttpException('Task Label was not found!', HttpStatus.NOT_FOUND);
    }
    this.logger.log({ method: 'findOneById', id, tasklabelId: tasklabel.id });
    return tasklabel;
  }

  async removeOneById(id: number, removingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removeOneById', id });
    await this.findOneById(id, removingUser);
    await this.labelRepository.delete({ id, companyId: removingUser.companyId });
  }
}
