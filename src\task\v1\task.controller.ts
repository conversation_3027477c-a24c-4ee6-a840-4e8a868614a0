import {
  <PERSON>,
  Post,
  Body,
  Req,
  Get,
  Param,
  Patch,
  Delete,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Role } from 'src/common/constants/roles.enum';
import { Roles } from 'src/common/decorators/roles.decorator';
import { CreateTaskCommentDto } from './dto/create-task-comment.dto';
import { CreateTaskDto } from './dto/create-task.dto';
import { GetTaskDto } from './dto/get-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskService } from './task.service';

@ApiTags('Tasks')
@Controller({ path: 'tasks', version: VERSION_NEUTRAL })
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @Post()
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async create(@Body() createTaskDto: CreateTaskDto, @Req() req) {
    return this.taskService.create(createTaskDto, req?.user);
  }

  @Post('get')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  findMany(@Body() body: GetTaskDto, @Req() req) {
    return this.taskService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.taskService.findOneById(+id, req?.user);
  }

  @Patch(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async updateOneById(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto, @Req() req) {
    return this.taskService.updateOneById(+id, updateTaskDto, req?.user);
  }

  @Delete(':id')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  removeOneById(@Param('id') id: string, @Req() req) {
    return this.taskService.removeOneById(+id, req?.user);
  }

  @Post(':id/comment')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async addComment(
    @Param('id') taskId: string,
    @Body() createTaskCommentDto: CreateTaskCommentDto,
    @Req() req,
  ) {
    return this.taskService.addComment(+taskId, createTaskCommentDto.content, req?.user);
  }

  @Get(':id/comments')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async findComments(@Param('id') id: string, @Req() req) {
    return this.taskService.findComments(+id, req?.user);
  }

  @Get(':id/activities')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.DISPATCHER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  async findActivities(@Param('id') id: string, @Req() req) {
    return this.taskService.findActivities(+id, req?.user);
  }
}
