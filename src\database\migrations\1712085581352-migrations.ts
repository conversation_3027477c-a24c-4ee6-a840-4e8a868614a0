import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1712085581352 implements MigrationInterface {
  name = 'Migrations1712085581352';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "password" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "password" SET NOT NULL`);
  }
}
