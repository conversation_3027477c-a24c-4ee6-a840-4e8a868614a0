import { Controller, Get, Post, Body, Param, Delete, Put, Req } from '@nestjs/common';
import { Role } from 'src/common/constants/roles.enum';
import { Roles } from 'src/common/decorators/roles.decorator';
import { ApiTags } from '@nestjs/swagger';
import { JobLocationService } from './job-location.service';
import { CreateJobLocationDto } from './dto/create-job-location.dto';
import { GetJobLocationDto } from './dto/get-job-location.dto';
import { UpdateJobLocationDto } from './dto/update-job-location.dto';

@ApiTags('JobLocations')
@Controller('job-locations')
export class JobLocationController {
  constructor(private readonly jobLocationService: JobLocationService) {}

  @Post()
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.OPERATOR },
  ])
  create(@Body() createJobLocationDto: CreateJobLocationDto, @Req() req) {
    return this.jobLocationService.create(createJobLocationDto, req?.user?.sub);
  }

  @Post('get')
  @Roles([
    { role: Role.DISPATCHER_MANAGER },
    { role: Role.OPERATOR_MANAGER },
    { role: Role.DISPATCHER },
  ])
  findMany(@Body() body: GetJobLocationDto) {
    return this.jobLocationService.findMany(body);
  }

  @Get(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  findOne(@Param('id') id: string) {
    return this.jobLocationService.findOneById(+id);
  }

  @Put(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  update(@Param('id') id: string, @Body() updatejobLocationDto: UpdateJobLocationDto, @Req() req) {
    return this.jobLocationService.update(+id, updatejobLocationDto, req?.user?.sub);
  }

  @Delete(':id')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  remove(@Param('id') id: string) {
    return this.jobLocationService.remove(+id);
  }
}
