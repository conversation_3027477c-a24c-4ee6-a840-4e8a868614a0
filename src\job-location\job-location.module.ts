import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JobModule } from 'src/job/job.module';
import { JobLocation } from './entities/job-location.entity';
import { JobLocationController } from './v1/job-location.controller';
import { JobLocationService } from './v1/job-location.service';

@Module({
  imports: [TypeOrmModule.forFeature([JobLocation]), forwardRef(() => JobModule)],
  providers: [JobLocationService],
  controllers: [JobLocationController],
  exports: [JobLocationService],
})
export class JobLocationModule {}
