import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1761852577101 implements MigrationInterface {
    name = 'Migrations1761852577101'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "totalWorkingHours" numeric(10,2)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "totalWorkingHours"`);
    }

}
