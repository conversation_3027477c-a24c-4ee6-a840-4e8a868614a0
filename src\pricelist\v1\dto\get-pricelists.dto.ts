import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, ValidateNested } from 'class-validator';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Type } from 'class-transformer';
import { SortItem, Expression } from 'src/libs/helpers/CeQuery';

export enum PricelistCategoryTypes {
  id = '"pricelist"."id"',
  isPublic = '"pricelist"."isPublic"',
  containerId = '"pricelist"."containerId"',
  containerIsPublic = '"container"."isPublic"',
  containerIsDefault = '"container"."isDefault"',
  userCompanyid = '"user"."companyId"',
  vehicleBoomSize = '"vehicle"."boomSize"',
}

export class PricelistSortItem extends SortItem {
  @ApiProperty({
    required: true,
    enum: PricelistCategoryTypes,
  })
  @IsEnum(PricelistCategoryTypes, { message: 'invalid sort field' })
  field: PricelistCategoryTypes;
}

export class PricelistExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: PricelistCategoryTypes,
  })
  @IsOptional()
  @IsEnum(PricelistCategoryTypes, { message: 'invalid filter category' })
  category?: PricelistCategoryTypes | string | null | undefined;
}
export class GetPricelistsDto extends CommonQueryParams {
  @ApiProperty({ required: true, isArray: true, type: PricelistSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PricelistSortItem)
  readonly sortModel: PricelistSortItem[];

  @ApiProperty({ required: true, isArray: true, type: PricelistExpression })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PricelistExpression)
  readonly expressions: PricelistExpression[];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
