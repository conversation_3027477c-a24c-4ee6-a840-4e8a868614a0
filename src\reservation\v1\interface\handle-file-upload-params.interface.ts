export interface HandleFileUploadsParams<T extends ReservationDto> {
  reservationDto: T;
  userId: number;
  existingReservation: ExistingReservation;
  jobId: number;
}

interface File {
  buffer: Buffer;
  mimetype: string;
}

export interface ReservationDto {
  trafficPlanFile?: File;
  localAdministrationAuthorizationFile?: File;
  parkingPermitAcquiredFile?: File;
  [key: string]: any;
}

interface ExistingReservation {
  trafficPlanKey?: string;
  localAdministrationAuthorizationKey?: string;
  parkingPermitAcquiredKey?: string;
  [key: string]: any;
}
