import { SelectQueryBuilder } from 'typeorm';
import { Vehicle } from '../entities/vehicle.entity';
import { Role } from 'src/common/constants/roles.enum';

export const getHiddenVehicleQuery = (
  queryBuilder: SelectQueryBuilder<Vehicle>,
  roleId: number,
  dateFrom?: Date,
  dateTo?: Date,
) => {
  if (roleId === Role.DISPATCHER || roleId === Role.DISPATCHER_MANAGER) {
    if (dateFrom && dateTo) {
      const hiddenDateFrom = new Date(dateFrom).toISOString();
      const hiddenDateTo = new Date(dateTo).toISOString();

      queryBuilder.andWhere(
        `NOT EXISTS (
          SELECT 1 FROM "unavailable_period_vehicles" upv
          INNER JOIN "unavailable_period" up ON upv."unavailablePeriodId" = up.id
          WHERE upv."vehicleId" = vehicle.id
            AND up."isHidden" = true
            AND up."from" <= :hiddenDateTo
            AND up."to" >= :hiddenDateFrom
            AND up."deleted_at" IS NULL
        )`,
        { hiddenDateFrom, hiddenDateTo },
      );
    } else {
      const currentDate = new Date().toISOString();

      queryBuilder.andWhere(
        `NOT EXISTS (
          SELECT 1 FROM "unavailable_period_vehicles" upv
          INNER JOIN "unavailable_period" up ON upv."unavailablePeriodId" = up.id
          WHERE upv."vehicleId" = vehicle.id
            AND up."isHidden" = true
            AND up."from" <= :currentDate
            AND up."to" >= :currentDate
            AND up."deleted_at" IS NULL
        )`,
        { currentDate },
      );
    }
  }
};
