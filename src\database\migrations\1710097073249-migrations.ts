import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1710097073249 implements MigrationInterface {
  name = 'Migrations1710097073249';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ADD "remainingAttempts" integer NOT NULL DEFAULT '5'`,
    );
    await queryRunner.query(`ALTER TABLE "user" ADD "lockoutEndTime" TIMESTAMP`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "lockoutEndTime"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "remainingAttempts"`);
  }
}
