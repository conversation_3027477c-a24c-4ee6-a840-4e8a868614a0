import { JobStatus } from 'src/common/constants/job.enum';

export const getReservationsForInvoiceQuery = async (
  reservationId: number[],
  companyId: number,
  managerId: number,
  jobStatus: JobStatus,
) => {
  return `
    SELECT
      r.id,
      r.invoiced,
      r."dateFrom",
      r.location::jsonb AS location,
      r."vehicleId",
      to_jsonb(p) AS pricelist,
      to_jsonb(j) || jsonb_build_object('report', (j.report)::jsonb) AS job
    FROM reservation AS r
    LEFT JOIN job AS j ON r."jobId" = j.id
    LEFT JOIN vehicle AS v ON r."vehicleId" = v.id
    LEFT JOIN pricelist AS p ON r."pricelistId" = p.id
    LEFT JOIN "user" AS d ON r."dispatcherId" = d.id
      WHERE r.id = ANY(ARRAY[${reservationId.join(', ')}])
      AND d."companyId" = ${companyId}
      AND r."managerId" = ${managerId}
      AND j.status = '${jobStatus}'
      AND r.invoiced = FALSE;
  `;
};
