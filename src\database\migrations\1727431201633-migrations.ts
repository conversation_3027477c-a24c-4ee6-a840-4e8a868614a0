import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1727431201633 implements MigrationInterface {
  name = 'Migrations1727431201633';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" DROP CONSTRAINT "FK_691cfe437aaf4c9ecb3f67cb2cc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" DROP CONSTRAINT "UQ_691cfe437aaf4c9ecb3f67cb2cc"`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle_type" DROP COLUMN "pricelistId"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_00e5ddee29dd30d0433ff418418"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_00e5ddee29dd30d0433ff418418" FOREIGN KEY ("vehicleTypeId") REFERENCES "vehicle_type"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_00e5ddee29dd30d0433ff418418"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_00e5ddee29dd30d0433ff418418" FOREIGN KEY ("vehicleTypeId") REFERENCES "vehicle_type"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle_type" ADD "pricelistId" integer`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" ADD CONSTRAINT "UQ_691cfe437aaf4c9ecb3f67cb2cc" UNIQUE ("pricelistId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_type" ADD CONSTRAINT "FK_691cfe437aaf4c9ecb3f67cb2cc" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
