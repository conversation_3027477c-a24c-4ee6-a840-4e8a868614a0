import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1724972520629 implements MigrationInterface {
  name = 'Migrations1724972520629';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reservation" ADD "pricelistId" integer`);
    await queryRunner.query(
      `ALTER TABLE "reservation" ADD CONSTRAINT "FK_d0386626d3778063e30bfc34204" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "reservation" DROP CONSTRAINT "FK_d0386626d3778063e30bfc34204"`,
    );
    await queryRunner.query(`ALTER TABLE "reservation" DROP COLUMN "pricelistId"`);
  }
}
