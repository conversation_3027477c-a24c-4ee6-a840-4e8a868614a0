import { ApiProperty } from '@nestjs/swagger';
import { IsISO8601, IsOptional } from 'class-validator';

export class DuplicateReservationDto {
  @ApiProperty({
    required: false,
    description:
      'New start date for the duplicated reservation. If provided, dateTo must also be provided.',
  })
  @IsOptional()
  @IsISO8601()
  dateFrom?: string;

  @ApiProperty({
    required: false,
    description:
      'New end date for the duplicated reservation. If provided, dateFrom must also be provided.',
  })
  @IsOptional()
  @IsISO8601()
  dateTo?: string;
}
