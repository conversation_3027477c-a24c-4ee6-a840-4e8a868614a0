import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1713298879032 implements MigrationInterface {
  name = 'Migrations1713298879032';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "job_event" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "statusDescription" character varying NOT NULL, "jobId" integer NOT NULL, CONSTRAINT "PK_d4439f70bd53ce8a8a2a9a783c7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_event" ADD CONSTRAINT "FK_deae36b9856509b82899b771112" FOREIGN KEY ("jobId") REFERENCES "job"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "job_event" DROP CONSTRAINT "FK_deae36b9856509b82899b771112"`,
    );
    await queryRunner.query(`DROP TABLE "job_event"`);
  }
}
