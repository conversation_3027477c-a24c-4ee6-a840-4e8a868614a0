import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { ActionType } from 'src/common/constants/task.enum';
import { processNumber } from 'src/common/helpers/processNumbers';
import { Transform } from 'class-transformer';

export class CreateTaskActivityDto {
  @ApiProperty({ required: true })
  @Transform(({ value }) => processNumber(value))
  taskId: number;

  @ApiProperty({ enum: ActionType, required: true })
  @IsNotEmpty()
  @IsEnum(ActionType)
  actionType: ActionType;
}
