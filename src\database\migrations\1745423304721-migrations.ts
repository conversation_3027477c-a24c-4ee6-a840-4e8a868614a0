import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1745423304721 implements MigrationInterface {
  name = 'Migrations1745423304721';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "contractCancellationPeriod" double precision`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "contractLateCancellationPeriod" double precision`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "contractLateCancellationPeriod"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "contractCancellationPeriod"`);
  }
}
