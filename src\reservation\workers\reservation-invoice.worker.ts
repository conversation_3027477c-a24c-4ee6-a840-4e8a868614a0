import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QUEUES } from 'src/libs/bullmq/constants';
import { ReservationService } from '../v1/reservation.service';
import { MailerService } from 'src/mailer/mailer.service';
import { UsersService } from 'src/users/v1/users.service';

@Processor(QUEUES.RESERVATION_INVOICE, {
  concurrency: 1,
  limiter: {
    max: 1, // Maximum job count
    duration: 15000, // per 15 seconds
  },
})
export class ProcessReservationInvoiceConsumer extends WorkerHost {
  private readonly logger: Logger;

  constructor(
    private reservationService: ReservationService,
    private mailerService: MailerService,
    private userService: UsersService,
  ) {
    super();
    this.logger = new Logger(ProcessReservationInvoiceConsumer.name);
  }

  async process(job: Job<any>): Promise<any> {
    this.logger.log(`Job id:${job.id} is being processed`);
    const { reservationId, invoiceLog } = job.data;
    return await this.reservationService.generateInvoiceForSingleReservation(
      reservationId,
      invoiceLog,
    );
  }

  @OnWorkerEvent('completed')
  onCompleted(job: Job<unknown>) {
    this.logger.log(`Job id:${job.id} completed`);
  }

  @OnWorkerEvent('failed')
  async onFailed(job: Job<any>, error: Error) {
    this.logger.log(`Job id:${job.id} has failed`, error.stack);

    const operatorManager = await this.userService.findOneById(job.data.operatorManagerId);

    const emailContext = {
      name: operatorManager.firstName,
      surname: operatorManager.lastName,
      errorMessage: error.message,
      documentType: 'Invoice',
    };

    await this.mailerService.sendMail({
      to: operatorManager.email,
      subject: 'Invoice Processing Error',
      template: 'document-error-notification',
      context: emailContext,
    });
  }

  @OnWorkerEvent('error')
  onError(job: Job<unknown>) {
    this.logger.log(`Job id:${job.id} encountered an error`);
  }
}
