import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { User } from '../../users/entities/user.entity';
import { WorkSessionStatus } from '../enums/work-session-status.enum';

export class WorkBreak {
  breakStart: Date;
  breakEnd?: Date;
  reason?: string;
}

@Entity()
@Index(['operatorId', 'sessionStart'])
export class WorkSession extends BaseEntity {
  @Column()
  @Index()
  operatorId: number;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'operatorId' })
  operator: User;

  @Column({ type: 'timestamptz' })
  sessionStart: Date;

  @Column({ type: 'timestamptz', nullable: true })
  sessionEnd?: Date;

  @Column({
    type: 'enum',
    enum: WorkSessionStatus,
    default: WorkSessionStatus.ACTIVE,
  })
  @Index()
  status: WorkSessionStatus;

  @Column({ type: 'simple-json', nullable: true })
  breaks?: WorkBreak[];

  @Column({ type: 'simple-array', nullable: true })
  jobIds?: number[];

  @Column({ type: 'int', nullable: true })
  totalWorkTime?: number;

  @Column({ type: 'int', nullable: true })
  totalBreakTime?: number;

  @Column({ type: 'text', nullable: true })
  notes?: string;
}
