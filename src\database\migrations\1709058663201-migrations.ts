import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1709058663201 implements MigrationInterface {
  name = 'Migrations1709058663201';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "stabilization"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "terrainStability" text`);
    await queryRunner.query(`ALTER TABLE "job" ADD "tonnageRestriction" integer`);
    await queryRunner.query(`ALTER TABLE "job" ADD "heightRestriction" integer`);
    await queryRunner.query(`ALTER TABLE "job" ADD "enlistSecondTechnician" boolean`);
    await queryRunner.query(`ALTER TABLE "job" ADD "backupPumpPackage" boolean`);
    await queryRunner.query(`ALTER TABLE "job" ADD "localAdministrationAuthorization" text`);
    await queryRunner.query(`ALTER TABLE "job" ADD "trafficPlan" text`);
    await queryRunner.query(
      `CREATE TYPE "public"."job_parkingon_enum" AS ENUM('Public Road', 'Private Road', 'WorkSide')`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "parkingOn" "public"."job_parkingon_enum"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "extraCementBag"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "extraCementBag" integer NOT NULL`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "presenceOfPowerLines"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "presenceOfPowerLines" integer NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "presenceOfPowerLines"`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "presenceOfPowerLines" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "extraCementBag"`);
    await queryRunner.query(
      `ALTER TABLE "job" ADD "extraCementBag" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "parkingOn"`);
    await queryRunner.query(`DROP TYPE "public"."job_parkingon_enum"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "trafficPlan"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "localAdministrationAuthorization"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "backupPumpPackage"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "enlistSecondTechnician"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "heightRestriction"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "tonnageRestriction"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "terrainStability"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "stabilization" text`);
  }
}
