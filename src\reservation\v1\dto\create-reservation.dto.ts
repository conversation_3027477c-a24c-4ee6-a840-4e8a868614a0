import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { Location } from 'src/vehicle/entities/vehicle.entity';
import { ClientDetails, ReservationType } from '../../entities/reservation.entity';
import { IsEnum, IsISO8601, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { CreateJobDto } from 'src/job/v1/dto/create-job.dto';
import { processNumber } from 'src/common/helpers/processNumbers';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';

export class CreateReservationDto {
  @ApiProperty({ required: true })
  @Transform(({ value }) => processNumber(value))
  managerId: number;

  @ApiProperty({ required: true })
  @Transform(({ value }) => processNumber(value))
  operatorId: number;

  @ApiProperty({ required: true })
  @Transform(({ value }) => processNumber(value))
  dispatcherId: number;

  @ApiProperty({ required: true })
  @Transform(({ value }) => processNumber(value))
  vehicleId: number;

  @ApiProperty({ enum: ReservationType, enumName: 'ReservationType' })
  @IsNotEmpty()
  @IsEnum(ReservationType)
  reservationType: ReservationType;

  @ApiProperty({ required: false })
  @IsISO8601()
  dateFrom?: Date;

  @ApiProperty({ required: false })
  @IsISO8601()
  dateTo?: Date;

  @ApiProperty({ required: false })
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'siteAddress'))
  siteAddress: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'city'))
  city: string;

  @ApiProperty({ required: false, type: Number, description: 'Postcode' })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  plz: number;

  @ApiProperty({ description: 'Order number for the reservation', required: false })
  @IsOptional()
  @IsString()
  orderNumber?: string;

  @ApiProperty()
  @Transform(({ value }) => JSON.parse(value), { toClassOnly: true })
  location: Location;

  @ApiProperty()
  @Transform(({ value }) => JSON.parse(value), { toClassOnly: true })
  clientDetails: ClientDetails;

  @ApiProperty()
  @Transform(({ value }) => JSON.parse(value), { toClassOnly: true })
  job: CreateJobDto;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  @IsOptional()
  trafficPlanFile?: Express.Multer.File;

  @IsOptional()
  @IsString()
  trafficPlanKey?: string;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  @IsOptional()
  parkingPermitAcquiredFile?: Express.Multer.File;

  @IsOptional()
  @IsString()
  parkingPermitAcquiredKey?: string;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  @IsOptional()
  localAdministrationAuthorizationFile?: Express.Multer.File;

  @IsOptional()
  @IsString()
  localAdministrationAuthorizationKey?: string;

  @ApiProperty({
    required: false,
    type: Number,
  })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  pricelistId?: number;
}
