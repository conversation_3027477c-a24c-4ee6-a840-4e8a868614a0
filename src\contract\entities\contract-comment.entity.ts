import { <PERSON><PERSON><PERSON>, <PERSON>umn, ManyTo<PERSON>ne, <PERSON>in<PERSON><PERSON><PERSON>n, Index } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Contract } from './contract.entity';

@Entity()
export class ContractComment extends BaseEntity {
  @Column({ type: 'text' })
  content: string;

  @Column()
  @Index()
  contractId: number;

  @ManyToOne(() => Contract, contract => contract.comments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'contractId' })
  contract: Contract;
}
