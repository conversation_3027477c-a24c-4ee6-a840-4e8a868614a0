import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1706659226717 implements MigrationInterface {
  name = 'Migrations1706659226717';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_9e3cb0ae2b24c39a55e84eed69"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "isManager"`);
    await queryRunner.query(`ALTER TYPE "public"."user_role_enum" RENAME TO "user_role_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."user_role_enum" AS ENUM('1', '2', '3', '4', '5')`,
    );
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "role" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "role" TYPE "public"."user_role_enum" USING "role"::"text"::"public"."user_role_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "role" SET DEFAULT '1'`);
    await queryRunner.query(`DROP TYPE "public"."user_role_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."user_role_enum_old" AS ENUM('1', '2', '3')`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "role" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "role" TYPE "public"."user_role_enum_old" USING "role"::"text"::"public"."user_role_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "role" SET DEFAULT '1'`);
    await queryRunner.query(`DROP TYPE "public"."user_role_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."user_role_enum_old" RENAME TO "user_role_enum"`);
    await queryRunner.query(`ALTER TABLE "user" ADD "isManager" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `CREATE INDEX "IDX_9e3cb0ae2b24c39a55e84eed69" ON "user" ("isManager") `,
    );
  }
}
