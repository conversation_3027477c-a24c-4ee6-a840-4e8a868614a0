import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1730469824002 implements MigrationInterface {
  name = 'Migrations1730469824002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" DROP CONSTRAINT "FK_b8468bd28b7c7c5eccd9e408de1"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_b8468bd28b7c7c5eccd9e408de"`);
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" DROP CONSTRAINT "REL_b8468bd28b7c7c5eccd9e408de"`,
    );
    await queryRunner.query(`ALTER TABLE "cancellation_settings" DROP COLUMN "companySettingsId"`);
    await queryRunner.query(`ALTER TABLE "company_settings" ADD "cancellationId" integer`);
    await queryRunner.query(
      `ALTER TABLE "company_settings" ADD CONSTRAINT "UQ_e00b096906dd073f5341bbd73c2" UNIQUE ("cancellationId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e00b096906dd073f5341bbd73c" ON "company_settings" ("cancellationId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "company_settings" ADD CONSTRAINT "FK_e00b096906dd073f5341bbd73c2" FOREIGN KEY ("cancellationId") REFERENCES "cancellation_settings"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "company_settings" DROP CONSTRAINT "FK_e00b096906dd073f5341bbd73c2"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_e00b096906dd073f5341bbd73c"`);
    await queryRunner.query(
      `ALTER TABLE "company_settings" DROP CONSTRAINT "UQ_e00b096906dd073f5341bbd73c2"`,
    );
    await queryRunner.query(`ALTER TABLE "company_settings" DROP COLUMN "cancellationId"`);
    await queryRunner.query(`ALTER TABLE "cancellation_settings" ADD "companySettingsId" integer`);
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" ADD CONSTRAINT "REL_b8468bd28b7c7c5eccd9e408de" UNIQUE ("companySettingsId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b8468bd28b7c7c5eccd9e408de" ON "cancellation_settings" ("companySettingsId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "cancellation_settings" ADD CONSTRAINT "FK_b8468bd28b7c7c5eccd9e408de1" FOREIGN KEY ("companySettingsId") REFERENCES "company_settings"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
