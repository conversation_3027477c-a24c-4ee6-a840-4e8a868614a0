import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNumber, IsOptional, IsBoolean, IsString } from 'class-validator';
import { processBoolean } from 'src/common/helpers/processBooleans';
import { processNumber } from 'src/common/helpers/processNumbers';

export class SecurityValidationDto {
  @ApiProperty()
  @IsNumber()
  @Transform(({ value }) => processNumber(value))
  jobId: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  isElectricalRisk?: boolean;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  @IsOptional()
  electricalRiskFile?: Express.Multer.File;

  @IsOptional()
  @IsString()
  electricalRiskKey?: string;

  @IsOptional()
  @IsString()
  electricalRiskPath?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  electricalRiskComment?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  isAccessCompliance?: boolean;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  @IsOptional()
  accessComplianceFile?: Express.Multer.File;

  @IsOptional()
  @IsString()
  accessComplianceKey?: string;

  @IsOptional()
  @IsString()
  accessCompliancePath?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  accessComplianceComment?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  isParkingCompliance?: boolean;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  @IsOptional()
  parkingComplianceFile?: Express.Multer.File;

  @IsOptional()
  @IsString()
  parkingComplianceKey?: string;

  @IsOptional()
  @IsString()
  parkingCompliancePath?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  parkingComplianceComment?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => processBoolean(value))
  @IsBoolean()
  isTerrainStability?: boolean;

  @ApiProperty({ type: 'string', format: 'binary', required: false })
  @IsOptional()
  terrainStabilityFile?: Express.Multer.File;

  @IsOptional()
  @IsString()
  terrainStabilityKey?: string;

  @IsOptional()
  @IsString()
  terrainStabilityPath?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  terrainStabilityComment?: string;
}
