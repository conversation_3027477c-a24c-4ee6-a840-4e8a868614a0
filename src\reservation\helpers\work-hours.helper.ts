import { WorkScheduleSettings } from 'src/company-settings/entities/work-schedule-settings.entity';

// Helper function to check if the time falls within working hours
export const isWithinWorkingHours = (
  reservationTime: Date,
  workSchedule: WorkScheduleSettings[],
): boolean => {
  const day = reservationTime.toLocaleString('en-us', { weekday: 'long' }).toLowerCase();
  const schedules = workSchedule.filter(w => w.day.toLowerCase() === day);

  if (schedules.length === 0) return false;

  const time = reservationTime.getHours() * 60 + reservationTime.getMinutes();

  // Check if the reservation time falls within any of the schedules for the day
  return schedules.some(schedule => {
    const start = parseTime(schedule.startTime);
    const end = parseTime(schedule.endTime);
    return time >= start && time <= end;
  });
};

// Helper function to parse time from HH:mm format to minutes of the day
export const parseTime = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

// Helper function to check if the reservation time is on a weekend or a non-working day
export const isWeekend = (reservationTime: Date, workSchedule: WorkScheduleSettings[]): boolean => {
  const day = reservationTime.toLocaleString('en-us', { weekday: 'long' }).toLowerCase();
  const schedules = workSchedule.filter(w => w.day.toLowerCase() === day);

  // If there is no work schedule for the given day, it is treated as a weekend/non-working day
  return schedules.length === 0;
};

// Helper function to check if it is night time
export const isNightTime = (
  reservationTime: Date,
  workSchedule: WorkScheduleSettings[],
): boolean => {
  const day = reservationTime.toLocaleString('en-us', { weekday: 'long' }).toLowerCase();
  const schedules = workSchedule.filter(w => w.day.toLowerCase() === day);

  if (schedules.length === 0) return false; // If no schedule is found, consider it as night time.

  const time = reservationTime.getHours() * 60 + reservationTime.getMinutes();

  // Check if the reservation time falls outside of all the schedules for the day
  return schedules.every(schedule => {
    const start = parseTime(schedule.startTime);
    const end = parseTime(schedule.endTime);
    return time < start || time > end;
  });
};

// Function to classify hours into work, night, and weekend
export const classifyHours = (
  startTime: Date,
  endTime: Date,
  workSchedule: WorkScheduleSettings[],
): { workHours: number; nightHours: number; weekendHours: number } => {
  let workHours = 0;
  let nightHours = 0;
  let weekendHours = 0;

  let currentTime = new Date(startTime);
  while (currentTime < endTime) {
    const nextHour = new Date(currentTime);
    nextHour.setHours(currentTime.getHours() + 1, 0, 0, 0);
    if (nextHour > endTime) nextHour.setTime(endTime.getTime());

    const day = currentTime.toLocaleString('en-us', { weekday: 'long' }).toLowerCase();
    const schedules = workSchedule.filter(w => w.day.toLowerCase() === day);
    const timeInMinutes = currentTime.getHours() * 60 + currentTime.getMinutes();

    if (schedules.length === 0) {
      weekendHours += (nextHour.getTime() - currentTime.getTime()) / (1000 * 60 * 60);
    } else {
      const isWorkHour = schedules.some(schedule => {
        const start = parseTime(schedule.startTime);
        const end = parseTime(schedule.endTime);
        return timeInMinutes >= start && timeInMinutes < end;
      });
      if (isWorkHour) {
        workHours += (nextHour.getTime() - currentTime.getTime()) / (1000 * 60 * 60);
      } else {
        nightHours += (nextHour.getTime() - currentTime.getTime()) / (1000 * 60 * 60);
      }
    }
    currentTime = nextHour;
  }

  return { workHours, nightHours, weekendHours };
};
