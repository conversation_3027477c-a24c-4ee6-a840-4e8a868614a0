import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739806208608 implements MigrationInterface {
  name = 'Migrations1739806208608';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "isPublic" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "isPublic"`);
  }
}
