import { AnalyticsService } from './v1/analytics.service';
import { AnalyticsController } from './v1/analytics.controller';
import { Module } from '@nestjs/common';
import { RedisModule } from 'src/libs/redis/redis.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Reservation } from 'src/reservation/entities/reservation.entity';
import { Vehicle } from 'src/vehicle/entities/vehicle.entity';

@Module({
  imports: [RedisModule, TypeOrmModule.forFeature([Reservation, Vehicle])],
  controllers: [AnalyticsController],
  providers: [AnalyticsService],
})
export class AnalyticsModule {}
