import { Module, forwardRef } from '@nestjs/common';
import { ReservationService } from './v1/reservation.service';
import { ReservationController } from './v1/reservation.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Reservation } from './entities/reservation.entity';
import { Pricelist } from 'src/pricelist/entities/pricelist.entity';
import { UsersModule } from 'src/users/users.module';
import { JobModule } from 'src/job/job.module';
import { VehicleModule } from 'src/vehicle/vehicle.module';
import { ReservationCronService } from './reservation-cron.service';
import { FilesService } from 'src/s3/files.service';
import { BullmqModule } from 'src/libs/bullmq/bullmq.module';
import { ProcessReservationInvoiceConsumer } from './workers/reservation-invoice.worker';
import { PdfService } from 'src/pdf-report/pdf.service';
import { MailerModule } from 'src/mailer/mailer.module';
import { MailerService } from 'src/mailer/mailer.service';
import { InvoiceLogModule } from 'src/invoice-log/invoice-log.module';
import { ProcessReservationBulkInvoiceConsumer } from './workers/reservation-bulk-invoice.worker';
import { CompanyModule } from 'src/company/company.module';
import { ContractModule } from 'src/contract/contract.module';
import { CompanySettingsModule } from 'src/company-settings/company-settings.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Reservation, Pricelist]),
    forwardRef(() => UsersModule),
    forwardRef(() => JobModule),
    forwardRef(() => VehicleModule),
    BullmqModule,
    MailerModule,
    forwardRef(() => InvoiceLogModule),
    forwardRef(() => CompanyModule),
    forwardRef(() => CompanySettingsModule),
    ContractModule,
  ],
  controllers: [ReservationController],
  providers: [
    ReservationService,
    ReservationCronService,
    FilesService,
    ProcessReservationInvoiceConsumer,
    ProcessReservationBulkInvoiceConsumer,
    PdfService,
    MailerService,
  ],
  exports: [ReservationService],
})
export class ReservationModule {}
