import {
  Injectable,
  Logger,
  NotFoundException,
  forwardRef,
  Inject,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { JobService } from 'src/job/v1/job.service';
import { DataSource, Repository } from 'typeorm';
import { JobLocation } from '../entities/job-location.entity';
import { CreateJobLocationDto } from './dto/create-job-location.dto';
import { GetJobLocationDto, JobLocationCategoryTypes } from './dto/get-job-location.dto';
import { UpdateJobLocationDto } from './dto/update-job-location.dto';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import {
  addExpressions,
  ConditionType,
  Expression,
  getQueryLimit,
  getQuerySort,
  getWhereQuery,
  SortDirections,
} from 'src/libs/helpers/CeQuery';
import {
  getJobLocationCountQuery,
  getJobLocationQuery,
} from '../queries/get-job-location-raw-query';

@Injectable()
export class JobLocationService {
  private readonly logger = new Logger(JobLocationService.name);

  constructor(
    @InjectRepository(JobLocation)
    private jobLocationRepository: Repository<JobLocation>,
    @Inject(forwardRef(() => JobService))
    private jobService: JobService,
    private dataSource: DataSource,
  ) {}

  async create(createJobLocationDto: CreateJobLocationDto, userId: number): Promise<JobLocation> {
    this.logger.log({
      method: 'createJobLocation',
      userId,
    });

    const jobLocation = this.jobLocationRepository.create({
      ...createJobLocationDto,
      created_by: userId,
    });

    if (createJobLocationDto.jobId) {
      const job = await this.jobService.findOneById(createJobLocationDto.jobId);
      if (job) {
        jobLocation.job = job;
      } else {
        jobLocation.job = null;
      }
    }

    const savedJobLocation = await this.jobLocationRepository.save(jobLocation);

    this.logger.log({
      method: 'createJobLocation',
      jobLocationId: savedJobLocation.id,
      userId,
    });

    return savedJobLocation;
  }

  async findMany(
    body: GetJobLocationDto,
    shouldReturnTotalCount = true,
  ): Promise<EntityWithCountDto<JobLocation>> {
    this.logger.log({ method: 'findMany', body });
    const { limit, offset, sortModel = [], relations = [], expressions = [] } = body;
    const queryRunner = this.dataSource.createQueryRunner('slave');

    const expressionsToAdd: Expression[] = [];

    if (!sortModel.length) {
      sortModel.push({ field: JobLocationCategoryTypes.jobLocationId, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const jobLocationsSQL = getJobLocationQuery(whereQuery.query, querySort, queryLimit, relations);

    try {
      let totalCount = 0;

      const jobLocations = await queryRunner.manager.query(jobLocationsSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getJobLocationCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({
        method: 'findMany',
        query: body,
        jobLocationsLength: jobLocations.length,
      });
      return new EntityWithCountDto<JobLocation>(JobLocation, jobLocations, totalCount);
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(id: number): Promise<JobLocation> {
    this.logger.log({ method: 'findOneById', id });

    const { data: jobLocations } = await this.findMany(
      {
        expressions: [
          { category: JobLocationCategoryTypes.jobLocationId, operator: '=', value: id },
        ],
        limit: 1,
        sortModel: [],
      },
      false,
    );

    if (jobLocations.length == 0) {
      throw new NotFoundException('Job Location was not found.');
    }

    this.logger.log({
      method: 'findOneById',
      id,
    });
    return jobLocations[0];
  }

  async update(
    id: number,
    updateJobLocationDto: UpdateJobLocationDto,
    updatingUserId?: number,
  ): Promise<JobLocation> {
    this.logger.log({
      method: 'updateJobLocation',
      jobLocationId: id,
      updatingUser: updatingUserId,
    });

    const jobLocation = await this.findOneById(id);
    if (!jobLocation) {
      throw new NotFoundException(`JobLocation with ID ${id} not found`);
    }

    Object.assign(jobLocation, updateJobLocationDto);

    if (updateJobLocationDto.jobId) {
      const job = await this.jobService.findOneById(updateJobLocationDto.jobId);
      if (job) {
        jobLocation.job = job;
      } else {
        jobLocation.job = null;
      }
    }
    const savedJobLocation = await this.jobLocationRepository.save(jobLocation);

    this.logger.log({
      method: 'updateJobLocation',
      jobLocationId: savedJobLocation.id,
      updatingUser: updatingUserId,
    });
    return savedJobLocation;
  }

  async remove(id: number): Promise<void> {
    this.logger.log({ method: 'removeJobLocation', id });

    const result = await this.jobLocationRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`JobLocation with ID ${id} not found`);
    }
  }
}
