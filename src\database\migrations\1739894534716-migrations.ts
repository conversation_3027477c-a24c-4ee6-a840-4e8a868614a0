import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739894534716 implements MigrationInterface {
  name = 'Migrations1739894534716';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "partner" DROP CONSTRAINT "FK_09390d3b33e820f8b58d0834157"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_a3a88ad51cf1cc45a92bec25d26"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "UQ_ca64c216528e5eeda194bb5358e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner" RENAME COLUMN "pricelistId" TO "containerPricelistsId"`,
    );
    await queryRunner.query(
      `CREATE TABLE "container_pricelists" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "title" character varying, "isPublic" boolean NOT NULL DEFAULT false, "partnerId" integer, "companyId" integer NOT NULL, CONSTRAINT "REL_cb009f4b6d93f0b9751c624490" UNIQUE ("partnerId"), CONSTRAINT "PK_e563d5fd409304c54957af30ec2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cb009f4b6d93f0b9751c624490" ON "container_pricelists" ("partnerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_31ea38cbe9e249f385117e677b" ON "container_pricelists" ("companyId") `,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "parentId"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "isPublic"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "containerId" integer`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "UQ_ada0566e4a8b5c66655f9334cd9" UNIQUE ("vehicleId", "containerId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner" ADD CONSTRAINT "FK_7f7ce8585b3fde2f2ac9f21b50a" FOREIGN KEY ("containerPricelistsId") REFERENCES "container_pricelists"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" ADD CONSTRAINT "FK_cb009f4b6d93f0b9751c624490c" FOREIGN KEY ("partnerId") REFERENCES "partner"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_558c85cb0085c72e403f793cfb1" FOREIGN KEY ("containerId") REFERENCES "container_pricelists"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_558c85cb0085c72e403f793cfb1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "container_pricelists" DROP CONSTRAINT "FK_cb009f4b6d93f0b9751c624490c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner" DROP CONSTRAINT "FK_7f7ce8585b3fde2f2ac9f21b50a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "UQ_ada0566e4a8b5c66655f9334cd9"`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "containerId"`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD "isPublic" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "parentId" integer`);
    await queryRunner.query(`DROP INDEX "public"."IDX_31ea38cbe9e249f385117e677b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cb009f4b6d93f0b9751c624490"`);
    await queryRunner.query(`DROP TABLE "container_pricelists"`);
    await queryRunner.query(
      `ALTER TABLE "partner" RENAME COLUMN "containerPricelistsId" TO "pricelistId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "UQ_ca64c216528e5eeda194bb5358e" UNIQUE ("parentId", "vehicleId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_a3a88ad51cf1cc45a92bec25d26" FOREIGN KEY ("parentId") REFERENCES "pricelist"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "partner" ADD CONSTRAINT "FK_09390d3b33e820f8b58d0834157" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
