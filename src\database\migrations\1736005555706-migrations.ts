import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1736005555706 implements MigrationInterface {
  name = 'Migrations1736005555706';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_00e5ddee29dd30d0433ff418418"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "UQ_00e5ddee29dd30d0433ff418418"`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "vehicleTypeId"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "weight"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "height"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "length"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "width"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "maxVerticalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "maxHorizontalReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "endHoseLength"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "maxDownwardReach"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "minUnfoldingHeight"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "boomRotation"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "frontOutriggerSpan"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "rearOutriggerSpan"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "frontPressureOnOutrigger"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "rearPressureOnOutrigger"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "frontSideOpening"`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" DROP COLUMN "rearSideOpening"`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" DROP COLUMN "pipeLengthForSecondTechnician"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" ADD "pipeLengthForSecondTechnician" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "rearSideOpening" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "frontSideOpening" double precision`);
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" ADD "rearPressureOnOutrigger" double precision`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle_size" ADD "frontPressureOnOutrigger" double precision`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "rearOutriggerSpan" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "frontOutriggerSpan" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "boomRotation" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "minUnfoldingHeight" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "maxDownwardReach" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "endHoseLength" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "maxHorizontalReach" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "maxVerticalReach" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "width" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "length" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "height" double precision`);
    await queryRunner.query(`ALTER TABLE "vehicle_size" ADD "weight" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "vehicleTypeId" integer`);
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "UQ_00e5ddee29dd30d0433ff418418" UNIQUE ("vehicleTypeId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_00e5ddee29dd30d0433ff418418" FOREIGN KEY ("vehicleTypeId") REFERENCES "vehicle_type"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
