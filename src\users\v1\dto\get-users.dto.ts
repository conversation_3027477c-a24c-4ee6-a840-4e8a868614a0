import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsOptional, ValidateNested } from 'class-validator';
import { Role } from 'src/common/constants/roles.enum';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';

export enum UserSearchType {
  MANAGER = 'getAllManagers',
  OPERATOR = 'getAllOperators',
  DISPATCHER = 'getAllDispatchers',
}

export enum UserCategoryTypes {
  id = '"user"."id"',
  role = '"user"."role"',
  email = '"user"."email"',
  firstName = '"user"."firstName"',
  lastName = '"user"."lastName"',
  status = '"user"."status"',
  createdBy = '"user"."created_by"',
  companyId = '"user"."companyId"',
  companyName = '"company"."name"',
}

export class GetUsersQueryParam extends CommonQueryParams {
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UserSortItem)
  readonly sortModel: UserSortItem[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UserExpression)
  readonly expressions: UserExpression[];

  @ApiProperty({ enum: Role, enumName: 'Role', required: false })
  @IsOptional()
  role?: Role;

  @ApiProperty({
    enum: UserSearchType,
    enumName: 'UserSearchType',
    required: false,
  })
  @IsOptional()
  type?: UserSearchType;
}

export class UserSortItem extends SortItem {
  @IsEnum(UserCategoryTypes, { message: 'invalid sort field' })
  field: string;
}

export class UserExpression extends Expression {
  @IsOptional()
  @IsEnum(UserCategoryTypes, { message: 'invalid filter category' })
  category?: string | null | undefined;
}
