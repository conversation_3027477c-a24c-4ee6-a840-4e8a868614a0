import { <PERSON>tity, Column, ManyToOne, Index, JoinColumn, Unique } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Vehicle } from 'src/vehicle/entities/vehicle.entity';
import { ContainerPricelists } from './container-pricelists.entity';

export interface PumpTier {
  name: string;
  price: number;
  minimum: number;
  maximum: number;
}

export interface TransportRate {
  name: string;
  tariff: number;
  from: number;
  to: number;
}

export interface Vat {
  name: string;
  percentage: number;
}

@Entity()
@Unique(['vehicleId', 'containerId'])
export class Pricelist extends BaseEntity {
  @Column({ nullable: true })
  title: string;

  @Column({ nullable: true, type: 'float' })
  packageFlatFee: number;

  @Column({ nullable: true, type: 'float' })
  pricePerMeterPumped: number;

  @Column({ nullable: true, type: 'float' })
  additionalHour: number;

  @Column({ nullable: true, type: 'float' })
  cleaningFee: number;

  @Column({ nullable: true, type: 'float' })
  pricePerMeterOfFlexiblePipeLength80Mm: number;

  @Column({ nullable: true, type: 'float' })
  pricePerMeterOfFlexiblePipeLength90Mm: number;

  @Column({ nullable: true, type: 'float' })
  pricePerMeterOfFlexiblePipeLength100Mm: number;

  @Column({ nullable: true, type: 'float' })
  pricePerMeterOfRigidPipeLength120Mm: number;

  @Column({ nullable: true, type: 'float' })
  supplyOfTheChemicalSlushie: number;

  @Column({ nullable: true, type: 'float' })
  barbotine: number;

  @Column({ nullable: true, type: 'float' })
  extraCementBagPrice: number;

  @Column({ nullable: true, type: 'int' })
  packageFlatFeeDuration: number;

  @Column({ nullable: true, type: 'int' })
  pipeInvoicingStartsFrom: number;

  @Column({ nullable: true, type: 'int' })
  containerId: number;

  @Column({ nullable: true, type: 'jsonb' })
  pumpTiers: PumpTier[];

  @Column({ nullable: true, type: 'jsonb' })
  transportRates: TransportRate[];

  @Column({ nullable: true, type: 'float' })
  dayContractFee: number;

  @Column({ nullable: true, type: 'int' })
  dayContractDuration: number;

  @Column({ nullable: true, type: 'float' })
  dayContractOvertimeRate: number;

  @Column({ nullable: true, type: 'float' })
  packageFlatFeeWeekend: number;

  @Column({ nullable: true, type: 'float' })
  additionalHourWeekend: number;

  @Column({ nullable: true, type: 'float' })
  packageFlatFeeNight: number;

  @Column({ nullable: true, type: 'float' })
  additionalHourNight: number;

  @Column({ nullable: true, type: 'float' })
  minimumChargeFlatFee: number;

  @Column({ nullable: true, type: 'int' })
  minimumM3Charged: number;

  @Column({ nullable: true, type: 'float' })
  dayCancellationFee: number;

  @Column({ nullable: true, type: 'float' })
  cancellationFee: number;

  @Column({ nullable: true, type: 'float' })
  secondTechnicianHourFee: number;

  @Column({ nullable: true, type: 'float' })
  secondTechnicianHourFeeNight: number;

  @Column({ nullable: true, type: 'float' })
  secondTechnicianHourFeeWeekend: number;

  @Column({ type: 'jsonb', nullable: true })
  vat?: Vat;

  @Column({ type: 'boolean', default: false })
  isContract: boolean;

  @Column({ nullable: true, type: 'interval' })
  contractCancellationPeriod: string;

  @Column({ nullable: true, type: 'interval' })
  contractLateCancellationPeriod: string;

  @ManyToOne(() => ContainerPricelists, entity => entity.pricelists, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'containerId' })
  container: ContainerPricelists;

  @Column({ nullable: true })
  @Index()
  vehicleId: number;

  @ManyToOne(() => Vehicle, entity => entity.pricelists, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'vehicleId' })
  vehicle?: Vehicle;
}
