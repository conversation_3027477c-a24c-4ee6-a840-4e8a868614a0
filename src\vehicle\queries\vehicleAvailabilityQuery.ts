export const getVehicleAvailabilityQuery = (
  queryBuilder: any,
  dateFrom: Date,
  dateTo: Date,
  currentReservationId?: number,
) => {
  queryBuilder.andWhere(
    `(NOT EXISTS (
        SELECT 1 FROM reservation
        LEFT JOIN job ON reservation."jobId" = job.id
        WHERE reservation."vehicleId" = vehicle.id
          AND (reservation.id != :currentReservationId OR :currentReservationId IS NULL)
          AND reservation."dateFrom" <= :dateTo
          AND reservation."dateTo" >= :dateFrom
          AND job."status" IS NOT NULL
          AND job."status" NOT IN ('CANCELLED', 'COMPLETE')
      ))
      AND
      ((NOT EXISTS (
        SELECT 1 FROM "unavailable_period_vehicles" upv
        INNER JOIN "unavailable_period" up ON upv."unavailablePeriodId" = up.id
        WHERE upv."vehicleId" = vehicle.id
          AND up."isHidden" = false
      ))
      OR (NOT EXISTS (
        SELECT 1 FROM "unavailable_period_vehicles" upv
        INNER JOIN "unavailable_period" up ON upv."unavailablePeriodId" = up.id
        WHERE upv."vehicleId" = vehicle.id
          AND up."from" <= :dateTo
          AND up."to" >= :dateFrom
          AND up."isHidden" = false
      )))`,
    { dateFrom, dateTo, currentReservationId },
  );
};
