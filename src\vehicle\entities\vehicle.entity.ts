import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '../../common/entities/base.entity';
import { User } from 'src/users/entities/user.entity';
import {
  Column,
  Entity,
  Geometry,
  Index,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { UnavailablePeriod } from 'src/unavailable-period/entities/unavailable-period.entity';
import { Reservation } from 'src/reservation/entities/reservation.entity';
import { VehicleType } from 'src/common/constants/vehicle-type.enum';
import { Pricelist } from 'src/pricelist/entities/pricelist.entity';
import { Status } from 'src/common/constants/status.enum';
import { Contract } from 'src/contract/entities/contract.entity';

export class Location {
  @ApiProperty()
  type: string;
  @ApiProperty({ type: [Number] })
  coordinates: number[];
}

@Entity()
export class Vehicle extends BaseEntity {
  @Column({ nullable: true })
  @Index()
  operatorId: number;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'operatorId' })
  operator?: User;

  @Column({ nullable: true })
  @Index()
  managerId: number;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'managerId' })
  manager?: User;

  @Column({
    type: 'enum',
    enum: VehicleType,
    nullable: true,
  })
  type?: VehicleType;

  @ManyToMany(() => UnavailablePeriod, unavailablePeriod => unavailablePeriod.vehicles)
  unavailablePeriods: UnavailablePeriod[];

  @Column({ nullable: true })
  @Index()
  boomSize: number;

  @Column({ nullable: true })
  typeOfMotorization: string;

  @Column({ nullable: true })
  vehicleBrand: string;

  @Column({ nullable: true })
  brandModel: string;

  @Column({ nullable: true })
  @Index()
  licensePlateNumber: string;

  @Column({ nullable: true, type: 'float' })
  weight: number;

  @Column({ nullable: true, type: 'float' })
  height: number;

  @Column({ nullable: true, type: 'float' })
  length: number;

  @Column({ nullable: true, type: 'float' })
  width?: number;

  @Column({ nullable: true, type: 'float' })
  maxVerticalReach: number;

  @Column({ nullable: true, type: 'float' })
  maxHorizontalReach: number;

  @Column({ nullable: true, type: 'float' })
  endHoseLength?: number;

  @Column({ nullable: true, type: 'float' })
  maxFlowRate: number;

  @Column({ nullable: true, type: 'float' })
  maxConcretePressure?: number;

  @Column({ nullable: true, type: 'float' })
  availableFlexiblePipeLength80Mm: number;

  @Column({ nullable: true, type: 'float' })
  availableFlexiblePipeLength90Mm: number;

  @Column({ nullable: true, type: 'float' })
  availableFlexiblePipeLength100Mm: number;

  @Column({ nullable: true, type: 'float' })
  availableFlexiblePipeLength120Mm: number;

  @Column({ nullable: true, type: 'float' })
  maxDownwardReach: number;

  @Column({ nullable: true, type: 'float' })
  numberOfBoomSections?: number;

  @Column({ nullable: true, type: 'float' })
  minUnfoldingHeight?: number;

  @Column({ nullable: true, type: 'float' })
  boomRotation?: number;

  @Column({ nullable: true, type: 'float' })
  frontOutriggerSpan?: number;

  @Column({ nullable: true, type: 'float' })
  rearOutriggerSpan?: number;

  @Column({ nullable: true, type: 'float' })
  frontPressureOnOutrigger?: number;

  @Column({ nullable: true, type: 'float' })
  rearPressureOnOutrigger?: number;

  @Column({ nullable: true })
  boomUnfoldingSystem: string;

  @Column({ default: false })
  bacExit: boolean;

  @Column({ default: false })
  bacExitReverse: boolean;

  @Column({ nullable: true })
  siteAddress: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  plz: number;

  @Column({ default: false })
  hasStrangler: boolean;

  @Column({ nullable: true, type: 'float' })
  frontSideOpening: number;

  @Column({ nullable: true, type: 'float' })
  rearSideOpening: number;

  @Column({ nullable: true, type: 'float' })
  invoicingPipesFrom: number;

  @Column({ nullable: true, type: 'float' })
  pipeLengthForSecondTechnician: number;

  @Column({
    type: 'geometry',
    spatialFeatureType: 'Point',
    srid: 4326,
    nullable: true,
  })
  location: Geometry;

  @Column({ nullable: true })
  @Index()
  uniqueIdentificationNumber: string;

  @Column({ nullable: true })
  @Index()
  completedJobs: number;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE,
  })
  @Index()
  status: number;

  @OneToMany(() => Reservation, reservation => reservation.vehicle)
  reservations?: Reservation[];

  @OneToMany(() => Pricelist, pricelist => pricelist.vehicle, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  pricelists?: Pricelist[];

  @OneToMany(() => Contract, contract => contract.vehicle, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  contracts?: Contract[];

  @Column({ nullable: true })
  imageUrl?: string;
}
