import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1709060383080 implements MigrationInterface {
  name = 'Migrations1709060383080';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."job_parkingon_enum" RENAME TO "job_parkingon_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."job_parkingon_enum" AS ENUM('Public Road', 'Private Road', 'Workside')`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "parkingOn" TYPE "public"."job_parkingon_enum" USING "parkingOn"::"text"::"public"."job_parkingon_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."job_parkingon_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."job_parkingon_enum_old" AS ENUM('Public Road', 'Private Road', 'WorkSide')`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ALTER COLUMN "parkingOn" TYPE "public"."job_parkingon_enum_old" USING "parkingOn"::"text"::"public"."job_parkingon_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."job_parkingon_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."job_parkingon_enum_old" RENAME TO "job_parkingon_enum"`,
    );
  }
}
