// get-unavailable-periods.dto.ts

import { IsOptional, IsEnum, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';
import { Expression, SortItem } from 'src/libs/helpers/CeQuery';
import { CommonQueryParams } from 'src/common/dto/common-query-params.dto';
import { ApiProperty } from '@nestjs/swagger';

export enum UnavailablePeriodCategoryTypes {
  id = '"unavailable_period"."id"',
  createdBy = '"unavailable_period"."created_by"',
  from = '"unavailable_period"."from"',
  to = '"unavailable_period"."to"',
  vehicleIds = '"vehicles"."id"',
  contractId = '"unavailable_period"."contractId"',
  vehicleManagerId = '"vehicles"."managerId"',
  operatorId = '"operator"."id"',
  operatorManagerId = '"operator_manager"."id"',
  companyId = '"unavailable_period"."companyId"',
  isHidden = '"unavailable_period"."isHidden"',
}

export class UnavailablePeriodExpression extends Expression {
  @ApiProperty({
    required: false,
    enum: UnavailablePeriodCategoryTypes,
  })
  @IsOptional()
  @IsEnum(UnavailablePeriodCategoryTypes, {
    message: 'Invalid filter category',
  })
  category?: string | null | undefined;
}

export class UnavailablePeriodSortItem extends SortItem {
  @ApiProperty({
    required: false,
    enum: UnavailablePeriodCategoryTypes,
  })
  @IsEnum(UnavailablePeriodCategoryTypes, {
    message: 'Invalid sort field',
  })
  field: string;
}

export class GetUnavailablePeriodsDto extends CommonQueryParams {
  @ApiProperty({ required: false, isArray: true, type: UnavailablePeriodExpression })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UnavailablePeriodExpression)
  expressions: UnavailablePeriodExpression[] = [];

  @ApiProperty({ required: false, isArray: true, type: UnavailablePeriodSortItem })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UnavailablePeriodSortItem)
  sortModel: UnavailablePeriodSortItem[] = [];

  @ApiProperty({ required: false })
  @IsArray()
  @IsOptional()
  readonly relations?: string[];
}
