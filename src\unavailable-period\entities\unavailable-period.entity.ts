import { <PERSON>tity, Column, ManyToOne, JoinColumn, Index, JoinTable, ManyToMany } from 'typeorm';
import { BaseEntity } from '../../common/entities/base.entity';
import { Vehicle } from 'src/vehicle/entities/vehicle.entity';
import { User } from 'src/users/entities/user.entity';
import { Company } from 'src/company/entities/company.entity';
import { Contract } from 'src/contract/entities/contract.entity';

@Entity()
export class UnavailablePeriod extends BaseEntity {
  @Column({ type: 'timestamp' })
  @Index()
  from: Date;

  @Column({ type: 'timestamp' })
  @Index()
  to: Date;

  @ManyToMany(() => Vehicle, vehicle => vehicle.unavailablePeriods, { nullable: true })
  @JoinTable({
    name: 'unavailable_period_vehicles',
    joinColumn: { name: 'unavailablePeriodId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'vehicleId', referencedColumnName: 'id' },
  })
  vehicles?: Vehicle[];

  @ManyToOne(() => User, user => user.operatorManagerUnavailablePeriods, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'operatorManagerId' })
  operatorManager?: User;

  @Column({ nullable: true })
  operatorId?: number;

  @ManyToOne(() => User, user => user.operatorUnavailablePeriods, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'operatorId' })
  operator?: User;

  @Column({ nullable: true })
  @Index()
  companyId: number;

  @ManyToOne(() => Company, entity => entity.users, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'companyId' })
  company?: Company;

  @Column({ nullable: true })
  @Index()
  contractId: number; // Indicates the contract responsible for creating this unavailable period.

  @ManyToOne(() => Contract, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'contractId' })
  contract?: Contract;

  @Column({ type: 'text', nullable: true })
  reason?: string;

  @Column({ type: 'boolean', default: false })
  @Index()
  isHidden: boolean; // If true, vehicles are hidden from dispatchers but remain available (visible only to operator managers)
}
