# syntax=docker/dockerfile:1

ARG NODE_VERSION=18.14.2

FROM node:${NODE_VERSION}-alpine AS base
WORKDIR /build
COPY --chown=node:node package*.json ./

FROM base AS build
RUN npm ci
COPY --chown=node:node . .
RUN npm run build

FROM base AS prod
ENV NODE_ENV=production
RUN  npm ci --omit=dev

FROM node:${NODE_VERSION}-alpine AS prod-base
WORKDIR /srv
COPY --chown=node:node package.json .
COPY --chown=node:node --from=prod /build/node_modules ./node_modules
COPY --chown=node:node --from=build /build/dist ./dist
USER node
EXPOSE 3001
ENTRYPOINT [ "node", "dist/main.js" ]
