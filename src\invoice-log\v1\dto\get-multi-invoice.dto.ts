import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsNumber, IsString, IsEnum } from 'class-validator';
import { processNumber } from 'src/common/helpers/processNumbers';
import { InvoiceType } from 'src/invoice-log/enums/invoice-type.enum';

export class GetMultiInvoiceDto {
  @ApiProperty({
    description: 'The ID of the reservation',
    required: false,
    type: Number,
  })
  @IsOptional()
  @Transform(({ value }) => processNumber(value))
  @IsNumber()
  reservationId?: number;

  @ApiProperty({
    description: 'The invoice number associated with the reservations',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  invoiceNumber?: string;

  @ApiProperty({
    description: 'The type of invoice (single or bulk)',
    required: false,
    enum: InvoiceType,
    default: InvoiceType.BULK,
  })
  @IsOptional()
  @IsEnum(InvoiceType)
  invoiceType?: InvoiceType = InvoiceType.BULK;
}
