import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { EntityWithCountDto } from 'src/common/dto/entity-with-counts.dto';
import { SerializedUser } from 'src/common/decorators/roles.decorator';
import {
  Expression,
  QueryOperator,
  SortDirections,
  addExpressions,
  ConditionType,
  getWhereQuery,
  getQuerySort,
  getQueryLimit,
} from 'src/libs/helpers/CeQuery';
import { ContainerPricelists } from '../entities/container-pricelists.entity';
import { CreateContainerPricelistsDto } from './dto/create-container-pricelists.dto';
import { UpdateContainerPricelistsDto } from './dto/update-container-pricelists.dto';
import { ContainerCategoryTypes, GetContainersDto } from './dto/get-container-pricelists.dto';
import { getContainersCountQuery, getContainersQuery } from '../queries/get-container-query';
import { InjectRepository } from '@nestjs/typeorm';
import { PartnerService } from 'src/partner/v1/partner.service';
import { PricelistService } from './pricelist.service';

@Injectable()
export class ContainerPricelistsService {
  private readonly logger = new Logger(ContainerPricelistsService.name);

  constructor(
    @InjectRepository(ContainerPricelists)
    private containerRepository: Repository<ContainerPricelists>,
    @Inject(forwardRef(() => PartnerService))
    private partnerService: PartnerService,
    @Inject(forwardRef(() => PricelistService))
    private pricelistService: PricelistService,
    private dataSource: DataSource,
  ) {}

  async create(
    createContainerPricelistsDto: CreateContainerPricelistsDto,
    creatingUser: SerializedUser,
  ): Promise<ContainerPricelists> {
    const userId = creatingUser?.sub;
    const companyId = creatingUser?.companyId;
    this.logger.log({ method: 'create', creatingUser: userId });

    const { partnerIds, copyFromContainerId, ...options } = createContainerPricelistsDto;

    const container = this.containerRepository.create({
      ...options,
      companyId,
      created_by: userId,
    });
    const savedContainer = await this.containerRepository.save(container);

    if (copyFromContainerId) {
      const copyFromContainer = await this.findOneById(copyFromContainerId);
      await this.pricelistService.createMany(
        {
          pricelistsDto: copyFromContainer.pricelists,
          containerId: savedContainer.id,
        },
        creatingUser,
      );
    }

    if (partnerIds && partnerIds.length > 0) {
      await Promise.all(
        partnerIds.map(async partnerId => {
          await this.partnerService.assignContainersById(partnerId, {
            containerIds: [savedContainer.id],
          });
        }),
      );
    }

    this.logger.log({
      method: 'create',
      containerId: savedContainer.id,
      creatingUser: userId,
    });

    return savedContainer;
  }

  async findMany(
    body: GetContainersDto,
    fetchingUser?: SerializedUser,
    shouldReturnTotalCount: boolean = true,
  ): Promise<EntityWithCountDto<ContainerPricelists>> {
    this.logger.log({ method: 'findMany' });
    const { limit, offset, sortModel = [], relations = ['pricelists', 'partners'] } = body;
    const companyId = fetchingUser?.companyId;
    const queryRunner = this.dataSource.createQueryRunner('slave');
    const expressionsToAdd: Expression[] = [];
    if (companyId) {
      expressionsToAdd.push({
        category: ContainerCategoryTypes.companyId,
        operator: '=' as QueryOperator,
        value: companyId,
        conditionType: 'AND',
      });
    }

    if (!sortModel.length) {
      sortModel.push({ field: ContainerCategoryTypes.id, sort: SortDirections.ASC });
    }

    const expressionsWithOtherData: Expression[] = addExpressions(
      expressionsToAdd,
      body.expressions,
      'AND' as ConditionType,
    );

    const whereQuery = getWhereQuery(expressionsWithOtherData);
    const querySort = getQuerySort(sortModel);
    const queryLimit = getQueryLimit(limit, offset);

    const generatedQueryConditions = `${whereQuery.query} ${querySort} ${queryLimit}`;
    const containersSQL = getContainersQuery(whereQuery.query, querySort, queryLimit, relations);

    try {
      let totalCount = 0;

      const containers = await queryRunner.manager.query(containersSQL, whereQuery.params);

      if (shouldReturnTotalCount) {
        const totalCountRaw = await queryRunner.manager.query(
          getContainersCountQuery(whereQuery.query, relations),
          whereQuery.params,
        );
        totalCount = totalCountRaw[0]?.totalCount ? Number(totalCountRaw[0].totalCount) : 0;
      }

      this.logger.log({ method: 'findMany', body, containersLength: containers.length });

      return new EntityWithCountDto<ContainerPricelists>(
        ContainerPricelists,
        containers,
        totalCount,
      );
    } catch (e) {
      this.logger.error({
        method: 'findMany',
        errorMessage: e.message,
        error: e,
        generatedQueryConditions,
      });
      throw new BadRequestException('Unable to get the data with that query');
    } finally {
      await queryRunner.release();
    }
  }

  async findOneById(
    id: number,
    fetchingUser?: SerializedUser,
  ): Promise<ContainerPricelists | null> {
    this.logger.log({ method: 'findOneById', id });

    const body: GetContainersDto = {
      expressions: [
        {
          category: ContainerCategoryTypes.id,
          operator: '=' as QueryOperator,
          value: id,
          conditionType: 'AND',
        },
      ],
      limit: 1,
      offset: 0,
      relations: ['partners', 'pricelists'],
      sortModel: [],
    };

    const result = await this.findMany(body, fetchingUser, false);

    const [container] = result.data;

    if (!container) {
      throw new NotFoundException('Container was not found!');
    }

    this.logger.log({ method: 'findOneById', id, containerId: container.id });
    return container;
  }

  async update(
    id: number,
    updateContainerDto: UpdateContainerPricelistsDto,
    updatingUser: SerializedUser,
  ): Promise<ContainerPricelists> {
    this.logger.log({
      method: 'update',
      containerId: id,
      updatingUser: updatingUser.sub,
    });
    const { partnerIds, ...options } = updateContainerDto;

    const container = await this.containerRepository.findOne({
      where: { id },
      relations: ['partners'],
    });

    if (!container) {
      throw new HttpException('Container was not found!', HttpStatus.NOT_FOUND);
    }

    if (partnerIds !== undefined) {
      const currentPartnerIds = container.partners?.map(p => p.id) || [];
      const newPartnerIds = partnerIds || [];
      const partnersToRemove = currentPartnerIds.filter(id => !newPartnerIds.includes(id));

      const partnersToAdd = newPartnerIds.filter(id => !currentPartnerIds.includes(id));

      if (partnersToRemove.length > 0) {
        await Promise.all(
          partnersToRemove.map(async partnerId => {
            await this.partnerService.removeContainersById(partnerId, {
              containerIds: [container.id],
            });
          }),
        );
      }

      if (partnersToAdd.length > 0) {
        await Promise.all(
          partnersToAdd.map(async partnerId => {
            await this.partnerService.assignContainersById(
              partnerId,
              { containerIds: [container.id] },
              true,
            );
          }),
        );
      }
      delete container.partners;
    }
    Object.assign(container, options);

    const savedContainer = await this.containerRepository.save(container);

    this.logger.log({
      method: 'update',
      containerId: container.id,
      updatingUser: updatingUser.sub,
    });
    return savedContainer;
  }

  async setAsDefault(id: number, updatingUser: SerializedUser): Promise<ContainerPricelists> {
    this.logger.log({
      method: 'setAsDefault',
      containerId: id,
      updatingUser: updatingUser.sub,
    });

    const container = await this.findOneById(id, updatingUser);

    const body: GetContainersDto = {
      expressions: [
        {
          category: ContainerCategoryTypes.isDefault,
          operator: '=' as QueryOperator,
          value: true,
          conditionType: 'AND',
        },
      ],
      limit: 1,
      offset: 0,
      relations: [],
      sortModel: [],
    };

    const result = await this.findMany(body, updatingUser, false);
    const [lastDefaultContainer] = result.data;
    if (lastDefaultContainer) {
      if (lastDefaultContainer.id == container.id) return lastDefaultContainer;
      lastDefaultContainer.isDefault = false;
      await this.containerRepository.save(lastDefaultContainer);
    }

    container.isDefault = true;
    const savedContainer = await this.containerRepository.save(container);

    this.logger.log({
      method: 'setAsDefault',
      containerId: container.id,
      updatingUser: updatingUser.sub,
    });
    return savedContainer;
  }

  async remove(id: number, removingUser: SerializedUser): Promise<void> {
    this.logger.log({ method: 'removeContainer', id, removingUser });

    const container = await this.findOneById(id, removingUser);
    if (container.partners && container.partners.length > 0) {
      throw new BadRequestException('Pricelist container has assigned partners.');
    }
    await this.containerRepository.delete(id);
  }
}
