// create-job-event.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { Job } from 'src/job/entities/job.entity';

export class CreateJobEventDto {
  @ApiProperty({ description: 'Job object', type: Job })
  @ValidateNested()
  @Type(() => Job)
  job: Job;

  @ApiProperty({ description: 'Previous status of the job' })
  @IsString()
  oldStatus: string;

  @ApiProperty({ description: 'New status of the job' })
  @IsString()
  newStatus: string;

  @ApiProperty({ description: 'ID of the user updating the job status' })
  @IsInt()
  updatingUserId: number;
}
