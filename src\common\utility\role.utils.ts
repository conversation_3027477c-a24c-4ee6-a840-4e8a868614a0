import { Role } from '../constants/roles.enum';

export const isHigherRole = (referenceRole: Role, checkRole: Role) => {
  if (isManager(checkRole)) {
    return true;
  }

  if (isDispatcher(checkRole) && isOperator(referenceRole)) {
    return true;
  }

  return false;
};

export const isManager = (role: Role) => {
  return role === Role.MANAGER;
};

export const isDispatcher = (role: Role) => {
  return role === Role.DISPATCHER;
};

export const isOperator = (role: Role) => {
  return role === Role.OPERATOR;
};
