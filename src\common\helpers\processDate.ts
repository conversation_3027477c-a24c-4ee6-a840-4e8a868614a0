import { BadRequestException } from '@nestjs/common';
import { trimStringAndValidateNotEmpty } from './processString';
import * as moment from 'moment';

export const processDate = (value: any): Date | undefined => {
  if (typeof value === 'string') {
    if (value.trim() === '') {
      return undefined;
    }
    const parsedDate = new Date(value);
    if (!isNaN(parsedDate.getTime())) {
      return parsedDate;
    } else {
      console.warn(`Invalid date string: ${value}`);
      throw new BadRequestException(`Invalid date format: ${value}`);
    }
  }

  if (typeof value === 'number') {
    const parsedDate = new Date(value);
    if (!isNaN(parsedDate.getTime())) {
      return parsedDate;
    } else {
      console.warn(`Invalid numeric timestamp: ${value}`);
      throw new BadRequestException(`Invalid numeric timestamp: ${value}`);
    }
  }

  return undefined;
};

export const turnDateIntoDateString = (
  dateStr?: string | null,
  propName?: string,
): string | null => {
  const validateString = trimStringAndValidateNotEmpty(dateStr, propName);

  if (!validateString) {
    return null;
  }

  if (moment(validateString, 'MM-DD-YYYY').isValid()) {
    const momentDate = moment(validateString, 'MM-DD-YYYY');
    return momentDate.format();
  }

  if (moment(validateString, 'MM/DD/YYYY').isValid()) {
    const momentDate = moment(validateString, 'MM/DD/YYYY');
    return momentDate.format();
  }

  if (moment(validateString, 'YYYY-MM-DD').isValid()) {
    const momentDate = moment(validateString, 'YYYY-MM-DD');
    return momentDate.format();
  }

  if (moment(validateString, 'DD/MM/YYYY').isValid()) {
    const momentDate = moment(validateString, 'DD/MM/YYYY');
    return momentDate.format();
  }

  return validateString;
};
