import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1714688794467 implements MigrationInterface {
  name = 'Migrations1714688794467';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier1"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier2"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier3"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier4"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier5"`);
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "priceForTier6"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier6" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier5" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier4" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier3" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier2" double precision`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "priceForTier1" double precision`);
  }
}
