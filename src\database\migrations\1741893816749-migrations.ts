import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1741893816749 implements MigrationInterface {
  name = 'Migrations1741893816749';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."job_jobtype_enum" AS ENUM('Cementation / Mounting', 'Cleanliness / Bottom of excavation', 'Concrete apron', 'Concrete blocs', 'Filling', 'Floor', 'Foundations', 'Longrine', 'Pole', 'Beam', 'Reinforced concrete wall / panel', 'Roads / Networks / Utilities', 'Screed', 'Slab', 'Unknown')`,
    );
    await queryRunner.query(`ALTER TABLE "job" ADD "jobType" "public"."job_jobtype_enum"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "supplyOfTheChemicalSlushie" boolean`);
    await queryRunner.query(`ALTER TABLE "job" ADD "ciaw" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "ciaw"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "supplyOfTheChemicalSlushie"`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "jobType"`);
    await queryRunner.query(`DROP TYPE "public"."job_jobtype_enum"`);
  }
}
