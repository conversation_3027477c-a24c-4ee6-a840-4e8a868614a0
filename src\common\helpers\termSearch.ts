import { Brackets, SelectQueryBuilder } from 'typeorm';

/**
 * Applies a multi-term search across specified fields using ILIKE.
 * @param qb The TypeORM QueryBuilder instance.
 * @param searchText The search text to be split into terms.
 * @param fields The fields to be searched, each prefixed with its table alias.
 */
export const applySearch = (
  queryBuilder: SelectQueryBuilder<any>,
  searchText: string,
  fields: string[],
) => {
  if (searchText) {
    const terms = searchText.split(/\s+/);

    terms.forEach((term, termIndex) => {
      queryBuilder.andWhere(
        new Brackets(subQb => {
          fields.forEach((field, fieldIndex) => {
            const condition = `${field} ILIKE :${termIndex + 1}`;
            fieldIndex === 0 ? subQb.where(condition) : subQb.orWhere(condition);
          });
        }),
      );
      queryBuilder.setParameter(`${termIndex + 1}`, `%${term}%`);
    });
  }
};
