import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1708472455156 implements MigrationInterface {
  name = 'Migrations1708472455156';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "cleaningThePump"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" ADD "cleaningThePump" text`);
  }
}
