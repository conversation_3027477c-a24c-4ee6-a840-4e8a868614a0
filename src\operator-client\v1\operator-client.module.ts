import { OperatorClientService } from './operator-client.service';
import { OperatorClientController } from './operator-client.controller';
import { Module } from '@nestjs/common';
import { OperatorClient } from '../entities/operator-client.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([OperatorClient])],
  controllers: [OperatorClientController],
  providers: [OperatorClientService],
})
export class OperatorClientModule {}
