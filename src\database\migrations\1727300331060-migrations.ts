import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1727300331060 implements MigrationInterface {
  name = 'Migrations1727300331060';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "birthdate" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "user" ADD "address" character varying`);
    await queryRunner.query(`ALTER TABLE "user" ADD "secondaryAddress" character varying`);
    await queryRunner.query(`ALTER TABLE "user" ADD "phoneNumberPersonal" character varying`);
    await queryRunner.query(`ALTER TABLE "user" ADD "city" character varying`);
    await queryRunner.query(`ALTER TABLE "user" ADD "zipCode" integer`);
    await queryRunner.query(`ALTER TABLE "company" ADD "secondaryAddress" character varying`);
    await queryRunner.query(`ALTER TABLE "company" ADD "vatNumber" integer`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "vatNumber"`);
    await queryRunner.query(`ALTER TABLE "user" ADD "vatNumber" integer`);
    await queryRunner.query(
      `ALTER TYPE "public"."company_type_enum" RENAME TO "company_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."company_type_enum" AS ENUM('SA', 'SRL', 'SC', 'SNC', 'SComm')`,
    );
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "type" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "type" TYPE "public"."company_type_enum" USING "type"::"text"::"public"."company_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."company_type_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."company_type_enum_old" AS ENUM('1', '2', '3')`);
    await queryRunner.query(
      `ALTER TABLE "company" ALTER COLUMN "type" TYPE "public"."company_type_enum_old" USING "type"::"text"::"public"."company_type_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "company" ALTER COLUMN "type" SET DEFAULT '2'`);
    await queryRunner.query(`DROP TYPE "public"."company_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."company_type_enum_old" RENAME TO "company_type_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "vatNumber"`);
    await queryRunner.query(`ALTER TABLE "user" ADD "vatNumber" character varying`);
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "vatNumber"`);
    await queryRunner.query(`ALTER TABLE "company" DROP COLUMN "secondaryAddress"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "zipCode"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "city"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "phoneNumberPersonal"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "secondaryAddress"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "address"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "birthdate"`);
  }
}
