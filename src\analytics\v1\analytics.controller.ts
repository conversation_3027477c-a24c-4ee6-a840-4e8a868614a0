import { Controller, Get, Query } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { ApiTags } from '@nestjs/swagger';
import { AverageM3PerCompanyDto } from './dto/average-m3-per-company.dto';
import { FrequentlyUsedVehiclesDto } from './dto/frequently-used-vehicles.dto';
import { AnalyticsBaseDto } from './dto/analytics-base.dto';
import { MostActiveContractorsDto } from './dto/most-active-contractors.dto';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';

@ApiTags('Analytics')
@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('average-m3-per-job')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getAverageM3PerJob(@Query() query: AnalyticsBaseDto) {
    return this.analyticsService.getAverageM3PerJob(query);
  }

  @Get('average-m3-per-hour-company')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getAverageM3PerHourCompany(@Query() query: AverageM3PerCompanyDto) {
    return this.analyticsService.getAverageM3PerCompany(query);
  }

  @Get('average-m3-per-hour')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getAverageM3PerHour(@Query() query: AnalyticsBaseDto) {
    return this.analyticsService.getAverageM3PerHour(query);
  }

  @Get('average-job-completion-time')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getAverageJobCompletionTime(@Query() query: AnalyticsBaseDto) {
    return this.analyticsService.getAverageJobCompletionTime(query);
  }

  @Get('average-reservations-per-dispatcher')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getAverageReservationsPerDispatcher(@Query() query: AnalyticsBaseDto) {
    return this.analyticsService.getAverageReservationsPerDispatcher(query);
  }

  @Get('total-reservations-per-month')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getTotalReservationsPerMonth(@Query() query: AnalyticsBaseDto) {
    return this.analyticsService.getTotalReservationsPerMonth(query);
  }

  @Get('total-expenses-per-month')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getTotalExpensesPerMonth(@Query() query: AnalyticsBaseDto) {
    return this.analyticsService.getTotalExpensesPerMonth(query);
  }

  @Get('frequently-used-vehicles')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getFrequentlyUsedVehicles(@Query() query: FrequentlyUsedVehiclesDto) {
    return this.analyticsService.getFrequentlyUsedVehicles(query);
  }

  @Get('most-active-contractors')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getMostActiveContractors(@Query() query: MostActiveContractorsDto) {
    return this.analyticsService.getMostActiveContractors(query);
  }

  @Get('highest-expenses-per-pumping-company')
  @Roles([{ role: Role.DISPATCHER_MANAGER }, { role: Role.OPERATOR_MANAGER }])
  async getHighestExpensesPerPumpingCompany(@Query() query: AnalyticsBaseDto) {
    return this.analyticsService.getHighestExpensesPerPumpingCompany(query);
  }
}
