import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1709743091724 implements MigrationInterface {
  name = 'Migrations1709743091724';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_849e0cc4d98a094f3b21a01578"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_849e0cc4d98a094f3b21a01578" ON "vehicle" ("licensePlateNumber") `,
    );
  }
}
