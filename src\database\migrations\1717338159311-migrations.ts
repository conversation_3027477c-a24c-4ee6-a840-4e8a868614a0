import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1717338159311 implements MigrationInterface {
  name = 'Migrations1717338159311';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_346103d35cbd1a48521384a81b4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_9d9b187fe55ae269d29492eec1f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ALTER COLUMN "dispatcherManagerId" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "operatorManagerId" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "pricelistId" SET NOT NULL`);
    await queryRunner.query(
      `CREATE INDEX "IDX_346103d35cbd1a48521384a81b" ON "contract" ("dispatcherManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9d9b187fe55ae269d29492eec1" ON "contract" ("operatorManagerId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_346103d35cbd1a48521384a81b4" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_9d9b187fe55ae269d29492eec1f" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_ea78f3aadec5197454998ca2ee5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_fa43f7b3050b011dd02fc5aa2b3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ALTER COLUMN "dispatcherManagerId" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "favorite" ALTER COLUMN "operatorManagerId" SET NOT NULL`);
    await queryRunner.query(
      `CREATE INDEX "IDX_ea78f3aadec5197454998ca2ee" ON "favorite" ("dispatcherManagerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fa43f7b3050b011dd02fc5aa2b" ON "favorite" ("operatorManagerId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_ea78f3aadec5197454998ca2ee5" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_fa43f7b3050b011dd02fc5aa2b3" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_fa43f7b3050b011dd02fc5aa2b3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" DROP CONSTRAINT "FK_ea78f3aadec5197454998ca2ee5"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_fa43f7b3050b011dd02fc5aa2b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ea78f3aadec5197454998ca2ee"`);
    await queryRunner.query(
      `ALTER TABLE "favorite" ALTER COLUMN "operatorManagerId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ALTER COLUMN "dispatcherManagerId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_fa43f7b3050b011dd02fc5aa2b3" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "favorite" ADD CONSTRAINT "FK_ea78f3aadec5197454998ca2ee5" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_9d9b187fe55ae269d29492eec1f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" DROP CONSTRAINT "FK_346103d35cbd1a48521384a81b4"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_9d9b187fe55ae269d29492eec1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_346103d35cbd1a48521384a81b"`);
    await queryRunner.query(`ALTER TABLE "contract" ALTER COLUMN "pricelistId" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "contract" ALTER COLUMN "operatorManagerId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ALTER COLUMN "dispatcherManagerId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_4fbfcf55a7ebb63296054bf9659" FOREIGN KEY ("pricelistId") REFERENCES "pricelist"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_9d9b187fe55ae269d29492eec1f" FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "contract" ADD CONSTRAINT "FK_346103d35cbd1a48521384a81b4" FOREIGN KEY ("dispatcherManagerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
