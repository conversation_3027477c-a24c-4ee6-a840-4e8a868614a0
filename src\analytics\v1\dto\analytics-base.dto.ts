import { IsInt, IsISO8601, <PERSON><PERSON><PERSON><PERSON>, Validate } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { IsStartDateBeforeOrEqualEndDate } from '../validators/is-start-date-before-or-equal-end-date.validator';
import { Type } from 'class-transformer';

export class AnalyticsBaseDto {
  @ApiProperty({ description: 'Dispatcher company ID', required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  dispatcherCompanyId?: number;

  @ApiProperty({ description: 'Operator company ID', required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  operatorCompanyId?: number;

  @ApiProperty({ required: true, description: 'Start date in ISO 8601 format' })
  @IsISO8601()
  @Validate(IsStartDateBeforeOrEqualEndDate)
  startDate: string;

  @ApiProperty({ required: true, description: 'End date in ISO 8601 format' })
  @IsISO8601()
  endDate: string;
}
