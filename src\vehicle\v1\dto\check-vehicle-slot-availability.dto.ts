import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsISO8601, IsNumber, IsOptional } from 'class-validator';
import { processNumber } from 'src/common/helpers/processNumbers';

export class CheckVehicleSlotAvailabilityDto {
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  id: number;

  @ApiProperty()
  @IsISO8601()
  dateFrom: Date;

  @ApiProperty()
  @IsISO8601()
  @Transform(({ value }) => processNumber(value))
  dateTo: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Transform(({ value }) => processNumber(value))
  currentReservationId?: number | null;
}
