import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsInt, IsISO8601, IsOptional } from 'class-validator';
import { Priority, Status } from 'src/common/constants/task.enum';
import { processNumber } from 'src/common/helpers/processNumbers';
import { trimStringAndValidateNotEmpty } from 'src/common/helpers/processString';

export class CreateTaskDto {
  @ApiProperty({ required: false })
  @IsInt()
  @IsOptional()
  labelId?: number;

  @ApiProperty({ enum: Priority, required: true })
  @IsEnum(Priority)
  priority: Priority;

  @ApiProperty({ enum: Status, required: true })
  @IsEnum(Status)
  status: Status;

  @ApiProperty({ required: true })
  @Transform(({ value }) => processNumber(value))
  assigneeId: number;

  @ApiProperty({ required: false })
  @IsISO8601()
  dueDate?: Date;

  @ApiProperty({ required: true })
  @Transform(({ value }) => trimStringAndValidateNotEmpty(value, 'description'))
  description: string;

  @ApiProperty({ required: false })
  @IsISO8601()
  @IsOptional()
  startDate?: Date;
}
