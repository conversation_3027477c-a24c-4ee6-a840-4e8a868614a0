import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1739556119471 implements MigrationInterface {
  name = 'Migrations1739556119471';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."vehicle_type_enum" AS ENUM('Pump', 'City Pump', 'Mixo Pump', 'Mixer', 'Stationary Pump')`,
    );
    await queryRunner.query(`ALTER TABLE "vehicle" ADD "type" "public"."vehicle_type_enum"`);

    await queryRunner.query(
      `UPDATE "vehicle"
      SET "type" = (
        SELECT
          CASE
            WHEN "vehicle_type"."name" ILIKE '% pump' THEN initcap("vehicle_type"."name")
            ELSE "vehicle_type"."name"
          END
        FROM "vehicle_type"
        WHERE "vehicle_type"."id" = "vehicle"."vehicleTypeId"
      )::public.vehicle_type_enum`,
    );

    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_be2f02be96355e6412f5efd7380"`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" DROP CONSTRAINT "FK_f2787f31f717e0a77fdb16e5ea0"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_6138d71264f56b4ff2355bc455"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_be2f02be96355e6412f5efd738"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f2787f31f717e0a77fdb16e5ea"`);
    await queryRunner.query(
      `UPDATE "vehicle"
      SET "boomSize" = (SELECT "boomSize" FROM "vehicle_size" WHERE "vehicle_size"."id" = "vehicle"."vehicleSizeId")`,
    );

    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "vehicleSizeId"`);
    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "vehicleTypeId"`);

    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "FK_6138d71264f56b4ff2355bc455c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" DROP CONSTRAINT "UQ_7cafb96380bf55d72ec621d3f7c"`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" DROP COLUMN "vehicleSizeId"`);
    await queryRunner.query(`ALTER TABLE "pricelist" ADD COLUMN "vehicleId" integer`);
    await queryRunner.query(
      `CREATE INDEX "IDX_f93cabad9331a241046f26aefe" ON "pricelist" ("vehicleId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "UQ_ca64c216528e5eeda194bb5358e" UNIQUE ("vehicleId", "parentId")`,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_f93cabad9331a241046f26aefe5" FOREIGN KEY ("vehicleId") REFERENCES "vehicle"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );

    await queryRunner.query(`DROP TABLE IF EXISTS "vehicle_size"`);
    await queryRunner.query(`DROP TABLE IF EXISTS "vehicle_type"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate the old tables
    await queryRunner.query(`CREATE TABLE "vehicle_size" (
            "id" SERIAL PRIMARY KEY,
            "boomSize" integer,
            "operatorManagerId" integer,
            "vehicleTypeId" integer,
            FOREIGN KEY ("vehicleTypeId") REFERENCES "vehicle_type"("id") ON DELETE SET NULL,
            FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE CASCADE
          )`);
    await queryRunner.query(`CREATE TABLE "vehicle_type" (
            "id" SERIAL PRIMARY KEY,
            "name" varchar,
            "operatorManagerId" integer,
            FOREIGN KEY ("operatorManagerId") REFERENCES "user"("id") ON DELETE CASCADE
          )`);

    await queryRunner.query(`ALTER TABLE "vehicle" ADD "vehicleSizeId" integer`);
    await queryRunner.query(
      `CREATE INDEX "IDX_be2f02be96355e6412f5efd738" ON "vehicle" ("vehicleSizeId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f2787f31f717e0a77fdb16e5ea" ON "vehicle" ("vehicleTypeId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_f2787f31f717e0a77fdb16e5ea0" FOREIGN KEY ("vehicleTypeId") REFERENCES "vehicle_type"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "vehicle" ADD CONSTRAINT "FK_be2f02be96355e6412f5efd7380" FOREIGN KEY ("vehicleSizeId") REFERENCES "vehicle_size"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "pricelist" ADD "vehicleSizeId" integer`);

    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "UQ_7cafb96380bf55d72ec621d3f7c" UNIQUE ("parentId", "vehicleSizeId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6138d71264f56b4ff2355bc455" ON "pricelist" ("vehicleSizeId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "pricelist" ADD CONSTRAINT "FK_6138d71264f56b4ff2355bc455c" FOREIGN KEY ("vehicleSizeId") REFERENCES "vehicle_size"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );

    await queryRunner.query(`ALTER TABLE "vehicle" DROP COLUMN "type"`);
    await queryRunner.query(`DROP TYPE "public"."vehicle_type_enum"`);
  }
}
