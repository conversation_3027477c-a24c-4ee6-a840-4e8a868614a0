import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsInt, IsNumber, Min } from 'class-validator';
import { AnalyticsBaseDto } from './analytics-base.dto';

export class MostActiveContractorsDto extends AnalyticsBaseDto {
  @ApiPropertyOptional({ description: 'Limit the number of results returned' })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  limit?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  readonly offset?: number = 0;
}
