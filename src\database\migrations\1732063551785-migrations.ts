import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1732063551785 implements MigrationInterface {
  name = 'Migrations1732063551785';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "task_label" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "created_by" integer, "name" character varying NOT NULL, "color" character varying NOT NULL, "companyId" integer NOT NULL, CONSTRAINT "UQ_57dbce11751dbf3a8149f4b30a8" UNIQUE ("companyId", "color"), CONSTRAINT "PK_fb2322fb12d4db26386caeff6ee" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4f06070cf0e191985ae41891be" ON "task_label" ("companyId") `,
    );
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "label"`);
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "labelColor"`);
    await queryRunner.query(`ALTER TABLE "task" ADD "labelId" integer`);
    await queryRunner.query(
      `ALTER TYPE "public"."task_activity_actiontype_enum" RENAME TO "task_activity_actiontype_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."task_activity_actiontype_enum" AS ENUM('StatusUpdate', 'AssigneeChange', 'PriorityUpdate', 'LabelChange')`,
    );
    await queryRunner.query(
      `ALTER TABLE "task_activity" ALTER COLUMN "actionType" TYPE "public"."task_activity_actiontype_enum" USING "actionType"::"text"::"public"."task_activity_actiontype_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."task_activity_actiontype_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "task_label" ADD CONSTRAINT "FK_4f06070cf0e191985ae41891be8" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "task" ADD CONSTRAINT "FK_525a4e2e6e90b8883fb688628cf" FOREIGN KEY ("labelId") REFERENCES "task_label"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "task" DROP CONSTRAINT "FK_525a4e2e6e90b8883fb688628cf"`);
    await queryRunner.query(
      `ALTER TABLE "task_label" DROP CONSTRAINT "FK_4f06070cf0e191985ae41891be8"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."task_activity_actiontype_enum_old" AS ENUM('StatusUpdate', 'AssigneeChange', 'PriorityUpdate')`,
    );
    await queryRunner.query(
      `ALTER TABLE "task_activity" ALTER COLUMN "actionType" TYPE "public"."task_activity_actiontype_enum_old" USING "actionType"::"text"::"public"."task_activity_actiontype_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."task_activity_actiontype_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."task_activity_actiontype_enum_old" RENAME TO "task_activity_actiontype_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "task" DROP COLUMN "labelId"`);
    await queryRunner.query(`ALTER TABLE "task" ADD "labelColor" character varying`);
    await queryRunner.query(`ALTER TABLE "task" ADD "label" character varying`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4f06070cf0e191985ae41891be"`);
    await queryRunner.query(`DROP TABLE "task_label"`);
  }
}
