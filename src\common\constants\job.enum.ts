export enum JobState {
  RUNNING = 'RUNNING',
  SUCCESS = 'SUCCESS',
  FAILURE = 'FAILURE',
  CANCELLED = 'CANCELLED',
  WAITING = 'WAITING',
}

export enum JobStatus {
  NOT_STARTED = 'NOT_STARTED',
  DRIVING_TO_SITE = 'DRIVING_TO_SITE',
  SITE_ARRIVAL = 'SITE_ARRIVAL',
  SECURITY_VALIDATION = 'SECURITY_VALIDATION',
  START_SETUP = 'START_SETUP',
  END_SETUP = 'END_SETUP',
  START_PUMPING = 'START_PUMPING',
  END_PUMPING = 'END_PUMPING',
  REPORT = 'REPORT',
  SIGNATURE = 'SIGNATURE',
  START_CLEANUP = 'START_CLEANUP',
  END_CLEANUP = 'END_CLEANUP',
  LEAVE_SITE = 'LEAVE_SITE',
  COMPLETE = 'COMPLETE',
  CANCELLED = 'CANCELLED',
}

export enum ParkingOn {
  PUBLIC_ROAD = 'Public Road',
  PRIVATE_ROAD = 'Private Road',
  WORKSITE = 'Worksite',
}

export enum TerrainStability {
  NATURAL_SOIL = 'Natural soil',
  BACKFILL = 'Backfill',
  BURIED_NETWORK = 'Buried network',
}

export enum Cleaning {
  WORKSITE = 'Worksite',
  CENTRAL = 'Central',
}

export enum JobType {
  CEMENTATION_MOUNTING = 'Cementation / Mounting',
  CLEANLINESSS_BOTTOM_OF_EXCAVATION = 'Cleanliness / Bottom of excavation',
  CONCRETE_APRON = 'Concrete apron',
  CONCRETE_BLOCS = 'Concrete blocs',
  FILLING = 'Filling',
  FLOOR = 'Floor',
  FOUNDATIONS = 'Foundations',
  LONGRINE = 'Longrine',
  POLE = 'Pole',
  BEAM = 'Beam',
  REINFORCED_CONCRETE_WALL_PANEL = 'Reinforced concrete wall / panel',
  ROADS_NETWORKS_UTILITIES = 'Roads / Networks / Utilities',
  SCREED = 'Screed',
  SLAB = 'Slab',
  UNKNOWN = 'Unknown',
}
