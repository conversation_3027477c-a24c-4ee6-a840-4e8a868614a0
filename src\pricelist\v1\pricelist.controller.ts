import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { PricelistService } from './pricelist.service';
import { CreatePricelistDto } from './dto/create-pricelist.dto';
import { UpdatePricelistDto } from './dto/update-pricelist.dto';
import { ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/common/decorators/roles.decorator';
import { Role } from 'src/common/constants/roles.enum';
import { GetPricelistsDto } from './dto/get-pricelists.dto';
import { CreateContainerPricelistsDto } from './dto/create-container-pricelists.dto';
import { UpdateContainerPricelistsDto } from './dto/update-container-pricelists.dto';
import { ContainerPricelistsService } from './container-pricelists.service';
import { GetContainersDto } from './dto/get-container-pricelists.dto';

@ApiTags('Pricelists')
@Controller({ path: 'pricelist', version: VERSION_NEUTRAL })
export class PricelistController {
  constructor(
    private readonly pricelistService: PricelistService,
    private readonly containerPricelistService: ContainerPricelistsService,
  ) {}

  @Post()
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  create(@Body() createPricelistDto: CreatePricelistDto, @Req() req) {
    return this.pricelistService.create(createPricelistDto, req?.user?.sub);
  }

  @Post('containers')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  createContainer(@Body() createContainerPricelistsDto: CreateContainerPricelistsDto, @Req() req) {
    return this.containerPricelistService.create(createContainerPricelistsDto, req?.user);
  }

  @Post('get')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  findMany(@Body() body: GetPricelistsDto, @Req() req) {
    return this.pricelistService.findMany(body, req?.user);
  }

  @Post('containers/get')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  findManyContainers(@Body() body: GetContainersDto, @Req() req) {
    return this.containerPricelistService.findMany(body, req?.user);
  }

  @Get(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  findOneById(@Param('id') id: string, @Req() req) {
    return this.pricelistService.findOneById(+id, req?.user);
  }

  @Get('containers/:id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  findOneByIdContainers(@Param('id') id: string, @Req() req) {
    return this.containerPricelistService.findOneById(+id, req?.user);
  }

  @Get('globally/:id')
  @Roles([{ role: Role.OPERATOR_MANAGER }, { role: Role.DISPATCHER_MANAGER }])
  findOneGlobally(@Param('id') id: string) {
    return this.pricelistService.findOneGlobally(+id);
  }

  @Patch(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  update(@Param('id') id: string, @Body() updatePricelistDto: UpdatePricelistDto, @Req() req) {
    return this.pricelistService.update(+id, updatePricelistDto, req?.user?.sub);
  }

  @Patch('containers/:id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  updateContainer(
    @Param('id') id: string,
    @Body() updateContainertDto: UpdateContainerPricelistsDto,
    @Req() req,
  ) {
    return this.containerPricelistService.update(+id, updateContainertDto, req?.user);
  }

  @Patch('containers/:id/default')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  setAsDefault(@Param('id') id: string, @Req() req) {
    return this.containerPricelistService.setAsDefault(+id, req?.user);
  }

  @Delete(':id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  remove(@Param('id') id: string, @Req() req) {
    return this.pricelistService.remove(+id, req?.user);
  }

  @Delete('containers/:id')
  @Roles([{ role: Role.OPERATOR_MANAGER }])
  removeContainer(@Param('id') id: string, @Req() req) {
    return this.containerPricelistService.remove(+id, req?.user);
  }
}
