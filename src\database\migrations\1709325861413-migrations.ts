import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1709325861413 implements MigrationInterface {
  name = 'Migrations1709325861413';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "localAdministrationAuthorization"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "localAdministrationAuthorization" boolean`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "trafficPlan"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "trafficPlan" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "trafficPlan"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "trafficPlan" text`);
    await queryRunner.query(`ALTER TABLE "job" DROP COLUMN "localAdministrationAuthorization"`);
    await queryRunner.query(`ALTER TABLE "job" ADD "localAdministrationAuthorization" text`);
  }
}
