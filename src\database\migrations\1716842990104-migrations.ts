import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1716842990104 implements MigrationInterface {
  name = 'Migrations1716842990104';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP INDEX IF EXISTS public."IDX_41a0f9931a4a994be6443f8298";
            DROP INDEX IF EXISTS public."IDX_7642988648d8d6c3f0e6eef5fa";
            DROP TABLE IF EXISTS public.pricelist_dispatcher_managers_user;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE IF EXISTS public.pricelist_dispatcher_managers_user;

            CREATE TABLE IF NOT EXISTS public.pricelist_dispatcher_managers_user (
                "pricelistId" integer NOT NULL,
                "userId" integer NOT NULL,
                CONSTRAINT "PK_cdedcf425236900ee04cd8baa6e" PRIMARY KEY ("pricelistId", "userId"),
                CONSTRAINT "FK_41a0f9931a4a994be6443f82986" FOREIGN KEY ("userId")
                    REFERENCES public."user" (id) MATCH SIMPLE
                    ON UPDATE NO ACTION
                    ON DELETE NO ACTION,
                CONSTRAINT "FK_7642988648d8d6c3f0e6eef5fac" FOREIGN KEY ("pricelistId")
                    REFERENCES public.pricelist (id) MATCH SIMPLE
                    ON UPDATE CASCADE
                    ON DELETE CASCADE
            );

            ALTER TABLE IF EXISTS public.pricelist_dispatcher_managers_user
                OWNER to postgres;

            CREATE INDEX IF NOT EXISTS "IDX_41a0f9931a4a994be6443f8298"
                ON public.pricelist_dispatcher_managers_user USING btree
                ("userId" ASC NULLS LAST)
                TABLESPACE pg_default;

            CREATE INDEX IF NOT EXISTS "IDX_7642988648d8d6c3f0e6eef5fa"
                ON public.pricelist_dispatcher_managers_user USING btree
                ("pricelistId" ASC NULLS LAST)
                TABLESPACE pg_default;
        `);
  }
}
